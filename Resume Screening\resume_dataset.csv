Category,Resume
Data Science,"Skills * Programming Languages: Python (pandas, numpy, scipy, scikit-learn, matplotlib), Sql, Java, JavaScript/JQuery. * Machine learning: Regression, SVM, NaÃ¯ve Bayes, KNN, Random Forest, Decision Trees, Boosting techniques, Cluster Analysis, Word Embedding, Sentiment Analysis, Natural Language processing, Dimensionality reduction, Topic Modelling (LDA, NMF), PCA & Neural Nets. * Database Visualizations: Mysql, SqlServer, Cassandra, Hbase, ElasticSearch D3.js, DC.js, Plotly, kibana, matplotlib, ggplot, Tableau. * Others: Regular Expression, HTML, CSS, Angular 6, Logstash, Kafka, Python Flask, Git, Docker, computer vision - Open CV and understanding of Deep learning.Education Details 
Data Science Assurance AssociateData Science Assurance Associate - Ernst & Young LLPSkill Details 
JAVASCRIPT- Exprience - 24 months
jQuery- Exprience - 24 months
Python- Exprience - 24 monthsCompany Details 
company - Ernst & Young LLP
description - Fraud Investigations and Dispute Services   Assurance
TECHNOLOGY ASSISTED REVIEW
TAR (Technology Assisted Review) assists in accelerating the review process and run analytics and generate reports.
* Core member of a team helped in developing automated review platform tool from scratch for assisting E discovery domain, this tool implements predictive coding and topic modelling by automating reviews, resulting in reduced labor costs and time spent during the lawyers review.
* Understand the end to end flow of the solution, doing research and development for classification models, predictive analysis and mining of the information present in text data. Worked on analyzing the outputs and precision monitoring for the entire tool.
* TAR assists in predictive coding, topic modelling from the evidence by following EY standards. Developed the classifier models in order to identify ""red flags"" and fraud-related issues.

Tools & Technologies: Python, scikit-learn, tfidf, word2vec, doc2vec, cosine similarity, NaÃ¯ve Bayes, LDA, NMF for topic modelling, Vader and text blob for sentiment analysis. Matplot lib, Tableau dashboard for reporting.

MULTIPLE DATA SCIENCE AND ANALYTIC PROJECTS (USA CLIENTS)
TEXT ANALYTICS - MOTOR VEHICLE CUSTOMER REVIEW DATA * Received customer feedback survey data for past one year. Performed sentiment (Positive, Negative & Neutral) and time series analysis on customer comments across all 4 categories.
* Created heat map of terms by survey category based on frequency of words * Extracted Positive and Negative words across all the Survey categories and plotted Word cloud.
* Created customized tableau dashboards for effective reporting and visualizations.
CHATBOT * Developed a user friendly chatbot for one of our Products which handle simple questions about hours of operation, reservation options and so on.
* This chat bot serves entire product related questions. Giving overview of tool via QA platform and also give recommendation responses so that user question to build chain of relevant answer.
* This too has intelligence to build the pipeline of questions as per user requirement and asks the relevant /recommended questions.

Tools & Technologies: Python, Natural language processing, NLTK, spacy, topic modelling, Sentiment analysis, Word Embedding, scikit-learn, JavaScript/JQuery, SqlServer

INFORMATION GOVERNANCE
Organizations to make informed decisions about all of the information they store. The integrated Information Governance portfolio synthesizes intelligence across unstructured data sources and facilitates action to ensure organizations are best positioned to counter information risk.
* Scan data from multiple sources of formats and parse different file formats, extract Meta data information, push results for indexing elastic search and created customized, interactive dashboards using kibana.
* Preforming ROT Analysis on the data which give information of data which helps identify content that is either Redundant, Outdated, or Trivial.
* Preforming full-text search analysis on elastic search with predefined methods which can tag as (PII) personally identifiable information (social security numbers, addresses, names, etc.) which frequently targeted during cyber-attacks.
Tools & Technologies: Python, Flask, Elastic Search, Kibana

FRAUD ANALYTIC PLATFORM
Fraud Analytics and investigative platform to review all red flag cases.
â¢ FAP is a Fraud Analytics and investigative platform with inbuilt case manager and suite of Analytics for various ERP systems.
* It can be used by clients to interrogate their Accounting systems for identifying the anomalies which can be indicators of fraud by running advanced analytics
Tools & Technologies: HTML, JavaScript, SqlServer, JQuery, CSS, Bootstrap, Node.js, D3.js, DC.js"
Data Science,"Education Details 
May 2013 to May 2017 B.E   UIT-RGPVData ScientistData Scientist - MatelabsSkill Details 
Python- Exprience - Less than 1 year months
Statsmodels- Exprience - 12 months
AWS- Exprience - Less than 1 year months
Machine learning- Exprience - Less than 1 year months
Sklearn- Exprience - Less than 1 year months
Scipy- Exprience - Less than 1 year months
Keras- Exprience - Less than 1 year monthsCompany Details 
company - Matelabs
description - ML Platform for business professionals, dummies and enthusiasts.
60/A Koramangala 5th block,
Achievements/Tasks behind sukh sagar, Bengaluru,
India                               Developed and deployed auto preprocessing steps of machine learning mainly missing value
treatment, outlier detection, encoding, scaling, feature selection and dimensionality reduction.
Deployed automated classification and regression model.
linkedin.com/in/aditya-rathore-
b4600b146                           Reasearch and deployed the time series forecasting model ARIMA, SARIMAX, Holt-winter and
Prophet.
Worked on meta-feature extracting problem.
github.com/rathorology
Implemented a state of the art research paper on outlier detection for mixed attributes.
company - Matelabs
description - "
Data Science,"Areas of Interest Deep Learning, Control System Design, Programming in-Python, Electric Machinery, Web Development, Analytics Technical Activities q Hindustan Aeronautics Limited, Bangalore - For 4 weeks under the guidance of Mr. Satish, Senior Engineer in the hangar of Mirage 2000 fighter aircraft Technical Skills Programming Matlab, Python and Java, LabView, Python WebFrameWork-Django, Flask, LTSPICE-intermediate Languages and and MIPOWER-intermediate, Github (GitBash), Jupyter Notebook, Xampp, MySQL-Basics, Python Software Packages Interpreters-Anaconda, Python2, Python3, Pycharm, Java IDE-Eclipse Operating Systems Windows, Ubuntu, Debian-Kali Linux Education Details 
January 2019 B.Tech. Electrical and Electronics Engineering  Manipal Institute of Technology
January 2015    DEEKSHA CENTER
January 2013    Little Flower Public School
August 2000    Manipal Academy of HigherDATA SCIENCEDATA SCIENCE AND ELECTRICAL ENTHUSIASTSkill Details 
Data Analysis- Exprience - Less than 1 year months
excel- Exprience - Less than 1 year months
Machine Learning- Exprience - Less than 1 year months
mathematics- Exprience - Less than 1 year months
Python- Exprience - Less than 1 year months
Matlab- Exprience - Less than 1 year months
Electrical Engineering- Exprience - Less than 1 year months
Sql- Exprience - Less than 1 year monthsCompany Details 
company - THEMATHCOMPANY
description - I am currently working with a Casino based operator(name not to be disclosed) in Macau.I need to segment the customers who visit their property based on the value the patrons bring into the company.Basically prove that the segmentation can be done in much better way than the current system which they have with proper numbers to back it up.Henceforth they can implement target marketing strategy to attract their customers who add value to the business."
Data Science,"Skills â¢ R â¢ Python â¢ SAP HANA â¢ Tableau â¢ SAP HANA SQL â¢ SAP HANA PAL â¢ MS SQL â¢ SAP Lumira â¢ C# â¢ Linear Programming â¢ Data Modelling â¢ Advance Analytics â¢ SCM Analytics â¢ Retail Analytics â¢Social Media Analytics â¢ NLP Education Details 
January 2017 to January 2018 PGDM Business Analytics  Great Lakes Institute of Management & Illinois Institute of Technology
January 2013 Bachelor of Engineering Electronics and Communication Bengaluru, Karnataka New Horizon College of Engineering, Bangalore Visvesvaraya Technological UniversityData Science ConsultantConsultant - Deloitte USISkill Details 
LINEAR PROGRAMMING- Exprience - 6 months
RETAIL- Exprience - 6 months
RETAIL MARKETING- Exprience - 6 months
SCM- Exprience - 6 months
SQL- Exprience - Less than 1 year months
Deep Learning- Exprience - Less than 1 year months
Machine learning- Exprience - Less than 1 year months
Python- Exprience - Less than 1 year months
R- Exprience - Less than 1 year monthsCompany Details 
company - Deloitte USI
description - The project involved analysing historic deals and coming with insights to optimize future deals.
Role: Was given raw data, carried out end to end analysis and presented insights to client.
Key Responsibilities:
â¢ Extract data from client systems across geographies.
â¢ Understand and build reports in tableau. Infer meaningful insights to optimize prices and find out process blockades.
Technical Environment: R, Tableau.

Industry: Cross Industry
Service Area: Cross Industry - Products
Project Name: Handwriting recognition
Consultant: 3 months.
The project involved taking handwritten images and converting them to digital text images by object detection and sentence creation.
Role: I was developing sentence correction functionality.
Key Responsibilities:
â¢ Gather data large enough to capture all English words
â¢ Train LSTM models on words.
Technical Environment: Python.

Industry: Finance
Service Area: Financial Services - BI development Project Name: SWIFT
Consultant: 8 months.
The project was to develop an analytics infrastructure on top of SAP S/4, it would user to view
financial reports to respective departments. Reporting also included forecasting expenses.
Role: I was leading the offshore team.
Key Responsibilities:
â¢ Design & Develop data models for reporting.
â¢ Develop ETL for data flow
â¢ Validate various reports.
Technical Environment: SAP HANA, Tableau, SAP AO.

Industry: Healthcare Analytics
Service Area: Life Sciences - Product development Project Name: Clinical Healthcare System
Consultant: 2 months.
The project was to develop an analytics infrastructure on top of Argus, it would allow users to query faster and provide advance analytics capabilities.
Role: I was involved from design to deploy phase, performed a lot of data restructuring and built
models for insights.
Key Responsibilities:
â¢ Design & Develop data models for reporting.
â¢ Develop and deploy analytical models.
â¢ Validate various reports.
Technical Environment: Data Modelling, SAP HANA, Tableau, NLP.

Industry: FMCG
Service Area: Trade & Promotion
Project Name: Consumption Based Planning for Flowers Foods Consultant; 8 months.
The project involved setting up of CRM and CBP modules.
Role: I was involved in key data decomposition activities and setting up the base for future year
forecast. Over the course of the project I developed various models and carried out key
performance improvements.
Key Responsibilities:
â¢ Design & Develop HANA models for decomposition.
â¢ Develop data flow for forecast.
â¢ Developed various views for reporting of Customer/Sales/Funds.
â¢ Validate various reports in BOBJ.
Technical Environment: Data Modelling, SAP HANA, BOBJ, Time Series Forecasting.

Internal Initiative Industry: FMCG
Customer Segmentation and RFM analysis Consultant; 3 months.
The initiative involved setting up of HANA-Python interface and advance analytics on Python. Over the course I had successfully segmented data into five core segments using K-means and carried out RFM analysis in Python. Also developed algorithm to categorize any new customer under the defined buckets.
Technical Environment: Anaconda3, Python3.6, HANA SPS12

Industry: Telecom Invoice state detection Consultant; 1 months.
The initiative was to reduce the manual effort in verifying closed and open invoices manually, it
involved development to a decision tree to classify open/closed invoices. This enabled effort
reduction by 60%.
Technical Environment: R, SAP PAL, SAP HANA SPS12

Accenture Experience
Industry: Analytics - Cross Industry
In Process Analytics for SAP Senior Developer; 19 months.
Accenture Solutions Pvt. Ltd., India
The project involved development of SAP analytics tool - In Process Analytics (IPA) . My role was to develop database objects and data models to provide operational insights to clients.
Role: I have developed various Finance related KPIs and spearheaded various deployments.
Introduced SAP Predictive analytics to reduce development time and reuse functionalities for KPIs and prepared production planning reports.
Key Responsibilities:
â¢ Involved in information gather phase.
â¢ Designed and implemented SAP HANA data modelling using Attribute View, Analytic View, and
Calculation View.
â¢ Developed various KPI's individually using complex SQL scripts in Calculation views.
â¢ Created procedures in HANA Database.
â¢ Took ownership and developed Dashboard functionality.
â¢ Involved in building data processing algorithms to be executed in R server for cluster analysis.
Technical Environment: R, SAP HANA, T-SQL.
Industry: Cross Industry
Accenture Testing Accelerator for SAP Database Developer; 21 months.
Accenture Solutions Pvt. Ltd., India
Role: I have taken care of all development activities for the ATAS tool and have also completed
various deployments of the product.
Apart from these activities I was also actively involved in maintenance of the database servers
(Production & Quality)
Key Responsibilities:
â¢ Analyzing business requirements, understanding the scope, getting requirements clarified
interacting with business and further transform all requirements to generate attribute
mapping documents and reviewing mapping specification documentation
â¢ Create / Update database objects like tables, views, stored procedures, function, and packages
â¢ Monitored SQL Server Error Logs and Application Logs through SQL Server Agent
â¢ Prepared Data Flow Diagrams, Entity Relationship Diagrams using UML
â¢ Responsible for Designing, developing and Normalization of database tables
â¢ Experience in performance tuning using SQL profiler.
â¢ Involved in QA, UAT, knowledge transfer and support activities
Technical Environment: SQL Server 2008/2014, Visual Studio 2010, Windows Server, Performance
Monitor, SQL Server Profiler, C#, PL-SQL, T-SQL."
Data Science,"Education Details 
 MCA   YMCAUST,  Faridabad,  HaryanaData Science internshipSkill Details 
Data Structure- Exprience - Less than 1 year months
C- Exprience - Less than 1 year months
Data Analysis- Exprience - Less than 1 year months
Python- Exprience - Less than 1 year months
Core Java- Exprience - Less than 1 year months
Database Management- Exprience - Less than 1 year monthsCompany Details 
company - Itechpower
description - "
Data Science,"SKILLS C Basics, IOT, Python, MATLAB, Data Science, Machine Learning, HTML, Microsoft Word, Microsoft Excel, Microsoft Powerpoint. RECOGNITION Academic Secured First place in B.Tech.Education Details 
August 2014 to May 2018 B.Tech.  Ghatkesar, Andhra Pradesh Aurora's Scientific and Technological Institute
June 2012 to May 2014  Secondary Education Warangal, Telangana SR Junior CollegeData ScienceSkill Details 
MS OFFICE- Exprience - Less than 1 year months
C- Exprience - Less than 1 year months
machine learning- Exprience - Less than 1 year months
data science- Exprience - Less than 1 year months
Matlab- Exprience - Less than 1 year monthsCompany Details 
company - 
description - "
Data Science,"Skills â¢ Python â¢ Tableau â¢ Data Visualization â¢ R Studio â¢ Machine Learning â¢ Statistics IABAC Certified Data Scientist with versatile experience over 1+ years in managing business, data science consulting and leading innovation projects, bringing business ideas to working real world solutions. Being a strong advocator of augmented era, where human capabilities are enhanced by machines, Fahed is passionate about bringing business concepts in area of machine learning, AI, robotics etc., to real life solutions.Education Details 
January 2017 B. Tech Computer Science & Engineering Mohali, Punjab Indo Global College of EngineeringData Science ConsultantData Science Consultant - DatamitesSkill Details 
MACHINE LEARNING- Exprience - 13 months
PYTHON- Exprience - 24 months
SOLUTIONS- Exprience - 24 months
DATA SCIENCE- Exprience - 24 months
DATA VISUALIZATION- Exprience - 24 months
Tableau- Exprience - 24 monthsCompany Details 
company - Datamites
description - â¢ Analyzed and processed complex data sets using advanced querying, visualization and analytics tools.
â¢ Responsible for loading, extracting and validation of client data.
â¢ Worked on manipulating, cleaning & processing data using python.
â¢ Used Tableau for data visualization.
company - Heretic Solutions Pvt Ltd
description - â¢ Worked closely with business to identify issues and used data to propose solutions for effective decision making.
â¢ Manipulating, cleansing & processing data using Python, Excel and R.
â¢ Analyzed raw data, drawing conclusions & developing recommendations.
â¢ Used machine learning tools and statistical techniques to produce solutions to problems."
Data Science,"Education Details 
 B.Tech   Rayat and Bahra Institute of Engineering and BiotechnologyData ScienceData ScienceSkill Details 
Numpy- Exprience - Less than 1 year months
Machine Learning- Exprience - Less than 1 year months
Tensorflow- Exprience - Less than 1 year months
Scikit- Exprience - Less than 1 year months
Python- Exprience - Less than 1 year months
GCP- Exprience - Less than 1 year months
Pandas- Exprience - Less than 1 year months
Neural Network- Exprience - Less than 1 year monthsCompany Details 
company - Wipro
description - Bhawana Aggarwal
E-Mail:<EMAIL>
Phone: ***********
VVersatile, high-energy professional targeting challenging assignments in Machine
PROFILE SUMMARY
âª An IT professional with knowledge and experience of 2 years in Wipro Technologies in Machine
Learning, Deep Learning, Data Science, Python, Software Development.
âª Skilled in managing end-to-end development and software products / projects from inception, requirement
specs, planning, designing, implementation, configuration and documentation.
âª Knowledge on Python , Machine Learning, Deep Learning, data Science, Algorithms, Neural Network,
NLP, GCP.
âª Knowledge on Python Libraries like Numpy, Pandas, Seaborn , Matplotlib, Cufflinks.
âª Knowledge on different algorithms in Machine learning like KNN, Decision Tree, Bias variance Trade off,
Support vector Machine(SVM),Logistic Regression, Neural networks.
âª Have knowledge on unsupervised, Supervised and reinforcement data.
âª Programming experience in relational platforms like MySQL,Oracle.
âª Have knowledge on Some programming language like C++,Java.
âª Experience in cloud based environment like Google Cloud.
âª Working on different Operating System like Linux, Ubuntu, Windows.
âª Good interpersonal and communication skills.
âª Problem solving skills with the ability to think laterally, and to think with a medium term and long term
perspective
âª Flexibility and an open attitude to change.
âª Ability to create, define and own frameworks with a strong emphasis on code reusability.
TECHNICAL SKILLS
Programming Languages Python, C
Libraries Seaborn, Numpy, Pandas, Cufflinks, Matplotlib
Algorithms
KNN, Decision Tree, Linear regression, Logistic Regression, Neural Networks, K means clustering,
Tensorflow, SVM
Databases SQL, Oracle
Operating Systems Linux, Window
Development Environments NetBeans, Notebooks, Sublime
Ticketing tools Service Now, Remedy
Education
UG Education:
B.Tech (Computer Science) from Rayat and Bahra Institute of Engineering and Biotechnology passed with 78.4%in
2016.
Schooling:
XII in 2012 from Moti Ram Arya Sr. Secondary School(Passed with 78.4%)
X in 2010 from Valley Public School (Passed with 9.4 CGPA)
WORK EXPERINCE
Title : Wipro Neural Intelligence Platform
Team Size : 5
Brief: Wiproâs Neural Intelligence Platform harnesses the power of automation and artificial intelligence
technologiesânatural language processing (NLP), cognitive, machine learning, and analytics. The platform
comprises three layers: a data engagement platform that can easily access and manage multiple structured and
unstructured data sources; an âintent assessment and reasoningâ engine that includes sentiment and predictive
analytics; and a deep machine learning engine that can sense, act, and learn over time. The project entailed
automating responses to user queries at the earliest. The Monster Bot using the power of Deep Machine Learning,
NLP to handle such queries. User can see the how their queries can be answered quickly like allL1 activities can be
eliminated.
Entity Extractor -> This involves text extraction and NLP for fetching out important information from the text like
dates, names, places, contact numbers etc. This involves Regex, Bluemix NLU apiâs and machine learning using
Tensor flow for further learning of new entities.
Classifier ->This involves the classifications of classes, training of dataset and predicting the output using the SKLearn
classifier (MNB, SVM, SGD as Classifier) and SGD for the optimization to map the user queries with the best
suited response and make the system efficient.
NER: A Deep Learning NER Model is trained to extract the entities from the text. Entities like Roles, Skills,
Organizations can be extracted from raw text. RNN(LSTM) Bidirectional model is trained for extracting such entities
using Keras TensorFlow framework.
OTHER PROJECTS
Title : Diabetes Detection
Brief : Developed the software which can detect whether the person is suffering from Diabetes or not and got the third
prize in it.
TRAINING AND CERTIFICATIONS
Title: Python Training, Machine Learning, Data Science, Deep Learning
Organization: Udemy, Coursera (Machine Learning, Deep Learning)
Personal Profile
Fatherâs Name :Mr. Tirlok Aggarwal
Language Known : English & Hindi
Marital Status :Single
Date of Birth(Gender):1993-12-20(YYYY-MM-DD) (F)
company - Wipro
description - Developing programs in Python.
company - Wipro
description - Title : Wipro Neural Intelligence Platform
Team Size : 5
Brief: Wiproâs Neural Intelligence Platform harnesses the power of automation and artificial intelligence
technologiesânatural language processing (NLP), cognitive, machine learning, and analytics. The platform
comprises three layers: a data engagement platform that can easily access and manage multiple structured and
unstructured data sources; an âintent assessment and reasoningâ engine that includes sentiment and predictive
analytics; and a deep machine learning engine that can sense, act, and learn over time. The project entailed
automating responses to user queries at the earliest. The Monster Bot using the power of Deep Machine Learning,
NLP to handle such queries. User can see the how their queries can be answered quickly like allL1 activities can be
eliminated.
Entity Extractor -> This involves text extraction and NLP for fetching out important information from the text like
dates, names, places, contact numbers etc. This involves Regex, Bluemix NLU apiâs and machine learning using
Tensor flow for further learning of new entities.
Classifier ->This involves the classifications of classes, training of dataset and predicting the output using the SKLearn
classifier (MNB, SVM, SGD as Classifier) and SGD for the optimization to map the user queries with the best
suited response and make the system efficient.
NER: A Deep Learning NER Model is trained to extract the entities from the text. Entities like Roles, Skills,
Organizations can be extracted from raw text. RNN(LSTM) Bidirectional model is trained for extracting such entities
using Keras TensorFlow framework.
company - Wipro Technologies
description - An IT professional with knowledge and experience of 2 years in Wipro Technologies in Machine
Learning, Deep Learning, Data Science, Python, Software Development.
âª Skilled in managing end-to-end development and software products / projects from inception, requirement
specs, planning, designing, implementation, configuration and documentation.
âª Knowledge on Python , Machine Learning, Deep Learning, data Science, Algorithms, Neural Network,
NLP, GCP.
âª Knowledge on Python Libraries like Numpy, Pandas, Seaborn , Matplotlib, Cufflinks.
âª Knowledge on different algorithms in Machine learning like KNN, Decision Tree, Bias variance Trade off,
Support vector Machine(SVM),Logistic Regression, Neural networks.
âª Have knowledge on unsupervised, Supervised and reinforcement data.
âª Programming experience in relational platforms like MySQL,Oracle.
âª Have knowledge on Some programming language like C++,Java.
âª Experience in cloud based environment like Google Cloud.
âª Working on different Operating System like Linux, Ubuntu, Windows.
âª Good interpersonal and communication skills.
âª Problem solving skills with the ability to think laterally, and to think with a medium term and long term
perspective
âª Flexibility and an open attitude to change.
âª Ability to create, define and own frameworks with a strong emphasis on code reusability."
Data Science,"Personal Skills â¢ Ability to quickly grasp technical aspects and willingness to learn â¢ High energy levels & Result oriented. Education Details 
January 2018 Master of Engineering Computer Technology & Application Bhopal, Madhya Pradesh Truba Institute of Engineering & Information Technology
January 2010 B.E. computer science Bhopal, Madhya Pradesh RKDF Institute of Science and Technology College of Engineering
January 2006 Polytechnic Information Technology Vidisha, Madhya Pradesh SATI Engineering College in Vidisha
January 2003 M.tech Thesis Detail  BMCH School in Ganj basodaData scienceI have six month experience in Data Science. Key Skills: - Experience in Machine Learning, Deep Leaning, NLP, Python, SQL, Web Scraping Good knowledge in computer subjects and ability to updateSkill Details 
Experience in Machine Learning, Deep Learning, NLP, Python, SQL, Web Crawling, HTML,CSS.- Exprience - Less than 1 year monthsCompany Details 
company - RNT.AI Technology Solution
description - Text classification using Machine learning Algorithms with python.
Practical knowledge of Deep learning algorithms such as Â Recurrent Neural Networks(RNN).
Develop custom data models and algorithms to apply to dataset
Experience with Python packages like Pandas, Scikit-learn, Tensor Flow, Numpy, Matplotliv, NLTK.
Comfort with SQL, Â MYSQL
Sentiment analysis.
Â Apply leave Dataset using classification technique like Tf--idf , LSA with cosine similarity using Machine learning Algorithms.
Web crawling using Selenium web driver and Beautiful Soup with python.
company - Life Insurance Corporation of India Bhopal
description - Ã¼Â Explaining policy features and the benefits
Ã¼ Updated knowledge of life insurance products and shared with customers"
Data Science,"Expertise â Data and Quantitative Analysis â Decision Analytics â Predictive Modeling â Data-Driven Personalization â KPI Dashboards â Big Data Queries and Interpretation â Data Mining and Visualization Tools â Machine Learning Algorithms â Business Intelligence (BI) â Research, Reports and Forecasts Education Details 
 PGP in Data Science  Mumbai, Maharashtra Aegis School of data science & Business
 B.E. in Electronics & Communication Electronics & Communication Indore, Madhya Pradesh IES IPS AcademyData ScientistData Scientist with PR CanadaSkill Details 
Algorithms- Exprience - 6 months
BI- Exprience - 6 months
Business Intelligence- Exprience - 6 months
Machine Learning- Exprience - 24 months
Visualization- Exprience - 24 months
spark- Exprience - 24 months
python- Exprience - 36 months
tableau- Exprience - 36 months
Data Analysis- Exprience - 24 monthsCompany Details 
company - Aegis school of Data Science & Business
description - Mostly working on industry project for providing solution along with Teaching Appointments: Teach undergraduate and graduate-level courses in Spark and Machine Learning as an adjunct faculty member at Aegis School of Data Science, Mumbai (2017 to Present)
company - Aegis school of Data & Business
description - Data Science Intern, Nov 2015 to Jan 2016

Furnish executive leadership team with insights, analytics, reports and recommendations enabling effective strategic planning across all business units, distribution channels and product lines.

â Chat Bot using AWS LEX and Tensor flow  Python
The goal of project creates a chat bot for an academic institution or university to handle queries related courses offered by that institute. The objective of this task is to reduce human efforts as well as reduce man made errors. Even by this companies handle their client 24x7. In this case companies are academic institutions and clients are participants or students.
â Web scraping using Selenium web driver   Python
The task is to scrap the data from the online messaging portal in a text format and have to find the pattern form it.
â Data Visualization and Data insights   Hadoop Eco System, Hive, PySpark, QlikSense
The goal of this project is to build a Business Solutions to a Internet Service Provider Company, like handling data which is generated per day basis, for that we have to visualize that data and find the usage pattern form it and have a generate a reports.
â Image Based Fraud Detection   Microsoft Face API, PySpark, Open CV
The main goal of project is Recognize similarity for a face to given Database images. Face recognition is the recognizing a special face from set of different faces. Face is extracted and then compared with the database Image if that Image recognized then the person already applied for loan from somewhere else and now hiding his or her identity, this is how we are going to prevent the frauds in the initial stage itself.
â Churn Analysis for Internet Service Provider   R, Python, Machine Learning, Hadoop
The objective is to identify the customer who is likely to churn in a given period of time; we have to pretend the customer giving incentive offers.
â Sentiment Analysis   Python, NLP, Apache Spark service in IBM Bluemix.
This project is highly emphasis on tweets from Twitter data were taken for mobile networks service provider to do a sentiment analysis and analyze whether the expressed opinion was positive, negative or neutral, capture the emotions of the tweets and comparative analysis.

Quantifiable Results:
â Mentored 7-12 Data Science Enthusiast each year that have all since gone on to graduate school in Data Science and Business Analytics.
â Reviewed and evaluated 20-40 Research Papers on Data Science for one of the largest Data Science Conference called Data Science Congress by Aegis School of Business Mumbai.
â Heading a solution providing organization called Data Science Delivered into Aegis school of Data Science Mumbai and managed 4-5 live projects using Data Science techniques.
â Working for some social cause with the help of Data Science for Social Goods Committee, where our team developed a product called ""Let's find a missing Child"" for helping society.
company - IBM India pvt ltd
description - Mostly worked on blumix and IBM Watson for Data science."
HR,"TECHNICAL SKILLS â¢ Typewriting â¢ TORA â¢ SPSSEducation Details 
January 2017 MBA  Chidambaram, Tamil Nadu SNS College of Engineering
January 2014 HSC   at SAV Higher Secondary School
 MBA   SNS College of Engineering
 SSLC Finance  at Kamaraj Matriculation SchoolHRSkill Details 
Human resource, Finance- Exprience - Less than 1 year monthsCompany Details 
company - 
description - "
HR,"I.T. Skills â¢ Windows XP, Ms Office (Word, Excel: Look-ups; Pivot table; other basic functions ; Power Point) â¢ Saral Payment Package- payroll software â¢ Internet ApplicationsEducation Details 
January 2006 Bachelor in Hospitality Management International Hospitality Management  Queen Margaret University Edinburg
January 2006 diploma Hotel Management  International Institute of Hotel ManagementHRSkill Details 
Hr Management- Exprience - Less than 1 year monthsCompany Details 
company - Atri Developers
description - â¢ HR Payroll Statutory Compliance Performance Management
company - 
description - Employee Relations and Administration: Creating industry specific Policies, Procedure, Forms, Formats, Letters, Checklists etc

Payroll Management: Salary restructuring to process payroll of 600 employees.
â¢ Validation of all input (Attendance, Leaves, and Salaries) before starting salary process.
â¢ Processing accurate & error free salary of employees.
â¢ Responsible for compensation and benefits administration.
â¢ Coordinate with Accounts team for salary processing.
â¢ Attendance & Leave record management
â¢ Assuring prompt and satisfactory resolution of payroll related queries of all employees.

Statutory Compliance Management:
â¢  Manage various statutory compliance requirements (PF, ESIC, PT, Gratuity, TDS etc calculations, deduction, payment and return filing.
â¢ Generate statutory reports like Form 16, Form 24Q. Conducting session with employees on Statutory Policies and procedure, compliance related topics.
â¢ Shops and Commercial Establishments Act (S&E)
â¢ The Payment of Gratuity Act 1972
Recruitment and Selection: Handling recruitment like job posting in naukri portal and coordination. Create annual manpower plan and budget. Screen and schedule preliminary interview. Arrange for employee orientation. Handling joining formalities and salary account opening formalities.

Performance Management: End to end facilitation of PMS starting from creating Job Description & Appraisal Forms to Disbursement of Letters. KRA setting, Mid-year reviews, Annual reviews, handling all appraisal activities (360 Degree)

Training and Development: Conduct training need analysis and arrange various training session.

Employee engagement and  Employee Welfare: Creation and deployment  of Sales  Rewards and Recognition Schemes, Periodic Interactive sessions like Monthly Birthday Celebration, Annual Day, Diwali Dhamaka, Offsite etc.
Working on Saral Payment Package- payroll software as well as on excel
Assisting MD in HR works, offering suggestions and answering employee queries on payroll compliance related issues, other benefits (insurance, medical, reimbursement, ), full & final settlement of resigned employees."
HR,"Education Details 
 BA   mumbai UniversityHRSkill Details 
Hr Operations- Exprience - Less than 1 year monthsCompany Details 
company - Mumbai Monorail
description - "
HR,"Education Details 
June 2012 to May 2015 B.A Economics Chennai, Tamil Nadu SdnbvcHrSkill Details 
Company Details 
company - Anything IT Solution
description - Hr"
HR,"Education Details 
June 2012 to May 2015 B.A Economics Chennai, Tamil Nadu SdnbvcHrSkill Details 
Company Details 
company - Anything IT Solution
description - Hr"
HR,"Education Details 
 BBA   lovely professional universityHRSkill Details 
Communication- Exprience - 6 monthsCompany Details 
company - 
description - "
HR,"Education Details 
 MBA   ACN College of engineering & mgtHRSkill Details 
Company Details 
company - HR Assistant
description - "
HR,"KEY SKILLS: â¢ Computerized accounting with tally â¢ Sincere & hard working â¢ Management accounting & income tax â¢ Good communication & leadership â¢ Two and four wheeler driving license â¢ Internet & Ecommerce management COMPUTER SKILLS: â¢ C Language â¢ Web programing â¢ Tally â¢ Dbms Education Details 
June 2017 to June 2019 Mba Finance/hr India Mlrit
June 2014 to June 2017 Bcom Computer Hyderabad, Telangana Osmania university
June 2012 to April 2014 Inter MEC India SrimedhavHrNaniSkill Details 
accounting- Exprience - 6 months
DATABASE MANAGEMENT SYSTEM- Exprience - 6 months
Dbms- Exprience - 6 months
Management accounting- Exprience - 6 months
Ecommerce- Exprience - 6 monthsCompany Details 
company - Valuelabs
description - They will give the RRF form the required DLT then the hand over to RLT then scrum master will take the form from the RLT then scrum master will give the forms to trainee which we can work on the requirement till the candidate receive the offer from the company"
HR,"Training in Special Education (Certificate Course) Education Details 
July 2016 to October 2018 M.Sc Psychology with specialization in Organizational Behaviour Malappuram, Kerala Calicut University
July 2013 to March 2016 BSc Psychology Thrissur Prajyoti Niketan CollegeHRSkill Details 
Company Details 
company - 
description - I have done a 30 days internship in the HR department of Foster Hot Breads, KINFRA, Malappuram, Kerala and I have also done a 60 days internship at Santhwana Institute of Counselling and Psychotherapy, Cochin, Kerala as Counsellor"
HR,"Computer Knowledge: â¢ Proficient in basic use of MS office â¢ Microsoft Dynamics AX software â¢ SAIBA softwareEducation Details 
 MBA   Distance education Bharathiar University
 BE   PA College of Engineering and Technology
 HSC   R.V.G. Hr Sec School
 SSC   G.Hr.Sec SchoolHRAdmin in BharatSkill Details 
DYNAMICS- Exprience - 6 months
DYNAMICS AX- Exprience - 6 months
MICROSOFT DYNAMICS- Exprience - 6 months
MICROSOFT DYNAMICS AX- Exprience - 6 months
MS OFFICE- Exprience - 6 monthsCompany Details 
company - Sri Ramesh Gaarment
description - Tirupur

Administration as well as clients service
Here corporate companies only insured so that knowledge gathered about
Garments, spinning mills

â¢ FEB 2018 to Still: Sri Ramesh Gaarment Tirupur.

HR Activities
Attendance maintenance, Time cards maintenance,
Staffs and labors individual records maintenance

Project:
â¢ Advanced automobile collision avoidance and blackbox in CAR"
HR,"SOFTWARE SKILLS: â¢ General Computer Proficiency â¢ Program Langages known C, C+, Java, Web Programming â¢ Tools & Software know MATLAB. DBMS KEY STRENGTHS: â¢ Posse's Good communication and analytic skills. â¢ Positive thinking. Sincere, Hard work, Honesty, Responsibility. â¢ Enthusiastic to learn new skills & take up new tasks. â¢ Self - motivated. â¢ Ready to accept challenges Education Details 
January 2014 to January 2017 BE in computer science and engineering computer science engineering  Adichunchanagiri institute of technology chikmagalurHRSkill Details 
DATABASE MANAGEMENT SYSTEM- Exprience - 6 months
DBMS- Exprience - 6 months
JAVA- Exprience - 6 months
MATLAB- Exprience - 6 monthsCompany Details 
company - Yours Truly
description - Jayashree H .K)"
Advocate,"TECHNICAL QUALIFICATIONS: â¢ Windows, Ms. OfficeEducation Details 
 LL.B  Guwahati, Assam University Law College, Guwahati University
 B.Sc  Jagiroad, ASSAM, IN Jagiroad College
    Morigaon CollegeAdvocateAdvocate - Gauhati High CourtSkill Details 
Company Details 
company - Gauhati High Court
description - of the Gauhati High Court and other subordinate Courts and Tribunals and Statutory Bodies in Guwahati from 2008. Numerous experiences of Civil, Criminal cases, cases under consumer forum.
â¢ Handling a good number of Civil as well as Criminal Matters in District & Sessions Judge Court, Judge Court, C.J.M. Court, and Consumer Forum & also in the High Court.
â¢ Working as Executive (Legal) at Hindustan Paper Corporation Limited (A Govt. of India Enterprise), Nagaon Paper Mill since Jan '2012.
STRENGTH: 
â¢ Adaptability: A penchant to adapt to the conditions & level of the environment accordance with the requirement of the given situation.
â¢ Interpersonal: An extrovert to the core, prefer to interact with people of all walks of life and varying strata of society. Ability to quickly establish rapport with others.
â¢ Team Work: Enjoy being a part of a group & achieve combined goals.
ROLES AND RESPONSIBILITIES AT HPC LTD 
â¢ Assisting in briefing counsels on matters relating to Nagaon Paper Mill, Cachar Paper Mill and also Nagaland Pulp and Paper Corporation Ltd.
â¢ The task of legal vetting in all NIBs/NITs terms and conditions etc.
â¢ Providing Legal views to protect the interest of the Corporation.
â¢ Assisting in formulation and drafting of all Disciplinary matters.
â¢ Liaisoning with all local bodies and also other forums."
Advocate,"Education Details 
 B.Com, LL.B.,   University of Clacutta, University of BurdwanADVOCATESkill Details 
Taxation matters Income Tax GST P Tax Accounts- Exprience - Less than 1 year months
Filing of Income Tax Returns GST Returns e-TDS AIR and more- Exprience - Less than 1 year monthsCompany Details 
company - own practice
description - 1. Drafting and preparation of plaint, Accounts and move before relevant Authority to hear the cases"
Advocate,"Education Details 
 LLB.   Dibrugarh UniversityAdvocateSkill Details 
Legal.- Exprience - Less than 1 year monthsCompany Details 
company - Legal.
description - â¢ Advocate"
Advocate,"Education Details 
November 2016 to January 2019 Llm Masters in Law Hyderabad, Telangana Sultan Ul Uloom College Of Law
September 2011 to May 2016 BA.llb Bachelors in Law Hyderabad, Telangana Osmania University PG College Of LawAdvocateExperienced in Litigation, Recently Acquired Masters Degree in LawSkill Details 
Microsoft word- Exprience - Less than 1 year months
litigation- Exprience - Less than 1 year months
Legal Research- Exprience - Less than 1 year months
Contracts- Exprience - Less than 1 year months
Internet Savvy- Exprience - Less than 1 year months
Drafting- Exprience - Less than 1 year monthsCompany Details 
company - LRC Office
description - â¢ Working under Senior Advocate L Ravichander in the High Court of Telangana.
â¢ Experience in drafting
â¢ Legal Correspondence"
Advocate,"SKILLS â¢ Knows English as native speaker (IELTS Overall 8; Listening 8, Reading 8, Writing 7 and Speaking 8.5); Hindi with bilingual proficiency and Punjabi as native speaker. â¢ Able to communicate clearly and concisely with people of diverse backgrounds and levels of authority. â¢ Exceptional knowledge of administrative procedures, evidence rules and trials. â¢ Can maintain a professional attitude with peers, co-workers and clients at all times. â¢ Focused and driven to always meet deadlines for pretrial procedures, trials and appellate briefings by effectively utilizing resources. â¢ A Microsoft Certified Systems Engineer (2008) and very much Competent in Windows and Word processing software Education Details 
July 2009 to May 2012 Bachelor's Laws Bikaner, Rajasthan Maharaja Ganga Singh University
August 2008 to November 2008 Microsoft Certified Systems Engineer Networking Chandigarh 
July 2005 to April 2008 Bachelor's of Science Bio-Informatics Chandigarh, Chandigarh Panjab UniversityAdvocateNewcomer Indian AdvocateSkill Details 
Administration- Exprience - 72 months
Legal Research- Exprience - 72 months
Microsoft Office- Exprience - 72 months
Drafting- Exprience - 72 monthsCompany Details 
company - District Courts
description - Key Features

â¢ Licensed Advocate in practice from 16-Aug-2012 (Bar Council of Punjab & Haryana, Chandigarh).
â¢ Admitted to practice at District Courts, Mansa since 18-Aug-2012.
â¢ Successfully passed the All India Bar Examination (AIBE) conducted by Bar Council of India (BCI) in September, 2013.
â¢ Lead/Sole Counsel for plaintiff and respondents in number of criminal/civil trials.
â¢ Since 2012, have prepared and prosecuted to conclusion, either by trial or settlement, number of claim cases involving personal injury or death cases.
â¢ Working on the Panel of District Legal Services Authority, Mansa run under the supervision of National Legal Services Authority, India.
â¢ Former Co-Opted Member Administrative Committee, Punjab.
â¢ Former Joint Secretary at District Bar Association, Mansa (2017-18).

Job Duties

â¢ Advice clients of their legal rights and all matters related to law.
â¢ Plead clients' cases which include various International companies, before courts of law, forums, tribunals and boards.
â¢ Researching legal precedents, gathering evidence and trials of criminal, Injury and death compensation claims and others.
â¢ Draw up legal documents such as bail petitions, appeals, divorces and contracts etc.
â¢ Negotiates settlements of civil disputes.
â¢ Act as mediator, conciliator, local commissioner or arbitrator as per Court orders
â¢ Managing a private practice for more than 6 years, including all aspects of administration and management functions, controlling costs and achieving objectives related to the practice of law."
Advocate,"QUALIFICATION: Introduction to Computer EXTRAEducation Details 
January 2001 to January 2003 Master Law Chennai, Tamil Nadu Dr.Ambedkar Law University
January 1998 to January 2001 Bachelor Law Chennai, Tamil Nadu Dr. Ambedkar Law University
January 1995 to January 1998 Bachelor English Literature Tirunelveli, Tamil Nadu Manonmaniam Sundaranar universityAdvocateAdvocateSkill Details 
Company Details 
company - Practiced
description - at

* High Court of Judicature at Madras, India

* City Civil Court, Chennai

* Debt Recovery Tribunal, Chennai

* Consumer Forums, Chennai

* Labour Courts

* Small Causes Courts

* Rent control Courts
* Legal advisor for Christian Institute of Management, Chennai in 2016
* Legal Advisor for Ruah church, Chennai and NESSA Trust till 2018"
Advocate,"Skills: Natural Languages: Proficient in English, Hindi and Marathi. Computer skills: Proficient with MS-Office, Internet operation.Education Details 
January 2015 to January 2018 LLB Law Mumbai, Maharashtra Mumbai university
January 2015 B.M.M  Mumbai, Maharashtra S.K.Somaiya College, Mumbai University
 H.S.C   Asmita Girls junior College, Maharashtra Board
 S.S.C Vidya Bhawan  Maharashtra BoardAdvocateLlb student and JournalistSkill Details 
Company Details 
company - Criminal lawyer (law firm)
description - "
Advocate,"Skills Legal Writing Efficient researcher Legal Education Family Law Criminal Defence Environment law and litigation Business legal issue Banking Law and commercial lawEducation Details 
January 2014 LLM Criminology Pune, Maharashtra University of Pune
January 2012 LLB Law Pune, Maharashtra University of Pune
January 2012 Diploma Labour Law Pune, Maharashtra University of PuneAdvocateAdvocateSkill Details 
LEGAL WRITING- Exprience - 6 months
LITIGATION- Exprience - 6 monthsCompany Details 
company - Family, Criminal in District and Session Court
description - Legal consultancy services for
Co-operative Banks, Scheduled Banks and Private Banks
Clients
Federal Bank (Chinchwad, Pune)
Bharat Co-Operative Bank (Chinchwad, Pune)
ICICI Bank

Scope of work
Finalising Loan Agreements,
Mortgage agreement, Release Deed  and Notice of Intimation,
Title Deed, Search Report
DRT cases
Legal notices for recovery of dues,
Filing Summary suits
Paper Notice,
Power of Attorney
Indemnity bond

Family Law
Legal Consultancy and representation for matters relating to Family disputes such as Marriage and Divorce, Succession, Adoption etc.
Lok Adalat
Panel Judicial Member - Four Times"
Advocate,"Good grasping quality and skillful work Education Details 
March 2013 to March 2018 B. A. LL. B. Law Solapur, Maharashtra Solapur UniversityAdvocateSkill Details 
Good knowledge of typing as well as many other activities- Exprience - Less than 1 year monthsCompany Details 
company - District and Session court of solapur
description - Forward thinking individual with refined interpersonal and multitasking skills. Looking to join a progressive organization to provide assistance in Legal work.
company - District and Session court of solapur
description - Provide legal assistance in legal work"
Advocate,"â¢ Hard working â¢ Quick learnerEducation Details 
June 2014 to May 2017 LLB LAW Mumbai, Maharashtra mumbai university
January 2014 B.Com Commerce Mumbai, Maharashtra Mumbai university
January 2011 HSC   Maharashtra board
January 2009 SSC   Maharashtra boardAdvocateSkill Details 
Company Details 
company - The vidishtra
description - "
Arts,"â¢ Good communication skill â¢ Quick learner â¢ Keen to find solutionsEducation Details 
 MBA Marketing and International Business Management Pune, Maharashtra Pune University
 B-Tech Tech Nagpur, Maharashtra RTM Nagpur UniversityG.M. Arts, Commerce & ScienceG.M. Arts, Commerce & ScienceSkill Details 
Company Details 
company - Samarth College
description - of Engineering        30          7        210 
5      College to campus             VJ College of Pharmacy         10 days' workshop       10

G.M. Arts, Commerce & Science 6          Soft Skills                                               6 days' workshop           6
College

Personality             G.M. Institute of Agricultural 7		6 days' workshop           6
Development                        Diploma 
8          Soft Skills           Samarth College of Polytechnic     20 days' workshop       20

TOTAL                                              350
WORKING EXPERIENCE IN CORPORATE:
Sr. No               Topic                      Company                  No. of days    Total Hrs 
1       Presentation skill & Team    Elringklinger Automotives Pvt 1 Day           8 building Workshop             Ltd, Ranjangaon, Pune 
2           Negotiation skill & Kubler Automation Pvt Ltd., 2 days          16
Communication skill               Chakan, Pune 
3       Business Communication       Finanza Home Loans, Pimple 3 days          21 & Stress management               saudagar, Pune 
4        Team-building & Verbal       Sharvari Products Pvt Ltd, 2 days          16 communication                   Junner, Pune 7 days' 5             Entrepreneurship       Agriculture Research Centre,
Workshop        168
Development               Narayangaon, Pune (8 batches)
TOTAL                                             229

ADJOINING SKILLS: â¢ Working knowledge of Windows operating system and MS Office â¢ Communicate well in English, Hindi & Marathi.
â¢ Organized and participated in events like gathering, teachers day, fashion show and various science exhibitions at college"
Arts,"â¢ Operating Systems: Windows XP / Vista / 07Education Details 
January 2018 M.F.A painting Nagpur, Maharashtra Nagpur University
January 2016 B.F.A. Painting Nagpur, Maharashtra Nagpur University
January 2012 Diploma Art  Maharashtra State Board
January 2010 H.S.C.   Maharashtra State Board
January 2008 S.S.C.   Maharashtra State BoardFine arts lecturerSkill Details 
Ms-Cit- Exprience - 96 monthsCompany Details 
company - Shubhankan Fine Arts College, indore
description - â¢ I'm doing a job as a Lecturer in Shubhankan Fine Arts College Indore from Nov 2018.
â¢ I'm an Artist, completed ATD, BFA and MFA in painting. 
â¢ I'm searching for a job in my faculty in my area and comfort place. To improve my knowledge and experience in this field.
company - 
description - I have a experience of classes of painting, rangoli, drawing, summer classes, etc

Health: Physical Disability ( Orthopedically)"
Arts,"Additional qualifications April 2000, Web Designing Course with above average computer skillsEducation Details 
January 2000 to January 2001 Bachelor of Arts Sociology Mumbai, Maharashtra The Mumbai University
January 1998 to January 2000 Bachelor of Arts Sociology  Sophia College
January 1997 to January 1998 H.S.C.   Sophia College
January 1995 to January 1996 S.S.C.   St. Teresa's Convent High SchoolHead business development, artsHead business development, artsSkill Details 
Company Details 
company - British Council
description - Responsibilities
Ã± Strategic oversight responsibility for programmes in the performing arts (music, theatre and dance) and other cultural sectors.
Ã± lead on the conception and oversight of specific large-scale programmes within the overall Arts
programme,
Ã± Represent the British Council at external events in India and act as deputy to the Director, Arts when required.
Ã± Oversee and manage resources to deliver compelling communications for applicants that convey
British Council's grants like Charles Wallace India trust, Young Creative Entrepreneurs and
Chevening Clore scholarship programs on time and with excellence
Ã± Shortlisting and Interviewing potential applicants for existing relevant grants or fellowships.
Ã± Oversee a diverse range of proposals, progress reports and related projects
Ã± Ensuring effective and timely identification and communication of program progress
Ã± Lead a team of six project managers across the country and manage the performance of the team
responsible for executing arts projects with partnerships built into their work, expertise within their
geographic region to ensure arts insight and knowledge is available as and when required.
Ã± Managing relations with existing partners, and developing relationships with targeted new partners and key government officials and ensuring that market insight into business development
opportunities is built into the planning of new programmes
Ã± Primary strategic responsibility for the marketing of the Arts program in India, to ensure that the program builds a reputation that will be attractive to potential partners, in partnership with the
Marketing and Communications team.
company - British Council
description - is a cultural relations organization creating international opportunities for the people of the UK and other countries by building trust between them worldwide. They have offices in six continents and over 100 countries bringing international opportunity to life, every day. Each year they work with millions of people, connecting them with the United Kingdom, sharing their cultures and the UK's most attractive
assets: English, the Arts, and Education. They have 80 years' experience of doing this
company - British Council
description - Responsibilities
Ã± Leading the strategic development of British Council's work in the music sector in India/ Sri Lanka
region and building and maintaining strong international partnerships across sectors in India/ Sri
Lanka and the UK
Ã± Developing strong external partnerships that lead to significant external investment in BC activities and enabling the delivery of an ambitious programme of music sector activities and events thus
strengthening cultural relationships between India/ Sri Lanka and the UK
Ã±    Leading the implementation of the music programme within India and Sri Lanka along with detailed
project plans in liaison with colleagues from India/ Sri Lanka and the UK
Ã±    Proactive management of budgets and timelines for all projects
Ã±    Ensuring systematic evaluation of projects, including developing effective systems and processes for capturing both quantitative and qualitative information about effectiveness of projects and longer
term impact
Ã±    Management of a team across India and Sri Lanka, contributing to recruitment and development/
mentoring of staff
company - British Council
description - Responsibilities
Ã± Planning and organizing logistics related to events, buildings, performers/artists and other
personnel
Ã± Marketing a performance or event through social media, direct mail, advertising, use of a website,
producing posters or publicity leaflets and attracting media coverage
Ã± Planning and managing budgets
Ã± Programming and booking performances and events, including arrangements for tours in India
Ã± Development of new projects and initiatives in consultation with arts professionals and key
stakeholders (e.g. local authorities, local government and communities, venue directors and regional partners)
Ã± Taking responsibility for operational and office management issues such as venue accessibility,
health and safety issues
Ã± Implementing and maintaining office and information systems
Ã± Providing administration support to managers and the director
Ã± Ensuring corporate and legal requirements are complied with, and reporting to the head of the unit
company - British Council
description - Responsibilities
Ã± Developing of new specific new projects and initiatives in the music, film and visual art sector in consultation with the Council and key stakeholders
Ã± Planning and managing budgets
Ã± Supporting the marketing a performance or event through social media, direct mail, advertising, use of a website, producing posters or publicity leaflets and attracting media coverage
Ã± Programming the outreach and workshops for the respective programmes.
company - AirCheck India
description - The company intended to launch stations in both these metros on August 29, 2001.
For its Mumbai FM station, WIN had the basic infrastructure that includes a studio and production facilities.
The transmission tower for the station is located in central Mumbai.

Responsibilities
Ã± Generating and researching ideas for programmes and pitching for commissions
Ã± Sourcing potential contributors and interviewees
Ã± Selecting music appropriate to the programme, the audience and the station
Ã± Producing pre-production briefings for presenters, reporters, technical staff and other contributors
Ã± Managing the logistics of getting people, resources and equipment together to the right place at the right time
Ã± undertaking editing, interviewing and reporting duties as necessary
Ã± Presenting programmes or managing presenters for both pre-recorded and recorded output
Ã± Checking that copyrights are cleared and understanding media law
Ã± Using editing and mixing software's like 'Sonic Foundry Vegas', 'Sonic Foundry Sound Forge',
'Acid', and 'Midi'.
company - Rave Magazine
description - Rave Magazine was the definitive voice of music emerging from the Indian sub-continent and the lifestyle
that surrounds it. Through exclusive reporting, a unique sensibility, and with an editorial team with over 20
years of experience in publishing, RAVE Magazine covers every genre of music emerging from the region and provides new perspectives on International music.

Responsibilities
Ã± Maintain production schedules and report on the progress
Ã± Overview the staff, manage and supervise photographers and freelance writers and generally
provide administrative support for the editor
Ã± Participated in production meetings and brain storming sessions to decide on the direction, future
trends and contents of the publication
company - Xs Events
description - Xs Events is an event management company primarily dealing with corporate clients who used different
events to increase an audience's exposure with a brand.

Responsibilities
Ã± Development, production and delivery of projects from proposal right up to delivery.
Ã± Delivering events on time, within budget
Ã± Maintaining timelines and priorities on every project
Ã± Managing supplier relationships
Ã±    Managing operational and administrative         functions to ensure specific projects are delivered
efficiently
company - Banyan Tree Communications
description - Responsibilities
Ã± Sourcing potential contributors and interviewees
Ã± Selecting music appropriate to the programme, the audience and the station
Ã± undertaking editing, interviewing and reporting duties as necessary
Ã± Checking that copyrights are cleared and understanding media law
company - French Embassy
description - on a part time basis.
company - British Council
description - Mumbai
Advice students on various academic opportunities in the United Kingdom and assisted with various
exhibitions by the British Council."
Arts,"Education Details 
January 2017    Rachana Sansad School of Interior Deign
January 2013    Holy Family High School
 Master of Commerce Marketing Mumbai, Maharashtra University of MumbaiDrawing & Arts & Craft TeacherDrawing & Arts & Craft Teacher - Ghatkopar YMCASkill Details 
Company Details 
company - Ghatkopar YMCA
description - for 3 Years.
â Worked in Jungle Cubs Gym as a Co-ordinator for 1 Year."
Arts,"Education Details 
August 2018 to January 2021 Entermediate Maths Mumbai, Maharashtra Sunbeam academy , samne ghat , varanasiMartial arts (fitness job)Skill Details 
Company Details 
company - Sports Authority
description - I am 2nd dan black belt in karate (martial arts)
I am in a searching of personal trainer job for fitness.
I won 3 gold medals in national karate championship.
I won 7 gold medals in state karate championship.
3 times best player of the year of uttar pradesh award ..
Represented india and Selected for world karate championship held at Croatia , Europe.
â¢"
Arts,"I Other Skills Course/Skill Name Board Year Of Passing Grade Intermediate Grade Drawing Art Examination Committee, 2011 B Examination Maharashtra State I Academic Programme Detail National Service Scheme (Camp): - Sponsored By Government of India Ministry Of Youth Affairs And Sports, New Delhi & North Maharashtra University, Jalgaon. I Strengths â¢ Ability to Learn From Mistakes. * Honesty â¢ Ready to accept challenges and responsibilities. * Quick learning, adaptability, confidence. I Declaration I /201 Your faithfully, Pia .. . . ( Jetalal Hiralal Gorbanjara)Education Details 
May 2010 HSC  Nashik, Maharashtra State Board
June 2008 SSC  Nashik, Maharashtra State BoardAsst.ProfessorAsst.ProfessorSkill Details 
Company Details 
company - Kisan Arts
description - - Total Work Experience ( In Months)
Sr.       Name of Organization / College        Designation              Working Period          Total Experience
No.		( In Months)
From            To
1.        Kisan Arts, Commerce & Science        Asst.Professor      20/07/2015    31/05/2016        10 Months
Sr. College, Parola Dist- Jalgaon, {Political Sci.)
Maharashtra
company - Bahadarpur, Tai- Parola Dist
description - "
Web Designing,"Technical Skills Web Technologies: Angular JS, HTML5, CSS3, SASS, Bootstrap, Jquery, Javascript. Software: Brackets, Visual Studio, Photoshop, Visual Studio Code Education Details 
January 2015 B.E CSE Nagpur, Maharashtra G.H.Raisoni College of Engineering
October 2009  Photography Competition Click Nagpur, Maharashtra Maharashtra State Board
    College Magazine OCEANWeb DesignerWeb Designer - Trust Systems and SoftwareSkill Details 
PHOTOSHOP- Exprience - 28 months
BOOTSTRAP- Exprience - 6 months
HTML5- Exprience - 6 months
JAVASCRIPT- Exprience - 6 months
CSS3- Exprience - Less than 1 year months
Angular 4- Exprience - Less than 1 year monthsCompany Details 
company - Trust Systems and Software
description - Projects worked on:
1. TrustBank-CBS
Project Description: TrustBank-CBS is a core banking solution by Trust Systems.
Roles and Responsibility:
â Renovated complete UI to make it more modern, user-friendly, maintainable and optimised for bank use.
â Shared the UI structure and guidelines to be incorporated, with development team of around 50
members.
â Achieved the target of project completion in given time frame.
â Made required graphics for the project in photoshop

2. Loan Bazar (Loan Appraisal)
Project Description: Loan Bazar is a MVC-based application dedicated to creating and managing
loan applications. The goal of this application is to streamline the process of loan application and integrate with existing CBS.
Roles and Responsibility
â Designed and developed modern and responsive UI of entire application and achieved the target in given time frame.
â Made required graphics for the project in photoshop
3. Capital Security Bond Application
Project Description: Capital Security Bond Application is a MVC based application which provided an online platform to purchase gold bond
Roles and Responsibility:
â Designed and developed modern and responsive UI of entire application and achieved the target in given time frame.
â Made required graphics for the project in photoshop

4. SoftGST
Project Description: SoftGST (Web Based Application) is an ASP application to every tax
payers and its vendors for generating the GSTR returns on the basis of sales / purchase
data, additionally the application can do the reconciliation of GSTR 2 A with purchase register.
Roles and Responsibility:
â Designed and developed the UI of Dashboard.

5. Trust Analytica:
Project Description: Trust Analytika is the mobile web app that shows bank asset, liability,
income, expenses.
Roles and Responsibility:
â Designed and developed the landing page of the application.
â Supported the developers in UI implementation

6. Website's:
Project Name:
1. TSR Technology Services - http://tsrtechnologyservices.com
2. Vidarbha Merchants Urban Co-Op Bank - http://vmcbank.com
3. GISSS - http://gisss.co.in
4. Softtrust USA - http://softtrustusa.com
Roles and Responsibility
â Communicated with clients to understand their requirement
â Made mocks for the website
â Designed and developed complete website and hosted them in stipulated time.
company - www.jalloshband.com
description - Project Name:
1. Jallosh Band - www.jalloshband.com
2. An Endeavor Foundation
Roles and Responsibility:
â Communicated with clients to understand their requirement
â Made mocks for the website
â Designed and developed complete website and hosted them in stipulated time.
company - 10MagicalFingers
description - National and international client interaction.
â Management of digital data"
Web Designing,"Education Details 
 B.C.A Bachelor Computer Application Pune, Maharashtra Pune University
 H.S.C.  Pune, Maharashtra Pune University
 S.S.C.  Pune, Maharashtra Pune UniversityWeb Designing and Developerphp Developer - Exposys Pvt. LtdSkill Details 
Company Details 
company - Exposys Pvt. Ltd
description - Technical Skills
Web Development: HTML5, CSS3, Bootstrap, PHP, Ajax, Jquery, JavaScript.
Database: MySQL.
Development Tools: Notepad++, Sublime Text2.
Framework: Codeigniter.
Server: Apache tomcat, Xampp Control Panel.
Operating Systems: Windows.
company - Exposys Pvt. Ltd
description - Pune.	Augest 2017 to till date

Project Details:
Project-I: Pragat Bharat System
Technologies Used: HTML, CSS, BOOTSTRAP, PHP, JQUERY, AJAX.
Database Used: My SQL.
Team size: 1
Position: Software Developer
Synopsis: This project aim is specially design for people. It is used to collect information to diifernt sector.

Project-II: Go Ayur System
Technologies Used: HTML, CSS, BOOTSTRAP, PHP, JQUERY, AJAX.
Database Used: My SQL.
Team size: 2
Position: Software Developer
Synopsis: Go Ayurveda Panchakarma center is one of most traditionally well established, professional and innovative providers of Classical
Ayurvedic Health services and Kerala Panchakarma therapies.

Project-III: Vitsanindia System
Technologies Used: HTML, CSS, BOOTSTRAP, PHP, JQUERY, AJAX, JAVA SCRIPT.
Database Used: My SQL.
Team size: 2
Position: Software Developer
Synopsis: Online Shooping through app. This app is user friendly because there is a option for change language. User can to find different categories products as there choice.

Project-IV: MahabaleshwarTours
Technologies Used: HTML, CSS, BOOTSTRAP, PHP, JQUERY, AJAX, JAVA SCRIPT.
Database Used: My SQL.
Team size: 1
Position: Software Developer
Synopsis: In this system is to provide Online Registration, Tour Package Information, Ticket Booking, Online Payment and Searching Facility for Customer and also Generate Different types of Report.

Project-V: Cityspaceindia
Technologies Used: HTML, CSS, BOOTSTRAP, PHP, JQUERY, AJAX, JAVA SCRIPT.
Database Used: My SQL.
Team size: 1
Position: Software Developer
Synopsis: Service provider website we provide different categories.

Project-VI: Fruitsbuddy
Technologies Used: HTML, CSS, BOOTSTRAP, PHP, JQUERY, AJAX, JAVA SCRIPT.
Database Used: My SQL.
Team size: 1
Position: Software Developer
Synopsis: Fruitbuddy is to manage the details of fruits, Customer, Order, Transaction, Payment. It manages all the information about fruits, Stocks, Payment. The project is totally built at administrative end and thus only the administrator is guaranteed the access. The purpose of the project is to build an application program to reduce the manual work for managing the fruits, Customer, Stocks, Order.

Project-VII: Totalcitee
Technologies Used: HTML, CSS, BOOTSTRAP, PHP, JQUERY, AJAX, JAVA SCRIPT.
Database Used: My SQL.
Team size: 1
Position: Software Developer
Synopsis: Real Estate web application has been created for helping you to sell properties through web based user interface. Visitors on your website can view particular desired products using search engine facility.

Project-VIII: Golchha
Technologies Used: HTML, CSS, BOOTSTRAP, PHP, JQUERY, AJAX, JAVA SCRIPT.
Database Used: My SQL.
Team size: 1
Position: Software Developer
Synopsis: Service provider website we provide different categories."
Web Designing,"Technical Skills Web Technologies: Angular JS, HTML5, CSS3, SASS, Bootstrap, Jquery, Javascript. Software: Brackets, Visual Studio, Photoshop, Visual Studio Code Education Details 
January 2015 B.E CSE Nagpur, Maharashtra G.H.Raisoni College of Engineering
October 2009  Photography Competition Click Nagpur, Maharashtra Maharashtra State Board
    College Magazine OCEANWeb DesignerWeb Designer - Trust Systems and SoftwareSkill Details 
PHOTOSHOP- Exprience - 28 months
BOOTSTRAP- Exprience - 6 months
HTML5- Exprience - 6 months
JAVASCRIPT- Exprience - 6 months
CSS3- Exprience - Less than 1 year months
Angular 4- Exprience - Less than 1 year monthsCompany Details 
company - Trust Systems and Software
description - Projects worked on:
1. TrustBank-CBS
Project Description: TrustBank-CBS is a core banking solution by Trust Systems.
Roles and Responsibility:
â Renovated complete UI to make it more modern, user-friendly, maintainable and optimised for bank use.
â Shared the UI structure and guidelines to be incorporated, with development team of around 50
members.
â Achieved the target of project completion in given time frame.
â Made required graphics for the project in photoshop

2. Loan Bazar (Loan Appraisal)
Project Description: Loan Bazar is a MVC-based application dedicated to creating and managing
loan applications. The goal of this application is to streamline the process of loan application and integrate with existing CBS.
Roles and Responsibility
â Designed and developed modern and responsive UI of entire application and achieved the target in given time frame.
â Made required graphics for the project in photoshop
3. Capital Security Bond Application
Project Description: Capital Security Bond Application is a MVC based application which provided an online platform to purchase gold bond
Roles and Responsibility:
â Designed and developed modern and responsive UI of entire application and achieved the target in given time frame.
â Made required graphics for the project in photoshop

4. SoftGST
Project Description: SoftGST (Web Based Application) is an ASP application to every tax
payers and its vendors for generating the GSTR returns on the basis of sales / purchase
data, additionally the application can do the reconciliation of GSTR 2 A with purchase register.
Roles and Responsibility:
â Designed and developed the UI of Dashboard.

5. Trust Analytica:
Project Description: Trust Analytika is the mobile web app that shows bank asset, liability,
income, expenses.
Roles and Responsibility:
â Designed and developed the landing page of the application.
â Supported the developers in UI implementation

6. Website's:
Project Name:
1. TSR Technology Services - http://tsrtechnologyservices.com
2. Vidarbha Merchants Urban Co-Op Bank - http://vmcbank.com
3. GISSS - http://gisss.co.in
4. Softtrust USA - http://softtrustusa.com
Roles and Responsibility
â Communicated with clients to understand their requirement
â Made mocks for the website
â Designed and developed complete website and hosted them in stipulated time.
company - www.jalloshband.com
description - Project Name:
1. Jallosh Band - www.jalloshband.com
2. An Endeavor Foundation
Roles and Responsibility:
â Communicated with clients to understand their requirement
â Made mocks for the website
â Designed and developed complete website and hosted them in stipulated time.
company - 10MagicalFingers
description - National and international client interaction.
â Management of digital data"
Web Designing,"Education Details 
January 2016 B.Sc. Information Technology Mumbai, Maharashtra University of Mumbai
January 2012 HSC  Allahabad, Uttar Pradesh Allahabad university
January 2010 SSC dot Net Allahabad, Uttar Pradesh Allahabad universityWeb designer and Developer TrainerWeb designer and DeveloperSkill Details 
Web design- Exprience - 12 months
Php- Exprience - 12 monthsCompany Details 
company - NetTech India
description - Working. ( salary - 12k)
PERSONAL INTEREST

Listening to Music, Surfing net, Watching Movie, Playing Cricket.
company - EPI Center Academy
description - Working.  ( Salary Contract based)
company - Aptech Charni Road
description - Salary Contract based)"
Web Designing,"IT SKILLS Languages: C (Basic), JAVA (Basic) Web Technologies: HTML5, CSS3, Bootstrap, JavaScript, jQuery, Corel Draw, Photoshop, Illustrator Databases: MySQL5.0 IDE & Tools: Sublime Text, Notepad Operating Systems: Windows XP, Windows 7Education Details 
September 2015 Bachelor of Engineer Information technology Nagpur, Maharashtra Nagpur University
May 2011 HSC Secondary & Higher Secondary  State Board of Secondary
June 2009 SSC Secondary & Higher Secondary  Maharashtra State Board of SecondaryWeb and Graphics DesignerWeb and Graphics Designer - Virtuous Media Point, PuneSkill Details 
BOOTSTRAP- Exprience - 24 months
HTML5- Exprience - 24 months
JAVASCRIPT- Exprience - 24 months
jQuery- Exprience - 24 months
COREL DRAW- Exprience - 24 months
Adobe Photoshop- Exprience - 24 months
Adobe Illustrator- Exprience - 12 months
CSS3- Exprience - 24 monthsCompany Details 
company - Virtuous Media Point
description - 
company - CNC Web World
description - Internship Program: At e-sense IT Solution pvt.ltd. Nagpur as a Web Designing and Developement.
* Presented in Project Competition in Innovesta 15 of Priyadarshini Indira Gandhi College of Engineering, Nagpur.
* Presented in National Level Paper Presentation in TECH-WAVE 2015 of S.R.M.C.E., Nagpur.
company - e-sense IT Solution pvt.ltd
description - Key Result Areas:
* Designed websites solutions by studying information needs, conferring with users, and studying systems flow, data usage, and work processes.
* Understood process requirements and provided use cases for business, functional & technical requirements.
* Interacted with users for requirement gathering, prepared functional specifications and low-level design documents.
* Participated in the Software Development Life cycle (SDLC) and Agile methodology right from requirement analysis,
* Performed detailed design of modules along with their implementation, and documentation integrated software modules
Developed by other team members.

Highlights:
* Developed various modules as per customer requirement and identified and fixed number of bugs related to code, Database connectivity, UI Defects and so on.
* Analyzed and modified existing codes to incorporate a number of changes in the application / user requirements, wrote new codes as required.
* Coded, implemented and integrated complex programs using technologies such as HTML5, CSS3, JavaScript, jQuery, bootstrap.
* Having good command on Graphics designing with effective ideas.

PROJECTS

* www.nitka.com, Nagpur united corporation (admin), Mintmetrix.com, Tagline videos (admin), Smartbadge (admin): -
In all projects I have used technologies like HTML5, CSS3, Bootstrap, JavaScript, jQuery and text editor as sublime text.

* www.shreekiaspack.co.in, www.3staragroproducts.com, www.luckystationery.co.in: - used technologies like HTML5, CSS3,
Bootstrap, javascript and text editor as notepad++.

* Design various Logos, Brochures, Advertising Banners, Visiting Cards, Pamphlet, Hoardings etc.

B.E. FINAL YEAR PROJECT

* Major Project: -

Title: WEB BASED DISEASE DIAGNOSIS EXPERT SYSTEM.
Duration: 1 Year

Description: In this project we provide a website in which doctor gives online consultation for particular disease. System gives better suggestions for any health problems.

* Mini Project Development-

* SHOPPING MANAGEMENT SYSTEM Developed in C++.

CURRICULUM & EXTRA CURRICULUM  ACTIVITIES
company - FACE-IT
description - Co-ordinator in project competition."
Mechanical Engineer,"Education Details 
May 1999 to September 2002 Diploma Mechanical Engg Mumbai, Maharashtra Institute of Mechanical Engg
May 1998 to May 1999 Diploma Mechanical Engg. Services  ITES
May 1993 to May 1995   Mumbai, Maharashtra Industrial Training InstituteSr. Executive-Mechanical Engineering- Automation & Projects ConsultantSr. Executive-Mechanical Engineering- Automation & Projects Consultant - Mechanical EngineeringSkill Details 
Microsoft Office -Word ,Excel,Auto cad,Micro station J ERP 3d Modeling software- Exprience - 120 monthsCompany Details 
company - Mechanical Engineering
description - Role & Responsibilities: - Application Engineering / Pre Sales & Inside Sales â¢ Provide applications support to inside sales personnel and outside sales channels, Provide product selection and materials of construction technical recommendations.
â¢ Participate in the necessary training activities to establish technical competency & also Participate in Field Service trips as directed by Top Mgmt.
â¢ Assist Brand Managers and/or Product Managers as needed.
â¢ Takes active role as support for the Projects Quotations team, being responsible for the technical part of project quotation including selection, sizing and costing of Pneumatic Automation Products, valves, linear & rotary actuator and field Fabrication & equipment on the basis of the customer's data sheets and Engineering Drawing.
â¢ Travel as required to support the field sales channel and to promote Company products and services.
â¢ Perform detailed reviews of customer specifications, providing comments, clarifications and alternatives to customer requirements.
â¢ Sizing and selection of valves, actuators and prepare cost effective techno-commercial quotations, solutions as per customers technical requirements.
â¢ Coordinate with other technical departments as required, in order to minimize technical and cost risk.
â¢ Sending quotations accurately and on-time, including all technical and commercial as per customer documentation requests. Participate in the customer negotiation process, including commercial and technical negotiations.
â¢ Follow up of quotations to close the order also Follow up for payment collection.
â¢ Perform PO reviews to ensure that all critical elements are correct and take up to Senior Top managements and customer in case of any discrepancies, and get PO amendment if required.
â¢ Create the order transfer file for submission to the factory in accordance with PO. Support factory for any post order activities & after sales service where necessary to develop and maintain good customer service.

Role & Responsibilities: - Proposal/Estimation Engineering & Products Costing â¢ Review and receive the RFQs for company products and solutions.
 â¢ Estimation of Pneumatic Automation Products & Projects, valves, linear & rotary actuator and field equipment, Piping, Fabricated equipment's etc.
 â¢ Plotting enquiries to vendors for critical bought out items.
 â¢ Continuous follow up with vendors for quotations.
 â¢ Applying thumb rule or standard practice of design while estimating for achieving accuracy with less time.
 â¢ Analyzing & evaluating estimated cost and actual cost of the jobs already manufactured in past to arrive at a realistic cost estimates.
 â¢ Maintaining databanks of all quotations for readily available to others also.
Coordinating with Instrumentation & electrical dept. for costing.
 â¢ Handling estimation department while submitting offers on time. Visit to site for site projects, if required. Co-ordination with Purchase and process departments. Attending Customer Enquiries meeting. Development of vendors.
 â¢ Preparation of statement enquiries pending for estimation/ under progress â¢ Understand the client's concern / pain area / requirement, Offer most suitable & optimized the tailor made or predesigned Chemtronics solution with techno-commercial proposal which should be technically feasible & commercially viable.
â¢ Assign work to Jr. Proposal executives/Engineers â¢ Guide & Coordinate with design, CAD, sales, marketing, project & commercial department.

Role & Responsibilities: - Engg. Procurement & Purchase, Vendor Development & Supply Chain.
â¢ Procurement of Steel/Raw Material & semi-finish Products Based on Projects requirement.
 â¢ Continuous improvement in negotiation process & Cost saving opportunities.
 â¢ Turn round time improvement & new vendor development Contract Management & Tenders.
 â¢ To achieve target savings against budget and/or last purchase rates & develop new vendors. & Obtaining quotation and finalization.
 â¢ Research and evaluate potential vendors and suppliers, Request quotes and do technical comparisons of Prices, also maintain good relationships with vendors and suppliers.
 â¢ Examine and review products and supplies to ensure quality & digitize procurement processes.
 â¢ Track incoming & future purchasing plans, Projects inventory, delivery arrival time, and note actual arrival time Organize and update database of suppliers, delivery times, invoices, and quantity of supplies.
 â¢ Collaborate with financial team members on contracts, invoicing, and other financial matters 
â¢ Finding out the suppliers for buying various materials from products finders as well as from internet Source.
 â¢ Create RFQ for materials for projects, departments, for project materials, equipment's and services.
 â¢ Review Vendor evaluation & Vendor registration from Supply time & quality of products.
company - Proposal & Estimation
description - M/s Duncan Engineering Ltd, Pune, Ranjangaon MIDC

Role & Responsibilities:- â¢ Project execution, Vendor development, supply chain Management, interacting with Vendors to ensure timely action of order & negotiating with the vendor.
â¢ Work directly with customer to understand their process and develop the best solution For their requirement, lead proposal engineering team & technical support to customer.
â¢ Work with sales team to professionally represent the company at a customer, gather Data and ask the right question to define a project.
â¢ Work with our engineers and designer to develop and quote cost effective solution. Cost estimation, preparing proposal & quotation base on Technical specification of Customer requirement & identify production process and machines/brought out Item requirements. & co-ordinate with customer to understand exact requirements.
â¢ Identification of critical problem & work out Proper solution in specified time frames.
â¢ Production design-new manufacturing process - new product development. Developing
Customized manufacturing solutions. (Steel, Power & Cement, petrochemical sector.) 
â¢ Executing cost saving techniques/ measures and modifications to achieve

Substantial Reduction in O & M expenditures and work within the budget

Maintaining fruitful Relationships with existing customers.
company - Rotex Engineers & Manufacturing Pvt. Ltd
description - Nov 2015 - June 2016  Rotex Engineers & Manufacturing Pvt. Ltd Dombivali MIDC, Kaylan.

Role & Responsibilities:- â¢ To Manage & enhance the activity related to proposal of pneumatics/fabrication projects & products.
â¢ Project execution, Vendor development, supply chain Management, interacting with vendors to ensure Timely action of order & Negotiating with the vendor.
â¢ Production design-new manufacturing process - new product development. Developing
Customized manufacturing solutions. (Steel, Power & Cement petrochemical sector.) â¢     Preparation of technical data sheets as per tender specification.
â¢     Calculate price structure and analyze cost proposals.
â¢     Monitoring progress throughout the job & comparing it with the schedule of work.
â¢     Maintaining fruitful relationships with existing customers â¢   Review Vendor evaluation & Vendor registration from Supply time & quality of products.
 â¢   Examine and review products and supplies to ensure quality.
company - Design & Proposal Engineering
description - M/s Schrader Duncan Ltd, Pune, Ranjangaon MIDC

Role & Responsibilities:- â¢ Project execution, Vendor development, supply chain Management, interacting with
Vendors to ensure timely action of order & negotiating with the vendor.
â¢ Design or modify Mechanical assemblies, hydraulic/pneumatic assemblies, Fabricated assy
Along with layouts/schematics and/or detailed drawings as per specification.
â¢ Generate electrical or mechanical product specifications, standard operating procedures, maintenance manuals & Doing basic mechanical design, if required.
â¢ Participate in growth & developing innovative solutions with design & development, sales & marketing team.
â¢ Define, coordinate, perform and generate engineering test reports & engineering analysis.
â¢ In Co-Ordination with production manager implement and assure that all manufacturing processes are being followed according to process Flow in relevant Departments.
â¢ Maintain records of Non-Conformity (NC) raised during internal audits and close the same with corrective actions.
â¢ Maintain records of obsolete documents and remain in touch with all HODs.
â¢ Maintain ISO documents pertaining to ISO9001-2015 and bring awareness among employees.
â¢ Coordinate & communicate with channel partners, O.E.M.s, dealers, consultants & end customers for Design & offer technically feasible.
â¢ Carry Out internal audit as per audit plan and schedule, maintain the record of controlled drawings.
company - Fabrication Field-Hoist, Goods Lifts
description - JOIST- O- MECH Engg. Pvt. Ltd, Rabale MIDC, Thane

Role & Responsibilities:- â¢ Detailing of drawing, Preparations of BOM & raw material inspection.
 â¢ Prototype manufacturing (3d modeling)
company - Fabrication Field
description - 
company - MUKAND LTD, Kurla
description - Role & Responsibilities:- â¢ Raw material inspection, Preparations of Gauge for inspection, marking on housing for machining, checking surface finishing"
Mechanical Engineer,"SKILLS: â¢ Knowledge of software / computer: Auto CAD (Included Diploma Academic Syllabus) â¢ MSCIT â¢ CNC Programming (Fanuc Series) - milling STRENGTHS: â¢ Strong Dedication towards work. â¢ Quick Learner. â¢ Positive thinking and self-confidence. â¢ Honest, discipline and hardworking. INDUSTRIAL EXPERIENCE: â¢ 10 Day's Industrial Training at Nagpur agro Components, Hingna Road, Nagpur. â¢ 10 Day's Industrial Training at METAL FAB High-tech Pvt. Ltd, Hingna Road, Nagpur. EXTRA CRICULUM ACTIVITY: Education Details 
 B.E   RTMNUMechanical engineeringSkill Details 
AUTO CAD- Exprience - 6 months
AUTOMOTIVE- Exprience - 6 months
AUTOMOTIVE CNC- Exprience - 6 months
CAD- Exprience - 6 months
CNC- Exprience - 6 monthsCompany Details 
company - Tata motors, adani, maruti suzuki,jet airways. Air india.
description - 1. I am student of mechanical engineering in the final year. I'm fresher so don't have any experiance in industry.
2. But in 3 rd year the industrial training program conducted by the so 10 days training experiance in the industry.
3. In the 5 th semister the 10 days working in Nagpur Agro Components. While the working all the machine and CNC machine i have seen in industry.
4. In 6 th semister another training program in Metal fab company.
company - No
description - English, Hindi, Marathi

â¢ Participate in JSW Urja Project Competition 2017-2018
â¢ Machine Assembly Disassembly and Functioning of parts (1 Weeks)

FINAL YEAR PROJECT:

â¢ Design nd fabrication of Pneumatic Punching Machine - in BE
â¢ Paddle operated Sugarcane Juicer machine - in Diploma Engineering."
Mechanical Engineer,"Education Details 
January 2018 Bachelor's of Engineering Engineering Mumbai, Maharashtra MGM College of Engineering
 Diploma Mechanical Pune, Maharashtra MITPresident of Mechanical Engineering Student's AssociationPresident of Mechanical Engineering Student's AssociationSkill Details 
Company Details 
company - Full Throttle
description - conducted by IIT
Bombay.

â¢   Worked as a President of Mechanical Engineering Student's Association [MESA] in
MIT, Pune.
company - R.C Car Race of STEPCONE
description - paper & project contest and exhibition conducted by GMR Instiute of Technology."
Mechanical Engineer,"Education Details 
June 2014 to June 2018 BE Mechanical Engineering Pune, Maharashtra Savitribai Phule Pune UniversityMechanical Design EngineerMechanical design engineerSkill Details 
Microsoft office, Autocad, Catia, Solidworks- Exprience - 6 monthsCompany Details 
company - Push Engineering Pvt ltd
description - Currently I am working as mechanical design engineer in Push engineering Pvt ltd. All types of ice making plant are designed and manufactured in our company. Design includes design of pressure vessel ,condenser, conveyor with the help of 3d modelling software. Also BOM Preparation of material.ERP System.
company - Central Water and Power Research Station
description - Pune)
(Govt. of India)

1 year experience as a Research assistant of R&D Department of mechanical workshop at
Central Water and Power Research Station, Khadakwasla, Pune (Govt. of india)
â¢ Experiences different project of generation of sea waves in laboratory useful for design of port and harbor structures of Mumbai, Chennai, Goa and foreign countries like Japan, Bangladesh etc.
â¢ Wave generated by wave maker comes under mechanical workshop. Wave generated with the help of hydraulic as well as pneumatic system and also by regular wave generator.
â¢ I experiences sea wave generation by hydraulic system. Experiences design of hydraulic system
includes design of servo actuator, axial piston pump and electric motor under supervision and guidance by 'Scientist 'B' at CWPRS.

â¢ SOFTWARES SKILLS
â¢ Autocad
â¢ Catia V5
â¢ MS office
â¢ ProE
â¢ EXTRA CURRICULAR ACTIVITY
â¢ Organize a National level Technical event in Mechanical Student Association (MESA)
â¢ Coordinator of MESA as a treasurer.
â¢ Attending 3d printing workshop in college.
â¢ Work readiness training conducted by NASSCOM foundation and Global Talent Track in college.
â¢ Life Skills training conducted by NASSCOM foundation and Global Talent Track in college.

â¢ PERSONAL ATRIBUTES
â¢ Positive Attitude
â¢ Quick Learner
â¢ Team Leader"
Mechanical Engineer,"* I'm hard working person. * I'm self confident and can mould myself to all work environments.Education Details 
January 2016 B.E MECHANICAL ENGINEERING  ALAMURI RATNAMALA INSTITUTE OF ENGINEERING & TECHNOLOGY
January 2010 H.S.C.  Mumbai, Maharashtra MUMBAI UNIVERSITY Secured
January 2008 S.S.C.   PRAKASH Jr. COLLEGE OF SCIENCE & COMMERCE
    S.M.T. RAJDEVI HINDI HIGH SCHOOL
    MAHARSHTRA STATE BOARD SecuredDesign Engineer (Mechanical)Skill Details 
Autocad, solidworks, catia- Exprience - 12 monthsCompany Details 
company - SANARCO ENGINEERING PVT LTD
description - * 1year in SANARCO ENGINEERING PVT. LTD. As a mechanical design engineer.
* Working in SURYA FITNESS as a design engineer.

Major project:
* Project based on  AUTOMATIC WALL PLASTERING MACHINE.
* Automated plastering machine is unique and perhaps one kind of automated plastering machinery ideally suitable for the construction/building industry.
* Automated plastering machine makes rendering easier, faster, and effortless as compare to manual application.
* Automated plastering machine works with conventional cement mortar which brings it to a smooth, flat finish.
* It has good future scope.

Personal Detais:
* Father's Name: Gyandatt Chauhan
*                              
*                            
*"
Sales,"Education Details 
 Bachelor's   
 Bachelor's Commerce India Guru Nanak high schoolSales ManagerSkill Details 
Data Entry- Exprience - Less than 1 year months
Cold Calling- Exprience - Less than 1 year months
Sales- Exprience - Less than 1 year months
Salesforce- Exprience - Less than 1 year months
MS Office- Exprience - Less than 1 year monthsCompany Details 
company - Emperor Honda
description - 
company - Honda cars india Ltd
description - 1. Worked as an Asm at Maruti dealership for 10 years
2. Currently working as Manager sales in Honda car dealership from last 5 years
3. Good sportsmen represent my college in various cricket  tournaments
4. Lead Nagpur university cricket team also 
5. Searching job in car dealership or cricket academy"
Sales,"SKILLS 1. MS-Office 2. Good Communication skills and Convincing Power 3. Knowledge of sales and marketing 4. Customer sales management 5. Talent management 6. Direct sales management STRENGTHS 1. Dedication and dependability 2. Flexibility: able to work in taxing and demanding conditions on reasonably stretchable time slots 3. Team work: able to work cooperatively in a team and lead the team to success Education Details 
Sales managerSkill Details 
AND MARKETING- Exprience - 6 months
DIRECT SALES- Exprience - 36 months
MARKETING- Exprience - 6 months
OF SALES- Exprience - 36 months
SALES- Exprience - 36 monthsCompany Details 
company - Bajaj finance
description - 
company - Bajaj finance
description - SBI BANK AS A OFFICE ASSISTENCE FOR 1 YEAR
K G N SHOPE AS A OFFICE ASSISTENCE 6 MONTH
VODAFONE AS A TELLE CALLER 1 YEAR
Bajaj Finserv Ltd AS An Assistant Sales Manager 11 MONTH
Square Capital AS A Sales Manager 5 MONTH
At present working in Bajaj Housing Finance Ltd AS An Assistant Sales Manager"
Sales,"KEY SKILLS: â¢ Planning & Strategizing â¢ Presentation skill â¢ Client relationship â¢ Energy level â¢ Enquiry Generation â¢ Achieving Targets QUALIFICATIONS: A university in marketing or business studies is preferred or a minimum of three years of related experience in sales & marketing sector. Problem - solving and analytical skills to interpret sales performance and market trend information. Proven ability to motivate and lead the sales team. Experience in developing marketing and sales strategies. Excellent oral and written communication skills, plus a good working knowledge of Microsoft Office. Computer KNOWLEDGE â¢ Knowledge of MS Excel, MS Word, MS PowerPoint achievements and Interests â¢ I played Cricket for National Team (Maharashtra Cricket Association) â¢ Played Regional level Cricket Tournament Thee times for School team â¢ Worked as a Sports Secretary in college annual meet. â¢ Worked as a volunteer for road show in POONA College. â¢ Worked as a Group leader for college presentation. â¢ My Interest are Learning various computers languages & Tricks and Techniques of computer and Playing Cricket. personal Information . Education Details 
 MBA Operations  Dr. D. Y. Patil College
 B.B.A. Marketing Pune, Maharashtra Poona College
 H. S. C.   Moledina high School & Jr. College
 S. S. C. Maharashtra Board  A.M.V.High SchoolSales managerSales ManagerSkill Details 
SALES- Exprience - 104 months
MARKETING- Exprience - 97 months
SALES TEAM- Exprience - 44 months
AND SALES- Exprience - 6 months
EXCEL- Exprience - 6 monthsCompany Details 
company - F2 Fun Fitness
description - â¢	Set individual sales targets with sales team.

â¢	Handling Enquiries.

â¢	Generating new enquiries

â¢	Set individual sales targets with sales team.

â¢	Continuously managing team performance.Â 

â¢	Managing staff training requirements.Â 
Â 
â¢	Generate timely sales reports.
â¢	Organizing seasonal promotions and events.

â¢	Supervise and motivate staff.

â¢	Â Â Holds regular meeting with sales staff.
company - Gold's Gym India Pvt Ltd
description - JOB PROFILE:

â¢ Set individual sales targets with sales team.

â¢ Handling Enquiries.

â¢ Generating new enquiries

â¢ Set individual sales targets with sales team.

â¢ Continuously managing team performance.

â¢ Managing staff training requirements.

â¢ Generate timely sales reports.
â¢ Organizing seasonal promotions and events.

â¢ Supervise and motivate staff.

â¢ Holds regular meeting with sales staff.
company - 
description - 10 to 24/2012
Achievements: Joined as a sales associate in 2010. Promoted to Sales Manager 2012.
Exceeded sales targets with high level of contribution & dedication to the organization.
Applauded for Best sales & marketing performance in Pune zone & Awarded the
â¢    Certificate of Excellence & outstanding performance in the year 2014. Ranked as #1 sales manager (out of 6) in 2013 and 2014. Recognized for superior performance as a two-time district ""Employee of the Month"" honoree.

JOB PROFILE:
â¢ Build strategies and develop marketing initiative to create awareness of company services.
â¢ Propose and execute the promotional programme to attract clients.
â¢ Working with the team of four - six people thereby ensuring that targets defined are achieved.
â¢ Arrange all the necessary & possible facilities for information desk.
â¢ Manages personal and develops sales roles support staff.
â¢ Reviews progress of sales roles throughout the company.
â¢ Determine price schedules and discount rate.
â¢ Generate timely sales reports.
â¢ Control expenses and monitor budgets.
company - Talwalkars Aspire Fitness Pvt Ltd
description - 
company - Talwalkars Aspire Fitness Pvt Ltd
description - "
Sales,"IT Skills: MS Office. Photoshop. SQL Server.Education Details 
June 2015 Bachelor   Yashwantrao chavan maharashtra universitySales ManagerSales ManagerSkill Details 
MS OFFICE- Exprience - 6 months
MS SQL SERVER- Exprience - 6 months
PHOTOSHOP- Exprience - 6 months
SQL- Exprience - 6 months
SQL SERVER- Exprience - 6 monthsCompany Details 
company - Nature Of Work
description - Generating leads on calls, Suggesting Residential property as per clients budget & specification, building Relationship with Channel Partner.

â¢  Meeting with clients for project presentation follow for up Site Visit
Deal closure.

Worked with ICICI Merchant services (Hgs Payroll) As Sales Manager. (16th Jan 2017 to till 30th Aug 2018)

Nature of Work:
â¢ Sales OF POS EDC Machine, SME Loan, Loan against electronic payment.
Acquiring New Merchants And retaing existing Merchants.
â¢ Cold calling With CASA Team (ICICI BANK) Generating leads.
â¢  Field Visit Providing demo to Retailers shopkeeper SME & Corporate merchant for Closing & Signup.

Worked with Mahal Pvt Ltd. as Sr. Sales Executive. (04th Mar 2016 to 03 Jan 2017)
Nature of Work:
â¢ Calling on leads gegenerat Interacting with clients on call & suggesting property Flat/Bunglows/N.A Plots & Row houses as per their requirement.
â¢ Meeting new channel partners & maintain relationship with channel partners for leads.
â¢ Achieving monthly targets, Maintaining reports & updating seniors of daily activities.
company - Bookmyflat.Com
description - Arranging Site visit with client on lead generated
â¢  Closing deal by suggesting appropriate Residential property to clients as per their budget."
Sales,"Skill Sets: â¢ Multi-tasking â¢ Collaborative â¢ Optimistic Thinking â¢ Effective teamleader/team trainer â¢ Visualizing the work which is to be done â¢ Good Grip on Communication â¢ Various Languages Known â¢ Value loyalty and is loyal towards my responsibility â¢ Compatible working with MS officeEducation Details 
January 2017 MBA Marketing & Sales  Amity University
January 2015 Bsc. Hotel Management  P.S.G College of arts and sciences
    Institution/University/BoardSales ManagerSales and Marketing ManagementSkill Details 
MS office- Exprience - 4 monthsCompany Details 
company - Cohesive Technologies
description - I am responsible for Managing company's business in Mumbai area,along with branch Manager,I follow up leads given to me by my corporate office, I meet them personally give them the best solutions and product suitable for their business, I even generate leads by myself by calling up the clients through cold call,My job is like an entrepreneur here, which basically involves managing a small business."
Health and fitness,"Education Details 
January 1992 to January 2003 First year Science Mumbai, Maharashtra St micheal highPersonal fitness trainer level3personal trainerSkill Details 
Company Details 
company - Golds gym ,fitness solution,flora hotel
description - Certification : american college of sports science golds gym heart saver reps level 3

Responsibilities
To obtain a challenging position which will commensurate with my qualification and experience in the field of health and fitness environment.


Accomplishments
Good



Skills Used
Fitness"
Health and fitness,"Education Details 
January 2018 M.S. Nutrition and Exercise Physiology New York, NY Teachers College, Columbia University
January 2016 B.S. Nutrition and Dietetics Miami, FL Florida International University
January 2011 B.Sc. General Microbiology Pune, Maharashtra Abasaheb Garware CollegeGroup Fitness Instructor, IndiaGroup Fitness Instructor, India - Columbia UniversitySkill Details 
Company Details 
company - Columbia University
description - Present
Organized high energy weight training, cardiovascular and indoor cycling classes
accommodating participants of varying age-groups, cultural backgrounds and fitness levels to help achieve their fitness goals.
company - Columbia Dental School
description - Provided detailed nutrition counselling and telephonic follow up to dental patients with accompanying metabolic conditions like diabetes, hypertension and obesity."
Health and fitness,"Personal Skills: â¢ Good verbal and written communication skills â¢ Ability to deal with people diplomatically â¢ Willingness to learn Other Qualifications: â¢ Seema Institute Ansalon: Body massage and scalp, Swedish, Thai body, Foot Reflexology, Aromatherapy. Completed course of 2 months. â¢ Talwalkars: Fitness academy with gym and aerobics. Completed 3 months course in fitness training. â¢ Radio Bhavan, The Career Super Market: Hotel and Front Office Management. Completed course of 3 months. â¢ Slender Gender: Done diploma in Spa Management, (4 Months)Education Details 
Fitness Trainer cum Team LeaderFitness Trainer cum Team Leader - Think HealthySkill Details 
Company Details 
company - Think Healthy
description - Job Responsibilities:
â¢ Perform related duties and responsibilities as assigned.
â¢ Achieve Fitness Goal
â¢ Training people to do work outs in Gym. Handling customer s queries related therapies, Refreshers, etc
company - Raymond Gym
description - Job Responsibilities:
â¢ Perform related duties and responsibilities as assigned.
â¢ Handling customers  queries related therapies
company - AROMA THAI FOOT SPA
description - Perform related duties and responsibilities as assigned.
â¢ Handling customers  queries related therapies, Refreshers, etc
â¢ Handled queries on phone as well as personally.
â¢ Taking care of payment Invoices.
company - GLOBAL 1 GYM
description - Job Responsibilities:
â¢ Perform related duties and responsibilities as assigned.
â¢ Handling a team, entire floor section
â¢ Achieve Fitness Goal
â¢ Training People to do work outs in Gym
company - RUDRAAKSH HEALTH CARE & SPA
description - Job Responsibilities:
â¢ Perform related duties and responsibilities as assigned.
â¢ Handling a team, entire floor section.
â¢ Achieve Fitness Goal.
â¢ Training people to do work outs in Gym. Handling customer s queries related therapies, Refreshers, etc
â¢ Handled queries on phone as well as personally.
â¢ Taking care of payment Invoices.
company - PLAZA GYM
description - 
company - HOTEL Windsor
description - 
company - HOTEL HOLIDAY INN, Juhu
description - Done Promotion: For Aromathai Foot Spa. 6 days (World Trade Centre, Bombay Exhibition Centre, Also done promotion at Bandra Kurla Complex, Bandra GYM Khana ( Promoted various products: Spa, Health and Fitness Product, Garments, Imitation Jewellery, Food & Beverage etc)"
Health and fitness,"SKILLS: Computer: â¢ Can easily operate in Operating System like Windows 10. â¢ Can work in Ms-Office (Word, Excel, and PowerPoint). â¢ Can easily operate internet & Gym Management Software (website & mobile application).Education Details 
January 2010 Advance Diploma in Hotel Management   J&W Institute of Hotel ManagementGym management & ConsultantGym management & Consultant - Sculpt Fitness CentreSkill Details 
Excel- Exprience - 96 months
PowerPoint- Exprience - 96 months
Word- Exprience - 96 months
Gym Management Software- Exprience - 96 monthsCompany Details 
company - Sculpt Fitness Centre
description - 2015 - Present	Sales, Fitness, Operations
company - AB's Fitness Club
description - Fitness, Sales, Administration, Facility & House-keeping
company - AB's Fitness Club
description - Sales, Fitness, Operations
company - Ultimate Fitness Club
description - 
company - Tripod Fitness Arena
description - Sales, Administration
company - C K Fitness
description - Sales & Fitness Staff Recruitment

KEY ROLES:
â¢ Designing and promoting activities to meet customer demand and generate revenue;
â¢ Advertising and promoting the club or Centre to increase usage, considering market research;
â¢ Recruiting, training and supervising staff, including managing staff rotas;
â¢ Taking Daily/weekly/monthly reporting from Fitness Manager, Sales Team Lead And Housekeeping Head;
â¢ Carrying out health and safety checks on the equipment and site;
â¢ Retaining existing members by providing good quality of service and by providing good sales programs;
â¢ Prioritizing target activities and user groups (especially in local authority Centres);
â¢ Customer Relationship;
â¢ Providing training to the sales staff to Deal with enquiries, complaints and emergencies
â¢ Delivering some fitness training or coaching in sports activities - often a good way of maintaining contact with customers;
â¢ Preparing and checking budgets and generating revenue;
â¢ Cashing-up and keeping stock records of supplements;
â¢ Utilizing Group-X studio by scheduling exciting grouper's batches;
â¢ Writing monthly or weekly reports and preparing cash projections for Centre owners;
â¢ Enhancing profitability by organizing and delivering an appropriate range of fitness activities/programs;
â¢ Keeping statistical and financial records;
â¢ Keeping check on the Maintenance of fitness equipment & other facilities;
â¢ Ensuring compliance with health and safety legislation;
â¢ Maintaining customer service, Fitness & Hospitality standards;
â¢ Planning;
â¢ Undertaking administrative tasks;
â¢ Promoting and marketing the business;
company - Endurance Fitness Club
description - Fitness, Sales, Administration, Facility & House-keeping"
Health and fitness,"Education Details 
January 2009 P.G. Sports science  Dr. BMN College of Home Science
January 2008 BSc Food Science & Nutrition  Dr. BMN College of Home Science
January 2004 HSC   Central Railway's Jr. college of Science and Commerce
January 2002 SSC HISTORY  IES's Modern English School
 MSc Nutrition  College of Home Sciencenutritionist and health promoter9yrs experience as a nutritionist and health coachSkill Details 
Company Details 
company - UGC NET
description - CAREER OBJECTIVE- To use my qualifications as a nutritionist and health promoter to contribute to the well being of individuals in the community.
UGC NET Certificate for Lectureship MAR- 2013

SKILLS LEARNT
â¢ Working in a team environment.
â¢ Working as an individual.
â¢ Time management to ensure all customers are served sufficiently and all tasks are completed to a high standard.
â¢ Working under pressure and in a busy environment
â¢ Personal presentation
Communication Skills
â¢ 9 years of work experience at healthcare startups, health clubs has enhanced my communication skills through discussions with health & fitness professionals & clients, writing articles and designing customized diet plans.
â¢ Confident in communicating with people on all levels and ensuring their needs are met.
Leadership skills
â¢ Leadership skills were developed in high school & college as a group leader where team work was essential, also conflict resolution skills were developed.
Oral Skills
â¢ Volunteering activities, leadership roles and university requirements involved many public speaking opportunities.
â¢ Communicating with the general public, asking them questions, and giving presentations has helped to further develop public speaking skills.
â¢ Comfortable with public speaking roles."
Health and fitness,"Education Details 
May 2014 Diploma Nutrition Education Bengaluru, Karnataka IGNOU University
June 2004 Bachelor of Science Clinical Nutrition and Dietetics Bengaluru, Karnataka Smt. VHD Institute of HomeScienceHealth and wellness coachAdvance PG diploma in Clinical research, Clinical data management and SASSkill Details 
Company Details 
company - GOQii Technologies Ltd
description - â¢ Motivate players (i.e. GOQii subscribers) to make a permanent shift to a healthier lifestyle and enhance health, nutrition and karma
â¢ Track and analyse player's lifestyle and fitness data captured through the GOQii band.
â¢ Design custom wellness/fitness goals mutually with players and coach them in meeting these goals.
â¢ Interact with GOQii Players on a daily basis via GOQii App text/audio.
company - 
description - VIA Health Marketing & Innovations

â¢ Viamedia health had jointly organized a National Campaign of Maternal Nutrition Evaluation with GlaxoSmithKline-Consumer Healthcare.
â¢ Conduct Maternal Nutritional Counselling Camps with practicing Gynaecologists at different hospitals/clinics allotted by the medical representative.
â¢ Conveying the Importance of every nutrient required during this time and foods that contain this type of nutrients.
â¢ Coordinating with the Area Branch Manager and in turn reporting to the ViaMedia Manager with the daily reports.
company - LifeKonnect Department
description - United Health Care India Pvt. Ltd

â¢ LifeKonnect business offers Health Checkups across 800 Cities & 3000 Diagnostic Centres and leaders in this business and also looks into the Operations & organising & supervising medicals for customers of different Insurance companies whose medical come under the rule & regulation of IRDA ( MNYL Iinsurance, BAXA life Insurance, METLIFE insurance, Canara HSBC insurance.
â¢ To proactively monitor the deferrals and ensure that they are done within the stipulated time line.
â¢ Ensuring a high level of professionalism through pro-active Customer relationship management by resolving their queries and complaints in a fair manner within the specified time.
â¢ Co-ordinating with the Lifekonnect members from different centres and in turn reporting to the Team leader.
company - VLCC HealthCare Ltd
description - Counselling to individuals on nutrition status, providing diet instructions to clients and impact on eating & nutrition. Create and present in-services to the clients.
â¢ Handling enquiries from Customers / Distributors through emails, letters, telephone etc.
â¢ Preparing Payment Bills for the customers.
â¢ Assisting the Slimming Manager in conference calls to discuss Nutritional care plans, and Nutrition education.
â¢ Co-ordinating with the sales members and in turn reporting to the Sales Manager."
Civil Engineer,"Education Details 
 B.E in Civil Engineering Civil Engineering Chennai, Tamil Nadu Anna UniversityQA/QC Civil EngineerQA/QC Civil EngineerSkill Details 
Company Details 
company - Ray Engineering Limited
description - Mumbai, India.
Consultancy: Jacobs Engineering India Limits. (United States Company)
Client: Glaxo Smith Kline (GSK), Nashik, India.
Position: QA/QC Civil Engineer.

Project Summary:
Glaxo Smith Kline (GSK) is British multinational pharmaceutical Company in India. USD 150 million Building project of GSK.
The Project involving in Medicine Manufacturing plant, Effluent Treatment plant
(ETP), Effluent Drain line and storm water line.

Duties & Responsibilities:

â¢	All site inspection shall be carried out vise an official Inspection and Test Request
(ITR) submitted by the Contractor through the proper channels
â¢	Develop method statement for the activity including risk assessment and job safety
environmental analysis and Inspection Test Plan and Checklist based on specifications of the project
â¢	Taking care of QA/QC documents of the entire project including certificates,
calibration, test results, inspection requests, non-compliance reports and site
instruction/observations, permanent materials delivered and other QA/QC
documents.
â¢	Supervision, checking and assuring that the construction is as per the final issued for construction drawings and project specifications.
â¢	Conducting joint inspection with consultant and client.
â¢	Monitor and control the status of punch list and exception lists relevant to quality
dossiers.

3|Page
Career Progression: March 2016 to Still on going

Company name: Gulf Asia Contracting Co LLC Dubai, UAE
Consultancy: Chawla Architectural & Consulting Engineers
Client: Geepas International ltd, UAE
Project Name: 2B + G+ 19 + Roof Building
Position: QA/QC Civil Engineer.

Project Summary:
Geepas Tower, an upcoming development in the UAE, entered the Guinness World
Records along with the project contractor Gulf Asia Contracting (GAC) - the construction
arm of the multi-billion-dollar business conglomerate RP Group - for completing the largest
continuous concrete pour (of 19,793 cu m for 42 hours) in the world.

The Dh600-million ($163 million) mid-rise residential tower, which will come up in Al Barsha, Dubai, is owned by Western International Group (Geepas)

Duties & Responsibilities:

â¢   Organize, implement, conduct and manage the QA/QC Programs as per the Company's Quality Policy.
â¢   Coordinate the document controls of technical submittals, drawings, etc with the Project Team and to ensure that the QA/QC validation has been done, to ensure their
issue, amendments and recall of controlled documents.
â¢   Coordinate all inspections, monitor the required tests and record inspections and tests made pas the Contract Plan and Contract Specifications.
â¢   Liaise with the Client's representatives for the conduct of day-to-day quality related
project activities.
â¢   Coordinate with the Project Manager regarding work performance and hold authority
to stop work in any area where discrepancies remain uncorrected and/or cancel the stop work order upon satisfactory correction of noted deficiencies.
â¢   Provide technical support to the Project Manager and guidance to site staff in submission of materials for approval, request for inspection, shop drawings, As-built
drawings, O & M manual.
â¢   Assist QA/QC Manager in conducting internal quality audits.
â¢   Ensure that Non-Conformance Report (NCR) are raised once the activities deviated
from the Contract Specifications or normal construction industry practices.

4|Page
â¢   Coordinate with the project staff to compile and maintain pertinent records of inspection and testing until end of the contract and all pertinent records for retention
on completion of the project.
â¢   Attend Monthly Project Review Meetings (PRM) and alert the project team of any
potential problems.
company - Ray Engineering Limited
description - Mumbai, India.
Consultancy: Toyo Engineering India Limits. (Japan Company)
Client: Petronet LNG Limits, Dahej, Gujarat, India.
Position: QA/QC - Civil Engineer.

Project Summary:
Petronet LNG (Liquefied Natural Gas) ltd, India's biggest gas's importer Plant to hike its Dahej importer terminal capacity to 17.5 million tons.
Petronet LNG ltd has set up its first LNG terminal at India with the Capacity of
10 million metric tons per year.
A USD 400 million Petronet project for the Indian Govt. The Project Involving in Air heater, Pipe rack, Substation, Pump House and Control room.

Duties & Responsibilities:
â¢   Taking care of QA/QC documents of the entire project including certificates,
calibration, test results, inspection requests, non-compliance reports and site
instruction/observations, permanent materials delivered and other QA/QC
documents. Responsible for closure of Non-conformance, and Site Instruction.
â¢   Management System Maintain standards of safety and comply with Company's
Health, Safety and Environment requirements.

2|Page
â¢	Perform all daily inspection and test of the scope and character necessary to achieve the quality of construction required in the drawings and specifications for all
works under the contract performed ON or OFF site.
â¢	Cary out inspection and checking for all quality related procedures in the site and ensures activity at site are as per approved method statement and inspection test
plan.
â¢	Coordinate with the consultant's representative and Site In-charge for inspection
and meeting about quality problems including closure of Non-Compliance Report.
company - High View Builders. Kerala. India
description - Project Name: G + 8 Floor Building

High View Builders has been playing a major role in implementing new lifestyle in Kerala. From its inception, High View Builders strictly follows the aspects like on-time delivery,
quality and trust that turned out of the brand of construction. The project involved in construction of villas and G + 8 Floor flats.

1|Page
Duties & Responsibilities:

â¢   To ensure understanding and application of all responsibilities with regard to company's
environment, Health, Safety, Security and quality standard.
â¢   Ensuring the required materials and PPE's are available to carry out the job smoothly
and safely in advance.
â¢   To be proactively involved in supporting the project engineer throughout the duration of project.
â¢   To maintain an accurately detailed daily site reports for the site conditions, progress
and resources available to undertake the activities throughout each phase of the project.
â¢   Coordinating with sub-contractors and suppliers at site.
â¢   Co-coordinating with employees for the timely completion of the work at site.
â¢   Deploying the workers at the site as per the schedule and maintain the labor cards.
â¢   Conduct weekly meeting with the project engineer to discuss the work progress."
Civil Engineer,"Education Details 
January 2005 S.S.C  Baramati, Maharashtra M.E.S. Highschool
 B.E. Civil Engineering Pune, Maharashtra Singhad Institute Of Technology And Sciencecivil site engineerCivil engineerSkill Details 
Site Engineer- Exprience - 60 monthsCompany Details 
company - Vasundhara nirmiti properties
description - Civil engineer
company - shri balaji housing company
description - Execution of all civil work
company - Ganesh construction
description - Site engineer"
Civil Engineer,"SKILLS â¢ 1.Autocad â¢ 2.Pro v â¢ 3.Catia â¢ 4.word, excel â¢ 5.Photoshop INDUSTRIAL EXPOSURE Industrial Visit at: â¢ Larsen & Tubro (L & T)-3 months industrial training Inplant Training at: â¢ Ashoka Buildcon-Training for infrastructure setup and maintenance.Education Details 
January 2017 Masters in structure  Nashik, Maharashtra Universal University NashikCivil EngineerCivil EngineerSkill Details 
Autocad- Exprience - 1 months
Catia- Exprience - 6 months
maintenance- Exprience - 48 months
photoshop- Exprience - 1 months
training- Exprience - 6 monthsCompany Details 
company - Color Sky Decor LLC Dubai
description - Role: Civil Engineer
company - Girija Construction And Devolopers
description - Role: Civil Engineer
company - Showrooms, Villa's, Hotels
description - â¢ 1.Exterior and Interior Of buildings, villa's etc.
â¢ 2.Construction of new building and villa's.
â¢ 3.Preparing bill of quantities, scheduling etc.
company - Showrooms, Villa's, Hotels
description - Maintenance and Interior outfit works of apartments, Showrooms, Villa's, Hotels etc.
Description: 1 Analyze photographs, drawings and maps to inform the direction of projects as well as the overall budget constraints

2 Ensure project feasibility through continual evaluation of structural integrity and design
practicality

3 Create schedule for project completion

3 Perform and adjust quantity calculations for practical and budgetary purposes

4 Communicate with team members as well as customers and vendors to ensure
maximum cohesion and fluidity on projects

5 Forecast design and construction time frames

6 Inspect project sites to ensure they meet relevant codes and are progressing properly
company - Sonia Buildcon
description - Role: Civil Engineer
company - 
description - 8.day-to-day management of the site, including supervising and monitoring the site labour
force and the work of any subcontractors
Duration: 2015-2016
Role: Civil Engineer
â¢ Construction, renovation, interior designing of buildings, malls, commercial complex, Villas etc.
Description: 1 Undertaking technical and feasibility studies including site investigations

2 Using a range of computer software for developing detailed designs

3 Undertaking complex calculations

4 Liaising with clients and a variety of professionals including architects and subcontractors

5 Compiling job specs and supervising tendering procedures

6 Resolving design and development problems

7 Managing budgets and project resources

8 Scheduling material and equipment purchases and deliveries

9 Making sure the project complies with legal requirements, especially health and safety

10 Assessing the sustainability and environmental impact of projects

11 Ensuring projects run smoothly and structures are completed within budget and on time
company - Grandeurs Realetors India
description - Role: Civil Site Engineer
company - 
description - Description: <Responsibility>
1.Evaluated overall cost of materails, labours, subcontractors and tools.

2.Regulated subcontractor work schedule and equipement delivery.

3.Provided required documents for owners and subcontractors.

4.Cheack regular basis project progrees and devolopment.

5.act as the main technical adviser on a construction site for subcontractors,
craftspeople and operatives.

6.ensure that all materials used and work performed are in accordance with the specifications.

7.liaise with any consultants, subcontractors, supervisors, planners, quantity surveyors
and the general workforce involved in the project.</Responsibility>"
Civil Engineer,"Computer Skills â¢ Holder of valid KSA license â¢ Basic Computer â¢ MS Office â¢ Autocad 2006Education Details 
January 2005 Diploma  Bengaluru, Karnataka Oxford polytechnic BangloreCivil EngineerCivil Engineer - Utility powertech LtdSkill Details 
Company Details 
company - Utility powertech Ltd
description - 1. Project Name          Nabinagar thermal power
Project Aurangabad Bihar

Client	NTPC/BRBCL

Position          Civil Engineer
company - DAMMAM KSA
description - October 2008 to June 2011                                          AL NAFJAN & AL ABAD CO.

DAMMAM KSA
Position          Civil Engineer
Project Undertaken:
1. Project Name: STC & QATIF S/L project no 2065864
Client: Department of civil aviation
Main Contractor: Al Nafjan & Al Abad co.
Consultant: Arif & Kinfolk

2. Project Name: King Fahad International Airport
Client: Department of civil aviation
Main Contractor: Al Nafjan & Al Abad co.
Consultant: Dar-Al-Hadassah

Project Description: DPF have Eleven Buildings. They are building 1 to building 11 all buildings are typical but it has three type. They are type 1, type 2 and type 3. Basically these building are for the senior staff for Saudi Arabian police force & army accommodation of Saudi Arabia.

Job Responsibility
â¢ Planning of projects from study the drawings and contract specifications.
â¢ Preparation of material submittals and follow up with consultant for approvals.
â¢ Planning of site installation activities, mobilization of man power, material on site, start up of the projects by leading team of supervisors, foremen & workers.
â¢ Monitoring and supervising the execution of equipment installation at site and guide the work force to complete the project at targeted time.
â¢ Co-ordination with clients, consultants & contractors.
â¢ Preparation of Shop drawings and subsequently As-Built Drawings.
â¢ Preparation of RFI.
â¢ Billing, variation claims, timely order realizing on approval of materials, technical submittals.
â¢ Attending site coordination meetings and dealing with clients & contractors.
â¢ Preparing the work progress for the execution of the projects, preparation of weekly & monthly progress reports

â¢ To prepare the progressive material take off for quantity of Ducting, Chilled Water Piping, Fittings, Insulation, etc. from workshop drawing as required according to approved work program.
â¢ To prepare a complete material take off from workshop drawings for procurement.
company - N.S Padke
description - urban project under MMRDA
Andheri (E), Mumbai
company - PBA Infrastructure Ltd
description - Project Undertaken:
1. Project Name: 8 leaning of tipu sultan chok to santnamdev chok
Client: Mumbai urban project under MMRDA
Andheri, Mumbai
company - Govt.ofJ&K
description - Jammu)
Client: Govt.ofJ&K
Period: 7 Dec 2006 to 20 March 2007

Job Profile
â¢ Obtaining approval of Material and Drawing submittal from Consultant and main contractor.
â¢ Material takeoff as required for the project according to the workshop drawing.
â¢ Making request to purchase department for the procurement of the above material according to the project plan.
â¢ Coordination with Electrical, Plumbing and Civil contractor for various site requirements.
â¢ Arrange inspection of installed work and on the completion of the project.

â¢ Estimating.
company - civil aviation
description - 
company - National highway authority of India
description - 1. Project Name:
Client: National highway authority of India
Paintha chock to paripora Sri Nagar
Govt.ofJ&K"
Civil Engineer,"COMPUTER KNOWLEDGE â¢ Drafting tools: AutoCAD. â¢ Packages: MS Office Applications â¢ Operating systems: Windows 9x/2000/XP/Vista. DETAILEXPERIENCES Experience in India: 1) Working Experience at ALIA CONSTRUCTION AT NALLASOPARA (WEST) Mumbai since JUNE 2014 to MAY 2015. As a Civil Engineer (1 years) Project handle: Project title: YASHWANT ORICHIDS Position: Site Engineer Location: Nallasopara (W) Mumbai Maharashtra - 400097 IndiaEducation Details 
 S.S.C Rajasthan board  excellence polytechnic collegeCIVIL ENGINEERCIVIL ENGINEER - Site EnchargeSkill Details 
AutoCAD.- Exprience - 9 months
Civil Engineer- Exprience - 42 months
Drafting- Exprience - 9 months
Engineer- Exprience - 42 months
MS Office- Exprience - 9 monthsCompany Details 
company - Site Encharge
description - Location: Andheri (E) Marol Bus Depot, near Maruti High School, MIDC
Mumbai Maharashtra 400093 India

Experience in UAE:
1) Currently Working at NOVA ENGINEERING WORKS Since MAY 2018 to TILL DATE as a CIVIL ENGINEER.

Location: Flat # 301 A&B Al Maha Building, University City Road,
Muweilah Commercial, Near Alfalah Round About SHARJAH.

Job Responsibilities
Achieving Construction Activities like Excavation, Foundation, PCC, Waterproofing, RCC and Shuttering, steel fixing, casting concrete, masonry for brick works and plastering Tile fixing Etc. Up to completion. Independently following with Design engineers of, Architecture, and Structural Updated drawing for minor changes, as per site requirement needed and Inspection before casting Columns, Slabs. Etc
Looking after construction activities, making requisition and facilitating the arrangement Procurement of construction materials. Executions of civil, interior and exterior ground finish works and Site Supervision.

Job Responsibilities:
â¢ Scheduling of work.
â¢ Preparing & Verification of Sub-Contractors' & Suppliers' bills.
â¢ Working out the Quantities.
â¢ Verify the Quantities from the Client's Engineer.

Computer Skills:

â¢ Drafting tools: AutoCAD.
â¢ Packages: MS Office Applications
â¢ Operating systems: Windows 9x/2000/XP/Vista.

Personal Strength:

â¢ Excellent communication skills.
â¢ Hardworking Sincerity and Honesty
â¢ Individual & team working Capability
â¢ Knowledge of 5M ( Minutes, Manpower, Material, Machine, Management )
â¢ Manage development of program strategies

Thank you for viewing my resume
company - Kherwadi Police Station
description - Mumbai Maharashtra
400051 India

3) Working Experience at OM CONSTRUCTION Since JANUARY 2017 to APRIL 2018 As
A Civil Engineer (1.4 Years)
Project handle:
Project title: 9 RADHA
Position: Senior Engineer
Location: Dadar (E) Hindu Colony Road No 2 Mumbai Maharashtra 400014

Project title: LODHA ETERNIS
company - EXPERT CONSTRUCTION
description - Project handle:
Project title: ESTADO"
Civil Engineer,"PERSONAL SKILLS â¢ Passionate towards learning new skills. â¢ Hardworking, sense of responsibility. COMPUTER SKILL â¢ AUTOCAD 2D, 3D â¢ CIVIL 3DEducation Details 
January 2014 BE Civil Engineering Chandrapur, Maharashtra Nagpur University
January 2010 Diploma Civil Engineering Mumbai, Maharashtra Bajaj Polytechnic
January 2007 SSC Industrial Waste Water Nagpur, Maharashtra Hindi City High School
    University /BoardSite Engineer (Civil)Skill Details 
AUTOCAD- Exprience - 6 monthsCompany Details 
company - Talreja construction
description - I am diploma and degree Holder in civil engineering Branch applying for post of civil engineer. Hoping for your positive response. Thanks and Regards Apeksha Naharkar."
Java Developer,"Education Details 
August 2010 to May 2017 BE Electronics & Communication Jabalpur, Madhya Pradesh Takshshila institute of technologyJava developerSkill Details 
Java, Javascript,- Exprience - 6 monthsCompany Details 
company - Wab It Softwere Pvt.  Ltd.
description - Jr. Java Developer"
Java Developer,"Technical Skills (Trained / Project Acquired Skills) â¢ Languages Known: C, C++, J2EE, Spring, Hibernate. â¢ Testing: Functional Testing, Manual Testing. â¢ Operating Systems: Windows & Linux. â¢ Packages: MS-office. â¢ Databases: My SQL & PostgreSQL.Education Details 
January 2013 to January 2017 B.E Computer Science and Engineering  RTMNU University - Jhulelal Institute of Technology
January 2011 to January 2013 HSC  Nagpur, Maharashtra Maharashtra State Board - Dayanand Arya Kanya Junior College
January 2010 to January 2011 SSC  Nagpur, Maharashtra Maharashtra State BoardJava DeveloperJava Developer - Inouvelle Ventures Private LimitedSkill Details 
J2EE- Exprience - 17 months
C++- Exprience - 6 months
DATABASES- Exprience - 6 months
FUNCTIONAL TESTING- Exprience - 6 months
LINUX- Exprience - 6 monthsCompany Details 
company - Inouvelle Ventures Private Limited
description - Nagpur.
1.   Project name: Stock Management System		December 2017 - Till Date
Language of implementation: Angular (Frontend), JAVA - J2EE, spring, Hibernate Framework (Backend)
& PostgresSql Database.
Role: Java Developer
Operating system: Windows XP/07/08
Description: Stock management System is Specially Developing for the Medicinal Market. This software is developing to keep the track of medicines, ailments, surgical Equipments, & also to keep the track of Wholesalers, Retailers, Employees, & Customers of the Company. Stock management system is the practice of ordering, storing, tracking, and controlling
inventory. Debtor invoicing software helps small businesses and freelancers keep track of company assets   .. Stock
management may also be called stock control, inventory management or inventory control.
company - Inouvelle Ventures Private Limited
description - Project Profile
company - Inouvelle Ventures Private Limited
description - Language of implementation: Angular (Frontend), JAVA - J2EE, spring, Hibernate Framework (Backend)
& PostgresSql Database.
Role: Java Developer, Manual Testing.
Operating system: Windows XP/07/08
Description: This Project is done for the Oldest Printing Press in Nagpur. The proposed web based Printing Press
Management System designed according to user and system requirement to fulfil the existing problems. This system offers the products and services that enhance organization growth, efficiency, and profitability."
Java Developer,"TECHNICAL SKILLS Skills: Java, SQL, PL/SQL, C, C++, BootStrap, JSP, Ext JS Operating Systems: Windows Tools: Toad, Eclipse, SoapBox, Postman Databases: Oracle, MS-SQL, MS-Access, MS-ExcelEducation Details 
January 2017 B.E. Computer Technology Gondia, MAHARASHTRA, IN Manoharbhai Patel Institute of Engineering and Technology
 HSC  Gondia, MAHARASHTRA, IN S.M.P. Science CollegeJava DeveloperJava Developer - Xoriant Solutions Pvt LtdSkill Details 
Company Details 
company - Xoriant Solutions Pvt Ltd
description - Pune
Period                Sept' 2017 to till date
Role                  Java Developer
Description           ResRent is a team responsible for the development of various
modules for Hertz. They are responsible for the development of screens in the portfolios they look after, and act as a
primary control function; developing webservices to ensure it can
be hit by XML and JSON. We build various modules which are
inbuilt in Hertz to provide interfaces for end users in their tasks.
Tools                 Eclipse, TOAD, SoapBox, Postman
Responsibilities      - Develop module from Backend to Middleware to Frontend.
- Bugs fixing and proactive developing of enhancements
- Resolve Application incidents within defined UCs & creating
different Task ticket as well.
- Debug and provide solution for workflow.
- Resolving DB issues and providing technical support to BAs.
- Keeping track of deployments and effect of same on ongoing
jobs
- Contribution towards automation of daily activities and process
based organization
- On-time, within-budget delivery meeting customers' quality
expectations.
- Maintain knowledge base of domain, known defects and issues,
processes and development techniques.
company - Xoriant Solutions Pvt Ltd
description - Strong communication, collaboration & team building skills with proficiency at grasping
new technical concepts quickly and utilize the same in a productive manner.
â Experience in development in Web application.
â Resolve project related issues in minimal time.
â Able to communicate effectively with multifunctional teams, programmers and technical staff at all levels.
â Good knowledge of Java, Sql, Ext JS, JSP, XML, Web services.
â Good interpersonal & analytical skills with proven abilities in resolving the complex
software issue."
Java Developer,"TECHNICAL STRENGTHS Computer Language Java/J2EE, Swift, HTML, Shell script, MySQL Databases MySQL Tools SVN, Jenkins, Hudson, Weblogic12c Software Android Studio, Eclipse, Oracle, Xcode Operating Systems Win 10, Mac (High Sierra) Education Details 
June 2016 B.E. Information Technology Goregaon, MAHARASHTRA, IN Vidyalankar Institute of Technology
May 2013   Mumbai, Maharashtra Thakur Polytechnic
May 2010   Mumbai, Maharashtra St. John's Universal SchoolJava developerJava developer - Tech MahindraSkill Details 
JAVA- Exprience - 21 months
MYSQL- Exprience - 21 months
DATABASES- Exprience - 17 months
J2EE- Exprience - 17 months
ANDROID- Exprience - 6 monthsCompany Details 
company - Tech Mahindra
description - Team Size: 5
Environment: Java, Mysql, Shell script.
Webserver: Jenkins.
Description: OR-Formatter is an application which takes the input file as Geneva Modified File GMF from Geneva server and reads the data to generate Bill backup and Bill Invoices for Client customers of BT. These invoices would be sent across to all the clients after billing.
Contribution:
â¢	Played the role of Java developer involved in applying change request to current modules.
â¢	Worked on requirement analysis and development for the user stories and also preformed dev unit testing for the user stories.
â¢ Handled E2E issues and fixing bugs.
â¢	Prepare required program level and user-level documentation.
company - Tech Mahindra
description - Team Size: 5
Environment: Java EJB, Mysql, Shell script.
Webserver: Weblogic12c Server, Jenkins.
Description: STAA is an encapsulation middleware layer, acting as an interface between client's operational support systems and Legacy systems. It transforms the legacy data into various formats such as TLV. Input is in form xml/http to STAA where validations are done and the response is sent to the back end systems where back end systems are CSS and COSMOSS Databases. The business methods deployed as EJBs on WebLogic Server shared across various market facing units of client.
Contribution:
â¢	Played the role of Java developer involved in creating services using Java/J2EE.
â¢	Worked on requirement analysis and development for the user stories and also preformed dev unit testing for the user stories.
â¢ Handled E2E issues and fixing bugs.
â¢	Worked in Migration from java 1.6 to java 1.8."
Java Developer,"Education Details 
January 2013 Master of Engineering Information Technology Pune, Maharashtra M.I.T
January 2005 Bachelor of Engineering Information Technology Pusad, Maharashtra Amravati University
January 2001   Pusad, Maharashtra P. N. Junior College
January 1999 S.S.C  Pusad, Maharashtra K.D. High-SchoolJava DeveloperJava Developer - Maxgen TechnologiesSkill Details 
Company Details 
company - Maxgen Technologies
description - Currently working in Infrasoft Technologies, Andheri as a Java Developer.
company - MIS Generation of Tata Sky and Tata Power
description - COURSES DONE: Android -Mobile App Development, Technologies in Java-Core Java,
Advance Java, JSF, Hibernate, Spring at NIIT in 2015-16.

ANDROID PROJECT: Location Detector of Computing and Mobile Devices. (Android)

ME PROJECT: Data Deduplication. My projects works to reduce redundant data from the system and free up the memory. It stores unique copy of data and for more location with same data with the help of pointers can access the data. (Java)

SUBJECTS TAUGHT:
C language, Core Java, Object Oriented Programming (OOT/C++), Database, PPS (Programming and problem solving), AD (Advance Database), IAS (Information Assurance and security), Android

PROGRAMMING SKILL: C, C++, Java, Android.
DATABASE HANDLED:
MS Access, SQL, Oracle

NATIONAL CONFERENCE: 1. paper presented on ""A secure cipher index over encrypted character data in database."" On dated 28-29 April 2011 at Pimpri chinchwad college of engineering, Pune."
Java Developer,"Education Details 
 BE IT   pjlceJava DeveloperJava DeveloperSkill Details 
c++- Exprience - Less than 1 year months
c- Exprience - Less than 1 year months
JAVA- Exprience - Less than 1 year months
DS- Exprience - Less than 1 year months
Jdbc- Exprience - 24 months
Hibernate- Exprience - Less than 1 year months
Java J2Ee- Exprience - Less than 1 year months
Javascript- Exprience - 6 months
JQuery- Exprience - 6 months
Ajax- Exprience - 6 monthsCompany Details 
company - Almighty tech pvt ltd nagpur
description - 1. As a Java Developer.
â ORGANISATION: Almighty tech pvt ltd Nagpur
â DESIGNATION.: Java Developer.
â DURATION.: From 1st jan 2018
â Notice Period: 15 days
JOB RESPONSIBILITIES
â Resolve Bugs
â Develop project as per user requirement.

KNOWLEDGE ABOUT:-
â Programming language C, C++, DS, Java(Swing, JDBC, J2EE) java script,jquery,Ajax
â Ms office, Excel."
Java Developer,"TECHNICALSKILLS SpringMVC, Hibernate, JDBC, Java, J2EE, AzureWeb SunTechnologies Services, JSP, Struts, Servlet, RestApi. Scripting JavaScript, AJAX, HTML, JSON. OpenSourceTechnologies PHP. Database MsSQL, MySQL, Oracle. WebServers ApacheTomcatServer. Internettechnologiesand OnesignalWebPushNotifications, AzurewebServices, frameworks IONIC, HTML, JSON OperatingSystem WindowsServer2012R2, WinXP/7/8.1/10, Linux, Mac OS Education Details 
August 2013 to July 2016 BE Computer Engineering Nashik, Maharashtra Late G.N. Sapkal COE Nashik
July 2009 to June 2013 Diploma Computer technology Nashik, Maharashtra K K Wagh Polytechnic  NashikJava developerJava DeveloperSkill Details 
AJAX- Exprience - 12 months
DATABASE- Exprience - 24 months
HTML- Exprience - 24 months
J2EE- Exprience - 6 months
JAVA- Exprience - 24 months
Spring MVC- Exprience - 12 months
Ionic 3- Exprience - 6 months
Angular JS- Exprience - 6 months
Spring- Exprience - Less than 1 year months
Java- Exprience - Less than 1 year monthsCompany Details 
company - Replete business solutions pvt ltd
description - Working as Java developer in spring MVC, MySQL, MsSql, Java, J2EE, Ajax, Javascript, ionic 3 framework, angular js etc. Technologies."
Java Developer,"TECHNICALSKILLS SpringMVC, Hibernate, JDBC, Java, J2EE, AzureWeb SunTechnologies Services, JSP, Struts, Servlet, RestApi. Scripting JavaScript, AJAX, HTML, JSON. OpenSourceTechnologies PHP. Database MsSQL, MySQL, Oracle. WebServers ApacheTomcatServer. Internettechnologiesand OnesignalWebPushNotifications, AzurewebServices, frameworks IONIC, HTML, JSON OperatingSystem WindowsServer2012R2, WinXP/7/8.1/10, Linux, Mac OS Education Details 
August 2013 to July 2016 BE Computer Engineering Nashik, Maharashtra Late G.N. Sapkal COE Nashik
July 2009 to June 2013 Diploma Computer technology Nashik, Maharashtra K K Wagh Polytechnic  NashikJava developerJava DeveloperSkill Details 
AJAX- Exprience - 12 months
DATABASE- Exprience - 24 months
HTML- Exprience - 24 months
J2EE- Exprience - 6 months
JAVA- Exprience - 24 months
Spring MVC- Exprience - 12 months
Ionic 3- Exprience - 6 months
Angular JS- Exprience - 6 months
Spring- Exprience - Less than 1 year months
Java- Exprience - Less than 1 year monthsCompany Details 
company - Replete business solutions pvt ltd
description - Working as Java developer in spring MVC, MySQL, MsSql, Java, J2EE, Ajax, Javascript, ionic 3 framework, angular js etc. Technologies."
Java Developer,"Operating Systems Windows XP, 7, 10. Tools/Packages Visual Basic 6.0, UML, Packet Tracer Web technologies Core java, Advance java, JSP, Hibernate, Spring Languages known C, C++ Databases SQL AREAS OF INTEREST â¢ Software Development â¢ Programming Academic Projects Details Diploma: Title: Banking Software Description: We used GSM technology for transaction of message for a security purpose. Environment/OS: Windows XP Technologies used frontend: visual basic 6.0 Backend: oracle 9i BE: Title: Single Sign On Description: We have created ""single sign on"" mechanism for network security. Environment/OS: Windows 7 Technologies Used Frontend: java Backend: Oracle 10g ME: Title: Text Based Graphical Password To Overcome Shoulder Surfing Attacks Description: I have created a most secure password system using Random Printing Algo. Environment/OS: Windows 10 Technologies used: Frontend: Java Backend: MySQL Company: Title: Agriculture Management System Description: Created Web application for Agriculture system and Farming. Environment/OS: Windows 10 Technologies used: Frontend: Hibernate Backend: MySQL Education Details 
June 2015 to February 2018 ME(computers) computer Pune, Maharashtra Savitribai Phule Pune University
June 2007 S.S.C.  Solapur, Maharashtra Maharashtra Secondary BoardJava DeveloperJava DeveloperSkill Details 
JAVA- Exprience - 6 months
DATABASES- Exprience - 6 months
Hibernate- Exprience - Less than 1 year months
Jsp- Exprience - Less than 1 year months
Servlet- Exprience - Less than 1 year monthsCompany Details 
company - Maxgen Technologies Pvt.Ltd.
description - 1. Working as a java developer over Hibernate technology. Developing web application using JSP servlet and hibernate.
company - Prakshal IT academy
description - I was working as a trainer. I have taught A+, N+ CCNA(routing and switching)."
Java Developer,"Computer Skills: Languages And Script: JSP, Servlet, HTML, CSS, Java Script, Jquery, Ajax, Spring, Hibernate Operating System: Microsoft WindowsÂ® 2003/XP/Vista/7/8 Databases: My SQL Concepts: OOPS, Core java, Advance java Development Tool: Eclipse, Net beans IDE Web Server: Apache Tomcat 7.0Education Details 
January 2007 H.S.C  Amravati, Maharashtra VidyaBharati college
January 2005 S.S.C  Amravati, Maharashtra Holy Cross English SchoolJava DeveloperJava Developer - Kunal IT Services Pvt LtdSkill Details 
ECLIPSE- Exprience - Less than 1 year months
JAVA- Exprience - 14 months
HIBERNATE- Exprience - Less than 1 year months
SPRING- Exprience - Less than 1 year months
jQuery- Exprience - Less than 1 year monthsCompany Details 
company - Kunal IT Services Pvt Ltd
description - Currently Working As Java Developer In Winsol Solution Pvt Ltd From 1 July 2017 To Till Date.

Experience Of  2 Yrs As A Java Developer In Kunal IT Services Pvt Ltd."
Java Developer,"Education Details 
January 2016 B.E Information Technology Pune, Maharashtra Sawitribai Phule Pune UniversityJava DeveloperJava Developer - Vertical SoftwareSkill Details 
Company Details 
company - Vertical Software
description - Expertise in design and development of web applications using J2EE, Servlets
JSP, JavaScript, HTML, CSS, JQUERY, AJAX, JSON.

Experienced in developing applications using MVC architecture.

Good understanding of Software Development Life Cycle Phases such as Requirement
gathering, analysis, design, development and unit testing.

Languages & open Source Java, J2EE, Spring, Hibernate
Frame Work

Scripting Languages & Server Java JSP, Servlets, DB Connectivity's
Side Program JDBC, JavaScript, jQuery, Ajax, JSON

Application Server TomCat
Database MongoDB, MySql
IDEs Eclipse

1. Project Title: Expense Ledger
Role: Java Developer
Tools and Technologies: Java, Jsp, Servlet, MySql, JavaScript, Json, Jquery, Ajax.

2. Project Title: Trimurti Developer (Realestate)
Role: Java Developer
Tools and Technologies: Java, Jsp, Servlet, MySql, JavaScript, Json, Jquery, Ajax.

3. Project Title: Vimay Enterprise
Role: Java Developer
Tools and Technologies: Java, Jsp, Spring, Hibernate, Maven, Jquery, Ajax.
company - Higher Secondary School
description - Pune, 58.8%"
Java Developer,"TECHNICAL SKILLS Programming Languages: Java (Servlet, JSP, Spring Boot). Web Technology: HTML5, CSS3, Bootstrap, JavaScript, JQuery, Ajax, AngularJs. Database: MySQL. IDE and Tool: Eclipse, spring tool Suit, Net beans, Sublime Text, Atom. Operating System: Windows XP, 7, 8, 10. ACHIEVEMENT â¢ Java Developer Certificate from Unanth Technical Institute. â¢ Java Certificate from solo Learn. â¢ Command line crash Course certificate from Udemy. JOB DETAILS Education Details 
January 2018 M.C.A  Pune, Maharashtra Pune University
January 2015 B.C.A  Amravati, Maharashtra Amravati University
January 2012 H.S.C  Amravati, Maharashtra Amravati University
January 2010 S.S.C  Amravati, Maharashtra Amravati UniversityJava developerFull Stack Java DeveloperSkill Details 
Css- Exprience - Less than 1 year months
Ajax- Exprience - Less than 1 year months
Servlet- Exprience - Less than 1 year months
Html5- Exprience - Less than 1 year months
Spring- Exprience - Less than 1 year months
Java- Exprience - Less than 1 year months
Jquery- Exprience - Less than 1 year months
Jsp- Exprience - Less than 1 year months
Javascript- Exprience - Less than 1 year months
Bootstrap- Exprience - Less than 1 year months
Spring Boot- Exprience - Less than 1 year monthsCompany Details 
company - Salcluster technologies
description - Worked as java developer. Developed 3 projects using java servlet jsp ajax and web technologies.
company - Salcluster Technologies LLP.
description - Technology: Core Java, Servlet and JSP, HTML5, CSS3, Bootstrap, Javascript, Jquery
Ajax and Bootstrap.

PROJECT DETAILS
#1 Title: GST And Sales Billing Softwares
â¢ Status: Completed
â¢ Duration: 1.5 Months.
â¢ Abstract: This is a Web application made by using Java (Servlet and JSP), JavaScript, Jquery, Ajax, and MySQL on back end. It is a client's website which is used for Billing. It includes GST Billing, Generate and print invoice. It has five master pages and four common pages. Masters include customer master, item master, uom master, setup master, reason master and common pages like sales order, payments, sales return and invoice.

#2 Title: Dinman News website
â¢ Status: Completed
â¢ Duration: 1 Months.
â¢ Abstract: This is a Website project by using Java (Servlet and JSP), JavaScript, Jquery, Ajax, and MySQL on back end. It is a clients website. It is used for displaying online video and text news.

#2 Title: Agri Management Website
â¢ Status: Completed
â¢ Duration: 1.5 Months.
â¢ Abstract: This is a Website project by using Java (Servlet and JSP), JavaScript, Jquery, Ajax, and MySQL on back end. It is used for purchasing and selling vegetables all over india.
company - OmegaSoft Technologies pvt.ltd
description - Company Name: OmegaSoft Technologies pvt.ltd. An  ISO certified Company.
Duration: 5 months.
Technology: Java Spring Hibernate, AngularJs, Ajax, KendoUI and BootStrap.
Architecture: MVC Architecture and Service based Programming.
Project Module: Login Registration and Role Assignment, Payments.

INTERNSHIP PROJECT DETAILS
#1 Title: Employment Times
â¢ Status: Completed
â¢ Duration: 4 Months.
â¢ Abstract: This is a Website project using Java, Spring, Hibernate. Angular Js, Ajax and MySQL on back end. It is a client's website which is used for posting of newspapers all over India. It has 12 Modules. In which it has Admin Panel, E-paper Posting, Subscriptions, and many more. It is a very light weight website built by using MVC Architecture and Service based Architecture. We can also post advertisements on website and share the news on Facebook or any other social media directly. It has Payment module and users are able to make payments online through debit or credit card or E-Wallet."
Java Developer,"SKILLS: - 1) Team leading 2) Self-motivated 3) Hard working â Strengths: Ready to learn new Programming Languages, Punctual, Discipline, Respectfulness. DECLARATION:- I hereby inform you that all the statement made above are true with the best of my knowledge and belief. Education Details 
August 2014 to July 2017 MCA Computer Sciene Amravati, Maharashtra SGBAU, AmaravtiJava Web DeveloperSkill Details 
Core Java,JSP,Servlet,Spring,Hibernate,Struts,Javascript- Exprience - 6 monthsCompany Details 
company - Salcluster Technologies
description - 1.  I am Java web developer
2. Jsp , servlet, Sprinng MVC Technologies"
Java Developer,"Skills â¢ Language: Java â¢ Operating System: Windows, Linux (CentOS 6.6) â¢ Databases: Oracle, My SQL and Derby (Embedded DB) â¢ IDE: Eclipse (Oxygen) â¢ Tools: SonarQube, Putty. Responsibilitis: â¢ Participated in requirements gathering and design development meetings. â¢ Reviewed code and debugged errors to improve performance. â¢ Coordinated with systems partners to finalize designs and confirm requirements. â¢ Consistently met deadlines and requirements for all production work orders. â¢ Collaborated with other developers to identify and alleviate the number of bugs in the software and provided maintenance and development of bug fixes and patch sets for existing applications. Education Details 
 Bachelor of Engineering  (Computer)   Savitribai Phule Pune UniversityJava DeveloperJava Developer - Aurus Tech Pvt. LtdSkill Details 
JAVA- Exprience - 32 months
LINUX- Exprience - 6 months
Adavance Java- Exprience - Less than 1 year months
Derby- Exprience - Less than 1 year months
Oracle- Exprience - Less than 1 year monthsCompany Details 
company - Aurus Tech Pvt. Ltd
description - Having 2 years of experience in working with a Payment Gateway Solutions provider with Core Java. Hardworking Java Developer enthusiastic about working with multicultural teams.
company - Aurus Tech Pvt Ltd.
description - â¢ Participated in requirements gathering and design development meetings.
â¢ Reviewed code and debugged errors to improve performance.
â¢ Coordinated with systems partners to finalize designs and confirm requirements.
â¢ Consistently met deadlines and requirements for all production work orders.
â¢ Â Collaborated with other developers to identify and alleviate the number of bugs in the software and provided continued maintenance and development of bug fixes and patch sets for existing applications."
Business Analyst,"Education Details 
 BE Computer Science Mumbai, Maharashtra Mumbai University
 HSC  Mumbai, Maharashtra Maharashtra State Board
 SSC  Mumbai, Maharashtra Maharashtra State BoardBusiness AnalystBusiness Analyst - Fino Payments BankSkill Details 
Company Details 
company - Fino Payments Bank
description - Key Role   In-depth requirement and input gathering
Responsibilities and Achievements:
â¦ Conducted in-depth requirement and input gathering from all concerned stakeholders [Business SMEs, Technical Architect and Business Architect] to create artifacts like Business Requirement Document (BRD) to arrive at functional requirements for development team
â¦ Created Functional Specification Document (FSD) highlighting the technical implementation and the use cases
â¦ Led the Merchant Commission Module project from end-to-end and co-ordinated for CUG and Live
â¦ Designed the Account Opening Process flow end-to-end during the time when bank was going Live.
â¦ SPOC for all the configurations (both account level and customer level) in production.
â¦ Led the Cash Controlling Processes for the field users as per the requirement from the business team.
â¦ Design and build proof of concepts to validate the viability of alternate approaches and determine optimum choice
â¦ Involved in Process Design for development of the products
â¦ Performed Functional Testing of the entire system and provided support during UAT by preparing UAT test cases, performing UAT tests to onboard new processes as BAU
â¦ Worked with the development teams in arriving at detailed techno-functional specifications, participate in Feasibility
Analysis
â¦ Conducting twice a week meetings with the vendor to discuss the status of CRs and to resolve technical queries
company - Fino Paytech Pvt. Ltd
description - Key Role   Requirement gathering, Development, Testing
Responsibilities and Achievements:
â¦ Requirement gathering, preparation of traceability matrix, preparation and execution of use cases, developing of test plans based on requirements for Airtel Zambia National Partner Project
â¦ Led the employee profile creation, maintenance of employee details in the database: Preparation of work flow, end-to-end development and testing of the module
â¦ Designed the work flow process of the CAPA (Corrective Action Preventive Analysis) module to maintain the audit findings raised by the internal audit team
â¦ Designed the Expense Management module and automated it for end-to-end in-house expense flow
â¦ Designed the PMO tool Parivartan used for tracking the projects end-to-end"
Business Analyst,"Technical Skills Application Servers: IIS 6.0, Jboss 7.1. Database: SQL, Oracle and DB2. Report Tool: iReport, Crystal report. Career GraphEducation Details 
Business AnalystBusiness Analyst - Zensar Technologies LtdSkill Details 
CRYSTAL REPORT- Exprience - 15 months
DATABASE- Exprience - 6 months
DB2- Exprience - 6 months
IIS- Exprience - 6 months
IIS 6- Exprience - 6 monthsCompany Details 
company - Zensar Technologies Ltd
description - Location: Goregoan, Mumbai (Client -SUN Pharmaceutical)
Designation: Business Analyst.
Role: Requirement gathering, gap analysis, support, end user training, documentation.
company - Proteus Technologies Pvt Ltd
description - Base Information Management Pvt. Ltd. Is a Mumbai base software service provider with core competency and proven track record of installations of Enterprise Wide Solutions. Base customers come from industries as wide as Pharmaceuticals, Life Sciences, Plastics, Engineering, Chemicals
company - Wings Infonet Pvt Ltd
description - It is IT solutions Provider Company. Company provides comprehensive technology solutions to Accounting, trade, payroll and Asset Management firms across the world.
company - Hiral Tektronix Pvt Ltd
description - Software relates to recruitment (HRMS), accounting, and Payroll.

Job Responsibilities: â¢ ERP Implementation and after go live support.
â¢ Documenting user requirements and developing specifications for customization.
â¢ Integrating with other modules, integration testing & extending Post Go-live support, including training support to end-users.
â¢ Drafting functional requirements for ERP systems to represent the processes and functions involved.
â¢ Guiding the users in using various modules and the management for various functional issues.
â¢ Delivering awareness about various reports available in ERP system for day to day transactions and for MIS reporting of departments
System Audit for better results and follow ups for Observation.
â¢ Developing and designing report using iReport and crystal report"
Business Analyst,"Key Skills - Requirement Gathering - Requirement Analysis -Design Specifications - Client Communication - System Documentation - Problem solving - SDLC Operating Systems: Windows OS, UNIX (Linux/Ubuntu) Languages: Java, C++ Web Languages: JavaScript, HTML Tools: Citrix Software, System Architect, Quality Center v9.0 & v10.0, Tortoise SVN, DOORS, Artifact Viewer, JformDesigner, JIRA, Microsoft D365 Other Skills: Microsoft Office, MS Excel, MS PowerPoint, MS Visio, AutoCAD, VLSI, MS-CIT Certified. Education Details 
January 2012 BE Electronics Mumbai, Maharashtra Mumbai University
January 2006    Maharashtra State BoardBusiness AnalystBusiness Analyst - Intertek India Pvt LtdSkill Details 
SDLC- Exprience - 75 months
VISIO- Exprience - 60 months
REQUIREMENT GATHERING- Exprience - 15 months
Documentation- Exprience - Less than 1 year months
Functional Testing- Exprience - Less than 1 year months
Business Analysis- Exprience - Less than 1 year months
Jira- Exprience - Less than 1 year monthsCompany Details 
company - Intertek India Pvt Ltd
description - Business Analyst. Key responsibilities include Requirements Gathering, Requirements Analysis. Documentation like FRD creation. Providing KT sessions to the team. Having Client Communication. Gap Analysis.
company - Intertek India Pvt Ltd
description - Requirement Gathering from Businesses. Creating FRDs.
â Vendor interaction for functional and technical disciplines.
â Creating Project Plan.
â Walkthrough to team regarding the requirement â Change Proposal Management; Effort Estimation, Impact & Gap Analysis â Actively participate in Change proposal implementation & testing and define ways for improvement / enhancement â Defect analysis & clarifying functional queries of team members & developers â Creating UAT Test cases. Executing the same.
â Test Management: Test Data creation, Test Case writing, Test Case Execution (Manual), Regression tests at various stages in the SDLC

Project Details
Project 1	Inlight (Feb 2018 till date)
Platform	.Net

Description:
Inlight - (Supplier Risk Assessment Application)
Inlight is an Application designed to assess the Suppliers within the Supply chain. The Application on boards the Importers, Exporters and Suppliers. Based on the role they perform a Questionnaire is assigned to them and they fill out the Questionnaire. Basis the answer a scoring methodology is defined and the Suppliers are assessed to be Critical, High, Medium and Low. This helps in assessing the risk involved in working with certain Suppliers in the Supply chain.

Beyond Curriculum â Completed Internship in L&T â Attended Logistics Business School Training in Germany.
â A1 Certified in German Language.
â Travelled Onsite for Business Meetings and Discussions with Clients.

Personal Dossier .
company - AllCargo India Pvt Ltd
description - FRD creation
Client communication
Vendor Management
Having product Walk through with the team.
company - AllCargo India Pvt Ltd
description - Requirement Gathering from Businesses. Creating BRDs and FSDs.
â Vendor interaction for functional and technical disciplines.
â Creating Project Plan.
â Analyzing business requirements and defining consistent, correct and complete specification â Change Proposal Management; Effort Estimation, Impact & Gap Analysis â Actively participate in Change proposal implementation & testing and define ways for improvement / enhancement â Defect analysis & clarifying functional queries of team members & developers â Prepare Requirement document, User manual, Test cases and training material â Test Management: Test Data creation, Test Case writing, Test Case Execution (Manual), Regression tests at various stages in the SDLC

Project Details
Project 1	CRM (Nov 2017 to Feb 2018)
Platform	Microsoft D365

Description:
CRM - (Sales Management System)
CRM is a Software solution specially designed for handling Sales Management. This is a product provided by Microsoft which helps in tracking the sales of company, the activities of the salesperson, 360-degree view of customer accounts. This basically helps to get the overall status and view of various businesses the company is achieving from different Customers. A platform where the salesperson provides the details of Lead, Opportunity, Accounts and Businesses. Available on Cloud.

Project 2	Credit Risk (Nov 2017 to Feb 2018)
Platform	.Net

Description:
Credit Risk - (Customer credit check Management System)
Credit Risk is a Software solution specially designed for checking the credit status of the customer from which businesses are gained. The software basically is designed to take the KYC and the consent from the customer. For those customers who provide the consent, the credit report and monitoring report are obtained from the Credit Bureau. Based on the reports the customer health can be determined and business with them can either be  or discontinued.

Work Experience 3:
company - Capgemini India Pvt Ltd
description - Client: DB Schenker â Analyzing business requirements and defining consistent, correct and complete specification â Change Proposal Management; Effort Estimation, Impact & Gap Analysis â Actively participate in Change proposal implementation & testing and define ways for improvement / enhancement â Defect analysis & clarifying functional queries of team members & developers â Prepare Requirement document, User manual, Test cases and training material â Impart business process knowledge transfer to the team members. Prepare business / functional process workflow using Visio, UML etc â Working knowledge of OOAD - Object Oriented Analysis & Design concept.
â Helping the Junior BAs in their work. Supervising their work.
â Tools & Applications: System Architect, DOORS, UML designs & concepts, HP Quality Center, MWB, Jformdesigner â Test Management: Test Data creation, Test Case writing, Test Case Execution (Manual), Regression tests at various stages in the SDLC

Project Details
company - Capgemini India Pvt Ltd
description - Platform	Java

Description:
TANGO - (Sea & Air Cargo Management System)
TANGO is a Software solution specially designed for handling sea and air cargo (Import & Export) Management. TANGO manages the creation of Shipment, Tracking the shipment via multiple service legs i.e. Pick-up, Delivery leg etc. It helps in managing the end to end shipment with respect to the entire department involvement (globally)

Work Experience 2:
company - Capgemini India Pvt Ltd
description - "
Business Analyst,"IT Skills: Area Exposure Modeling Tool: Bizagi, MS Visio Prototyping Tool: Indigo Studio. Documentation: MS Office (MS Word, MS Excel, MS Power Point) Testing Proficiency: Smoke, Sanity, Integration, Functional, Acceptance and UI Methodology implemented: Waterfall, Agile (Scrum) Database: SQL Testing Tool: HPQC Business Exposure Education Details 
 Bachelor Of Computer Engineering Computer Engineering Mumbai, Maharashtra Thadomal Shahani Engineering college
 Diploma Computer Engineering Ulhasnagar, Maharashtra Institute of Technology
 Secondary School Certificate  Ulhasnagar, Maharashtra New English High SchoolSenior Business Analyst - RPASenior Business Analyst - RPA - Hexaware TechnologiesSkill Details 
DOCUMENTATION- Exprience - 47 months
TESTING- Exprience - 29 months
INTEGRATION- Exprience - 25 months
INTEGRATOR- Exprience - 25 months
PROTOTYPE- Exprience - 13 monthsCompany Details 
company - Hexaware Technologies
description - Working as a RPA Business Analyst
company - BBH- Brown Brothers Harriman & Co
description - is a private bank that provides commercial banking, investment management, brokerage, and trust services to private companies and individuals. It also performs merger advisory, foreign exchange, custody services, commercial banking, and corporate financing services.

Responsibilities: â¢ Performed Automation Assessment of various Processes and identified processes which can be candidates of RPA.
â¢ Conducting Assessment that involves an initial Understanding of the Existing System, their technology, processes, Usage of the tools, Feasibility of tool with automation tool along with automation ROI analysis.
â¢ Preparing the Automation Potential Sheet which describes the steps in the process, the volume and frequency of the transaction, the AHT taken by SME to perform the process and depending on the steps that could be automated, Automation potential and the manual efforts that will be saved are calculated.
Calculating the complexity of the Process which is considered for automation and depending on all these factors Number of Bots and Number of Automation tool Licenses are determined.
â¢ Implementing a Proof of Concept (POC) to Validate Feasibility by executing the selected critical use cases for conducting a POC which will helps to identify financial and operational benefits and provide recommendations regarding the actual need for complete automation.
â¢ Gathering business requirements by conducting detailed interviews with business users, stakeholders, and Subject Matter Experts (SME's) â¢ Preparing Business Requirement Document and then converted Business requirements into Functional Requirements Specification.
 â¢ Constructing prototype early toward a design acceptable to the customer and feasible.
â¢ Assisting in designing test plans, test scenarios and test cases for integration, regression, and user acceptance testing (UAT) to improve the overall quality of the Automation.
â¢ Participating regularly in Walkthroughs and Review meetings with Project Manager, QA Engineers, and Development team.
â¢ Regularly interacting with offshore and onshore development teams.
company - FADV - First Advantage
description - is a criminal background check company that delivers global solutions ranging from employment screenings to background checks.
The following are the processes which were covered:
Email Process, Research Process, Review Process.

Responsibilities: â¢ Requirement Gathering through conducting Interviews & Brainstorming sessions with stakeholders â¢ To develop decision models and execute those rules as per the use case specifications.
â¢ To Test/validate the decision models against document test data.
â¢ To maintain and enhance the decision models for changes in regulations as per use case specifications.
â¢ Responsible for performing the business research that will make a business growth.
â¢ Developing a clear understanding of existing business functions and processes.
â¢ Effectively communicate with the onsite clients for the queries, suggestions, and update.
â¢ Giving suggestions to enhance the current processes.
â¢ Identifying areas for process improvement.
â¢ Flagging up potential problems at an early stage.
â¢ Preparing PowerPoint presentations and documents for business meetings.
â¢ Using any information gathered to write up detailed reports.
â¢ Highlighting risks and issues that could impact project delivery.
â¢ Able to work accurately.
â¢ To develop and maintain documentation for internal team training and client end user operations.
â¢ To work efficiently with team members and across teams.
â¢ To mentor and train junior team members.
company - Clinical Testing, Lab Work and Diagnostic Testing
description - IQVIA provides services to its customers this includes: Clinical Testing, Lab Work and Diagnostic Testing under clinical trial. These customers need to pay to IQVIA and aging details and invoices are generated for the same.
The following are the processes which were covered:

Tracking Payments, Automated Real Time Metrics Reporting (Dashboard), Past Due Notifications, AR Statements, Credit/Rebill.
Responsibilities: â¢ Conducting meetings with clients and key stakeholders to gather requirements, analyze, finalize and have formal sign-offs from approvers Gather and perform analysis of the business requirements â¢ Translating the business requirements into the Business Requirement Document [BRD], Functional Requirement Document [FRD].
â¢ Facilitating meetings with the appropriate subject matter experts in both business and technology teams â¢ Coordinating with business user community for the execution of user acceptance test as well as tracking issues â¢ Working, collaborating and coordinating with Offshore and Onsite team members to fulfill the BA responsibilities from project initiation to Post-Implementation â¢ Reviewing the test scripts with business users as well as technology team. Execute test scripts with expected results for the System Integration Test (SIT) and User Acceptance Test (UAT) â¢ Coordinating and conducting the Production Acceptance Testing (PAT) with the business users â¢ Creating flow diagrams, structure charts, and other types of system or process representations â¢ Managing changes to requirements and baseline through a change control process â¢ Utilizing standard methods, design and testing tools throughout project development life cycle â¢ Work closely with the operational functional teams, operations management, and personnel, and various technology teams to facilitate a shared understanding of requirements and priorities across all areas
company - Eduavenir IT Solution
description - Project: M.B.M.S

M.B.M.S. - is an Inventory management application that allows user to manage inventory details of different warehouses, having different products located at various locations and help extract what goods have been procured, sold or returned by customers. It generates automated invoicesalong withcustomized reports. It also managescustomer complaint and resolution system implementation along with automated MIS on monthly basis.Sales and forecastingis also developed on MIS System and the streamlining of process of warehousing and dispatch along with online proof of delivery management system (POD documentation) is generated.

Responsibilities: â¢ Participate in requirement gathering discussion with client to understand the flow of business processes â¢ Analyze the requirements and determine the core processes, develop Process Documentation and ensure to stay up-to-date in conjunction with on-going changes â¢ Participate in process flow analysis and preparing BRD, SRS.
â¢ Coordinating with developers, designers & operations teams for various nuances of the project, communicate the stakeholder requirements from requirement /enhancement to implementation and finally deliver the same within estimated timeframe.
â¢ Support UAT by reviewing test cases, manage version control of documents, software builds.
â¢ Coordinate with the stakeholders for UAT sign off and coordinate internally for production movement till Golive stage of the application.
â¢ Provide demo and training to internal and end user using PowerPoint presentation.
â¢ Resolving project functional &technical issues during UAT.
â¢ Prioritizing the Production bugs and resolving the same within the estimated timeframe.
â¢ Preparing Project Status Report and Production Bugs Status to all the stakeholders.
â¢ Promoting and Networking for online trading platform.
â¢ Designing query sheet for obtaining and comparison of quotes from various vendors.
â¢ Development of product codes / material codes for inventory management (Master Data Management)
company - CAPGEMINI Head Office
description - Type: Mobile and Device Testing.       Duration: January 2014 - August 2014

Follet - An application which takes an electronic request from the user for the books he requires from a particular follet store. This detailed information about books that will include the name of the book, its price, the date of the transaction and the parties involved which will then be sent to follet stores. User then create request for one or more books for a given date. This request is then processed further and user gets a mail of the date when he will be provided with that book.

Responsibilities: â¢ Understanding the needs and business requirements.
â¢ Preparing BRD, SRS by eliciting all the requirements from the client and SMEs â¢ Understanding the dependency of the modules in the system â¢ Preparation of test plan for Unit level and Integration level.
â¢ Preparation and execution of test cases.
â¢ Defect tracking, Issue Resolution, Risk Monitoring, Status Tracking, Reporting and Follow-up.
â¢ Preparation of Test Completion report.
company - CAPGEMINI Head Office
description - 
company - CAPGEMINI Head Office
description - Humana is a health care insurance project of U.S. which deals with supplying various medicines to citizens as per the doctor's reference and patient's insurance policy. This application keeps track of all the medicines user has consumed in the past and generates a patient history. A citizen is given a drug only after the doctor's reference so the doctor's information is also linked with the patient's history.

Responsibilities: â¢ Understanding the requirements and getting clarifications from client.
â¢ Involved in writing test cases based on test scenarios and execute them.
â¢ Ensuring Test Coverage using Requirement Traceability Matrix (RTM) â¢ Preparation of Test Completion report.
company - CAPGEMINI Head Office
description - Testing Trends WQR (World Quality Report) is an application which allows the users to take a survey on different methods and technologies used for testing. Users can choose to answer any type of questions under three different categories. Users have a facility to search, view and export the data to excel. Also, users get daily and weekly reports through email about the new trends in testing implemented around the globe. Testing Trends WQR app is available on Android and IOS platforms.

Responsibilities: â¢ Understanding the requirements and getting clarifications from client.
â¢ Writing test cases based on test scenarios and executed them.
â¢ Performing different types of testing such as Functional, Integration, System, and UAT.
â¢ Defect resolution and maintenance of the application."
Business Analyst,"TECHNOLOGICAL SKILLS â¦ Knowledge of Computers on the Windows platform. â¦ Fluency in MS-Office Applications such as Excel, Word, PowerPoint, etc. â¦ HTML, JAVA, PHP ATTRIBUTES â¦ Hardworking towards achieving the Goal â¦ Good communication skills â¦ Quick learner â¦ Good interpersonal relationEducation Details 
January 2016 to January 2018 MMS  Mumbai, Maharashtra University of Mumbai
January 2016 to January 2018 MMS Management Mumbai, Maharashtra University of Mumbai
January 2014 B.Sc.  Bandra, MAHARASHTRA, IN Rivzi College
January 2011 HSC  Bandra, Maharashtra, IN St. Andrews College
January 2011 HSC   Allana Junior College
January 2009 SSC   Canossa High School
January 2008 SSC   Maharashtra State BoardBusiness AnalystBusiness Analyst - Mass Group of CompaniesSkill Details 
EXCEL- Exprience - 23 months
HTML- Exprience - 6 months
JAVA- Exprience - 6 months
PHP- Exprience - 6 months
POWERPOINT- Exprience - 6 monthsCompany Details 
company - Mass Group of Companies
description - Key Role   Analyst
Responsibilities:
â¦ Manage risk and provide risk management process.
â¦ Liaise with other project areas to coordinate with interdependencies and resolve issues.
â¦ Analyse and map business process.
â¦ Guide stakeholders on devising effective and efficient approaches to achieving project objectives.
â¦ Preparation of various Derivable i.e Business requirement, functional requirement and report specification.
company - Commversion Pvt.LTD
description - Responsibilities:
â¦ Employee Satisfaction reports
â¦ Evaluate overall work
â¦ Internal Audit
â¦ Maintain timesheet validation.
â¦ Set Simple and performance driven compensation strategies and polices."
Business Analyst,"Education Details 
February 2006 to February 2006 TYBCOM Commerce  mumbaiBusiness AnalystBusiness AnalystSkill Details 
Company Details 
company - Motilal Oswal
description - Business Analyst
Handling IT Operation for Institutional Equities 
Maintain Daily MIS in Excel for CAG, Research, Derivative, Sales team Preparing Auto Dashboard For Research, Sales, Trading team Working on Excel Macro to Create Innovative Report 
Working on Block Related Data Working on BD Fund from different GEO Working on Investors Corporate Meeting to track Corporate Block & Fund Interest in Sector
company - FSS
description - Project Description:
Maintain and prepare cash indent, cash report, cash position, and cash planning
Responsibilities:

â¢ Maintain Daily MIS in excel.
â¢ Provide complete information about MIS & ATM.
â¢ Maintain and prepare cash indent, cash report, cash position., cash planning
â¢ Co-ordinate with BANK CASH DEPARTMENT.
â¢ Co-ordinate with custodians

.
DEGREE/ COURSE          YEAR of PASSING   GRADE          INSTITUTE                              UNIVERSITY/ BOARD   PERCENTAGE
company - ANGEL BROKING
description - Reporting: Assistant Manager /   SR. Manger
Responsibilities:

â¢ Handling team of 14 Quality assurance team members
â¢ Maintain Daily MIS in excel of team productivity
â¢ Maintain and prepare repots, adding comments on remark
â¢ mailing client  for modification of given number
â¢ Mailing reports to different branches
â¢ Coordinating with RM
â¢ Provide complete information about script to client"
SAP Developer,"Skills: â¢ ETL â¢ Data Warehousing â¢ SQL/PL SQL â¢ Basic Core Java â¢ Basic Python Tools: â¢ SAP BODS â¢ SAP BO â¢ Oracle 11g â¢ Sybase Education Details 
August 2008 to July 2012 Bachelor of Engineering Navi Mumbai Panvel, Maharashtra Mumbai University
June 2006 to May 2007 B.E Computer Engineering Navi Mumbai, Maharashtra Ryan International SchoolSAP BO/BODS Developer/Administrator, Tata Consultancy Services LimitedSAP BP/BODS Developer/Administrator, Tata Consultancy - SAP BODSSkill Details 
DATA WAREHOUSING- Exprience - 6 months
SAP BO ADMIN- Exprience - Less than 1 year months
SAP BODS- Exprience - Less than 1 year months
ETL- Exprience - Less than 1 year months
BASIC CORE JAVA- Exprience - Less than 1 year months
SAP BO- Exprience - Less than 1 year months
SAP BODS ADMIN- Exprience - Less than 1 year months
BASIC PYTHON- Exprience - Less than 1 year monthsCompany Details 
company - SAP BODS
description - â¢ 
Client: Royal Bank of Canada

â¢ Developed BODS jobs and created/modified packages during migration from Sybase to Oracle Database.
â¢ Taking up regular performance enhancement activities by co-coordinating with other teams like database team, Server Team, Data Center team to identify bottlenecks in the running of jobs.
â¢ Developed BODS jobs to fetch data from SAP R3 using various data transfer modes.
â¢ Implemented Central repositories to move jobs seamlessly in multi-developer environment
company - Tata Consultancy Services Limited
description - â¢ Installed and configured all the five environments of BO 4.0 and BODS 4.0/4.2 (DEV/QA/PROD/Standby/DR) with Apache and Tomcat as web and app servers on windows
â¢ Loading dataÂ received from various source systems like TCS BaNCS, SAP R3, LOS, Credence and flat files using SCD1 and SCD2 to Data Warehouse to generate consolidated reports.
â¢ Developed BODS jobs to fetch data from SAP R3 using various data transfer modes.Â 
â¢ Implemented Central repositories to move jobs seamlessly in multi-developer environment.
â¢ Configuration and creation of BODS repositories using Repository Manager and Sever Manager
â¢ Used Formats and Transforms including Flat File formats, Query, Table Comparison, History Preserving, Validation Transform, Key Generation and Map Operation
â¢ Â Deployed BODS in Active-Active mode using server groups to improve performance and availability of the system
â¢ Using BO Java SDK to build custom java programs to automate various administration tasks and fetching metadata about BO system.
â¢ Implemented BO Mobile and used wdeploy to separate static and dynamic contents into web server and web application.
â¢ Developed audit reports in web Intelligence using audit universe to gather information about the usage of various reports in the BO system.
â¢ Â Implemented and deployed features like Trusted authentication,VintelaÂ sso to make application easy to use for end users.
â¢ Daily administrator tasks involved migration of resources from one environment to another, publication creation and monitoring, troubleshooting any issues with the application and monitoring the application through the tools available to ensure that the application is up and available round the clock.
â¢ Have been involved in testing of backup systems in Standby and DR to make sure that application continue to service business users in case the primary systems are down.
company - Tata Consultancy Services Limited
description - Client: Tata Capital Financial Services Limited

â¢ Installed and configured all the five environments of BO 4.0 and BODS 4.0/4.2
â¢ (DEV/QA/PROD/Standby/DR) with Apache and Tomcat as web and app servers on windows.
â¢ Loading data received from various source systems like TCS BaNCS, SAP R3, LOS, Credence and flat files using SCD1 and SCD2 to Data Warehouse to generate consolidated reports.
â¢ Configuration and creation of BODS repositories using Repository Manager and Sever Manager.
â¢ Used Formats and Transforms including Flat File formats, Query, Table Comparison, History
â¢ Preserving, Validation Transform, Key Generation and Map Operation.
â¢ Deployed BODS in Active-Active mode using server groups to improve performance and availability of the system.
â¢ Using BO Java SDK to build custom java programs to automate various administration tasks and fetching metadata about BO system.
â¢ Implemented BO Mobile and used wdeploy to separate static and dynamic contents into web server and web application.
â¢ Developed audit reports in web Intelligence using audit universe to gather information about the usage of various reports in the BO system.
â¢ Implemented and deployed features like Trusted authentication, Vintela sso to make application easy to use for end users.
â¢ Daily administrator tasks involved migration of resources from one environment to another,publication creation and monitoring, troubleshooting any issues with the application and monitoring the application through the tools available to ensure that the application is up and available round the clock.
â¢ Have been involved in testing of backup systems in Standby and DR to make sure that application continue to service business users in case the primary systems are down."
SAP Developer,"Competencies: SAP Business Intelligence Version SAP BO 4.2 BO Tools Design Studio, IDT, Webi, UDT, CMC activities Backend DB HANA, BW, Bex Queries, SQLEducation Details 
SAP BO Developer and SAP HANA DeveloperSAP BO Developer - Credit SuisseSkill Details 
Sap Bi- Exprience - 72 months
CMC- Exprience - 72 months
HANA- Exprience - 36 months
BUSINESS INTELLIGENCE- Exprience - 72 months
SQL- Exprience - 72 months
SAP BO- Exprience - 72 monthsCompany Details 
company - Accenture
description - Company Accenture, Pune
Environment SAP BO 4.2, IDT, HANA views
Role SAP BO Developer and HANA developer

Description:
This Project aims to provide reporting solution for Swiss business users.The universe supports analyzing customer funds purchase order patterns and order lifecycle reporting.
The purpose of reports is to provide an end-to-end view of the entire sales order transaction, and to track the investment orders in various funds, including the status of orders, order types along with the gross value and net value to be generated from these orders.

Roles & Responsibilities:
â¢ Analyzed user requirements and sorted the best possible ways of optimizing performance of universe.
â¢ CV and generated views development in HANA studio
â¢ Handling day to day activities involved in development of Business Objects as per the client requirements.
â¢ Worked as a team member with backend counterpart to understand the Business Requirements.
â¢ Developing and maintaining universes.
â¢ Raised OSS tickets to SAP for issues and implemented suggestion/workaround in development.
â¢ Fixed webi issues reported by users.
â¢ Created BIAR file and promoted to higher environments by change request.
â¢ Project handover to team and documentation for reference.

Project 2:

Project Name Nestle
Client Nestle Globe
Company Tech Mahindra Pvt. Ltd. Bangalore
Environment SAP BO 4.2, IDT, HANA views
Role SAP BO Developer and HANA developer

Description:
This Project aims to provide reporting solution for Global business users. The Globe Integrated Order (IO), Billing Detail and Order Detail universes combines orders, deliveries, shipment and billing documents at the order item level.
The universe supports analyzing customer order patterns and order lifecycle reporting.
The purpose of reports on this universe is to provide an end-to-end view of the entire sales order transaction, and to track the sales orders in various ways, including the status of orders, order types along with the gross value and net value to be generated from these orders.

Roles & Responsibilities:
â¢ Analyzed user requirements and sorted the best possible ways of optimizing performance of universe.
â¢ CV and generated views development in HANA studio
â¢ Handling day to day activities involved in development of Business Objects as per the client requirements.
â¢ Worked as a team member with backend counterpart to understand the Business Requirements.
â¢ Developing and maintaining universes.
â¢ Raised OSS tickets to SAP for issues and implemented suggestion/workaround in development.
â¢ Implemented Union pruning in concept in universe to optimize performance by partition selection in HANA views by passing value to prompt.
â¢ Fixed webi issues reported by users.
â¢ Created BIAR file and promoted to higher environments by change request.
â¢ Project handover to team and documentation for reference.

Project 3:

Project Name Nestle
Client Nestle TCT
Company Tech Mahindra Pvt. Ltd. Bangalore
Environment SAP BO XI 4.2, Design Studio 1.6 SP6 & Bex Queries
Role SAP BO Developer

Description:
The Project was developed to show delay in Nestle freight orders by hours on cross tab, bar chart, geo map due to weather conditions.

Basic Source for the data was BW Bex queries and the top of these Queries dashboard was designed.

This main user audience was transport control tower members to analyze and take decision to send consignments via other modes of transport due to weather conditions to avoid delay and reduce expenditure.

Roles & Responsibilities:
â¢ Involved with users to understand the Business Requirements and implement feature in a generic way.
â¢ Suggested best visualization components in dashboard to use.
â¢ Analyzing user requirements and finding the best possible ways of representing the data.
â¢ Bug Fixes and feature enhancement in application.

Project 4:

Project Name Nestle
Client Nestle BA
Company Tech Mahindra Pvt. Ltd. Bangalore
Environment SAP BO 4.2, Design Studio 1.6 & Bex Queries
Role SAP BO Developer

Description:
The Project ""Nestle BA Catalogue Reporting"" for Nestle is basically to provide Dashboard in Design studio with the information of all the reports used within Nestle.
And this dashboard is been used by all the users of Nestle within different region of globe.
Basic Source for the data was BW Bex Queries and the top of these Queries Dashboard was designed.
This main purpose of the project was to help users to understand about usage of different reports on portal and to help them to take decision for decommissioning of a report.

Roles & Responsibilities:
â¢ Involved with customer to understand the Business Requirements in order to present the data in a meaningful manner and suggested component selection to visualize data in most effectively.
â¢ Analyzing user requirements and finding the best possible ways of representing the data.
â¢ Changes in BW query according to requirement
â¢ Interacting with client team for requirement gathering and analysis.
â¢ Implemented ideas in bex and design studio app to optimize performance with help of successful POC.

Project 5:

Project Name Warner Bros
Client Marvin Pictures, US
Company CapGemini Pvt. Ltd. Bangalore
Tools Teradata DB, SAP BO 4.1 Webi, IDT, CMC, Query builder, HPSM ticketing tool
Role SAP BO Developer

Description:
The purpose of the project is tracking of DVD and comic sale worldwide. The Application will be used by End Users to analyze their sales Information and to help them in taking decision for their Business Growth.

Sales reports in webi were based on universe. Teradata tables were source to universe.

Worked as shared resource for CMC activities and webi/universe issues tickets.

Roles & Responsibilities:
â¢ Working as a team member to understand the Business Requirements.
â¢ Performed CMC activities like user & user group creation, providing access to objects and folders. Created folders for reports, connection, universes and provided user security, connection pointing to universe.
â¢ Worked on issue related to connection, universe objects and webi reports enhancements.
â¢ Raised OSS messages to SAP to seek solution related to BO tool and limitations.
â¢ Worked on webi report, universe defects and enhancements.
â¢ Meeting with users to understand issue, suggest best solution and ETA.
â¢ Worked on query builder to find BO objects metadata for investigation.
â¢ Created of BIAR format using promotion management.

Project 6:

Project Name Nordea
Client Nordea Bank, Sweden
Company CapGemini Pvt. Ltd. Bangalore
Environment SAP BO 4.1, IDT, Webi, Teradata
Role SAP BO Developer

Description:
The Project report Analytics for Nordea aims to provide clear analysis of personal, company accounts in bank around the world. In all regions reports have data related to users and their account type.

Webi reports based on universe based on Teradata DB financial tables.

This project mainly tracks firm's Sales with respect to different criteria's like customer wise sales for different categories. It helps various end Users to analyze their sales related information and to help in their business growth and make precise decisions.

Roles & Responsibilities:
â¢ Involved in activities to understand the Business Requirements.
â¢ Analyzing user requirements and finding the best possible ways of representing the data.
â¢ Designing and developing interactive webi reports as per client's dynamic requirements.
â¢ Developed webi reports having blocks, graphs and linked report with summary report.
â¢ After development performing unit test activities and presentation to users.
â¢ Implementation of Business Object i.e. Webi from one Environment to another using BIAR File.
â¢ Fixed issues in webi reports related to data and report formatting and made changes in report requested by users in MAT.
â¢ Prepared RDD for reports and performed unit testing.
â¢ Provided KT to users and supports teams.
company - Tech Mahindra
description - 
company - Capgemini
description - "
SAP Developer,"Education Details 
July 2008 to February 2012 BE COMPUTER SCIENCE Nagpur, Maharashtra NAGPUR UNIVERSITYSAP HANA DeveloperSAP HANA Developer - SchlumbergerSkill Details 
SAP HANA- Exprience - 60 months
SAP BO- Exprience - 36 monthsCompany Details 
company - Schlumberger
description - Schlumberger is the world's leading provider of technology for reservoir characterization, drilling, production, and processing to the oil and gas industry. Working in more than 85 countries.
Role-SAP HANA Developer
Team Size: 12
Duration: Feb 2017 to Till Date
Responsibilities:
Working on Suit on HANA and Native HANA Systems.
Working on end to end implementation for Release 2.3 and 2.4.
Developing HANA Information Models Calculation Views, Table Functions, Procedures.
Actively involved in cutover activities, Dry Runs Production Go live and Post Production Go Live and resolving issues.
Worked on HANA functionalities like Currency Conversions and analyzing the Planviz for performance related issues etc.
Worked on to bring the Tables in SAP HANA Landing zone using SDA.
Working on to resolve the performance issues in HANA views by applying filter, joins etc.
Interacting with the Business and functional team members, based on the discussion Creating Design Documents, Technical Specification and Test Documents.
Successfully implemented the Release 2.2 and 2.3 for US, Ecuador and Canada.
Successfully completed the Self-service project for Finance Domain and created Reports in Analysis of office.
Working on to create the Power BI Reports.
Working on to resolve production defects and Incidents by using HPALM and Remedy Tools.
Working on to resolve the issues in an existing HANA Views.
Working on to promote the Objects in different environment by using Charm/Solmon.
Working on to enhance the performance of the HANA views by using PLANVIZ.
Worked on to create the BO Reports for Vendor Master.
 2. Aero Space Design/Honeywell
company - Fujitsu Consulting India
description - Responsibilities: 
â¢ HANA Data Model Development and Modification of Existing Models.
â¢ Providing estimation to create the data model based on business Request.
â¢ Creating Views- Attribute, Analytical and Calculation Views base on business requirement â¢ Creation of Design document and Technical design document.
â¢ Brought the data into HANA Landing zone using SLT and SDA.
â¢ Handled complex performance issues.
â¢ Worked SAP Landscape transformation for data replication from source DB to HANA DB â¢ Solved SIT tickets on HANA Information Model.
â¢ Developing HANA Information Models Calculation Views, Table Functions, Procedures.
â¢ Modifying existing models to include tables, columns etc. necessary to complete the requirement.
â¢ Configuring business objects to fetch data from HANA.
â¢ Editing, Modifying exiting reports to fetch data from HANA as per the requirement.
 3. Mc Donald
Company- Fujitsu Consulting India
Client- MC Donald
Role-SAP BO Developer
Team Size: 3
Duration: July2013to June2014
Responsibilities: 
â¢ Providing estimation to create the Universe and Webi Reports based on business request.
â¢ Worked on creating the Universes on top of SAP HANA and BW Resources.
â¢ Worked on creating the Complex Reports in webi.
â¢ Worked on providing the Row level and Folder level security.
â¢ Worked on Scheduling the Reports.

Tools and Technology Knowledge
SAP HANA SPS12
SAP ECC 6.0
SQL
Remedy
SharePoint
SAP Logon
WEBI
Power BI"
SAP Developer,"Education Details 
May 2013 Master Computer Application Mumbai, Maharashtra IMCOST College
April 2010 BSc Computer Science Mumbai, Maharashtra V.K.K. Menon College
March 2005  SpringerNature Pune, Maharashtra S.V.V.M SchoolABAP DeveloperABAP Developer - SAP EXPOSURESkill Details 
Company Details 
company - SAP EXPOSURE
description - 1. Organization: SpringerNature.
Duration: MAR 2017 -Till date â¢ Project: Support for springer Nature
Project Type: Support
Role: ABAP Developer

Major Contribution: â¢ Writing new reports and modification for existing reports.
â¢ Worked on smartforms.
â¢ Standard debugging for issues â¢ For unit testing created contracts, orders, and invoices and tested individually.
company - Atos Pvt. Ltd
description - Duration: MAY 2014 -DEC 2015 â¢ Project: lenM - It's a Ministry of Netherland Project â¢ Project: DSP - DSM Sinochem Pharmaceuticals â¢ Project: AkzoNobel - Akzonobel is a leading global paints and coatings company  and a major producer of specialty chemicals.
Project Type: Support
Role: ABAP Developer
Major Contribution: â¢ Modified SAP Script as per the requirement specified.
â¢ Modified Invoice Smart Form.
â¢ Created custom tables, data elements, structures."
SAP Developer,"Education Details 
January 2016 Bachelor Of Engineering LEAN PROJECTS Mumbai, Maharashtra Mumbai University
January 2013 Diploma Computer Engineering Ratnagiri, Maharashtra Maharashtra State Board of Technical EducationSAP CONSULTANTSAP CONSULTANT - Quick LearnerSkill Details 
Company Details 
company - Quick Learner
description - Groomed skills with Microsoft Excel.
Job Responsibilities:
Working on Centralized SAP Security project which includes User            â¢ VBA programming for automation.
Level authorizations including Role creation and changes. and
Compliance policy maintenance for SAP systems.
â¢ Confidence and Positive Attitude.
Assisting users in troubleshooting and identifying authorization
issues using SU53 and trace analysis.
Willingness to learn & work in team.
Working on GRC 10.X Access Control application and all its
component.
â¢ Strong analytical thinking, documentation and design skills
Assisting in defining the scope of a project/opportunities,
estimating efforts and project timelines.
Hands on experience in SAP Market Place for creating User /                â¢ Adaptable to new environment, with ability to get along with
OSS Ids/Developer Keys.		people."
SAP Developer,"Education Details 
SAP Technical ArchitectSAP Technical Consultant -  (ALE/IDOC/oDATA/Fiori/S4HANA/EWM/APO/Retail)Skill Details 
SAP ABAP- Exprience - 120 months
SAP ABAP ( ALE / IDOC / EDI )- Exprience - 96 months
SAP Netweaver Gateway / oData / Fiori- Exprience - 24 months
SAP Techno Functional- Exprience - 36 months
SAP ABAP ( IS-Retail / APO / IS-Auto / EWM)- Exprience - 36 months
SAP Techno-functional- Exprience - 48 months
SAP SD- Exprience - 36 months
NetWeaver Gateway (oDATA, Fiori)- Exprience - 24 months
SAP S/4HANA (new features Extensibility, Embedded Analytics)- Exprience - 12 monthsCompany Details 
company - 
description - 13+ years of work experience in SAP which includes roles varies from developer to subject matter expert
â¢ Strong project experience in implementation, upgrade, application development and maintenance
â¢ SAP ABAP Certified associate and has certification in ITIL 2011 Foundation and PRINCE2 practitioner
â¢ SAP project Full-Lifecycle implementations across multiple global projects
â¢ Direct client exposure of 1.3 years in United States of America and 1.2 years in Malaysia
â¢ Expertise on SAP ABAP FRICEW components
â¢ Experience in various SAP modules like MM, FI, CO, SD, PS, PP, CS, PM, QM and HR
â¢ Experience on S/4HANA, IS-Auto, IS-Retail, SCM 7.0, SOLMAN, SAP GTS, SAP PI 7.3, SAP BI 7.0/ 7.3, Vistex, ProShip, HPQC, IBM ManageNow, CA service desk, Loftware, Data Matrix
â¢ Experience in SAP Netweaver Gateway (Fiori) and also created number of POCs for potential customers
â¢ Successfully completed 9 implementation, 7 support, 1 roll-out, and 1 upgrade projects
â¢ Expertise in understanding different requirements of the client for diverse industries
â¢ Experience in leading technical team of different sizes
â¢ Customization experience in various areas of SD, FI & MM modules
â¢ Experience in Upgrading to ECC6.0
â¢ Experience in Unicode conversion related issues/tasks.
â¢ Skilled in document processes, identifying issues, handling multiple tasks, and training end users.
â¢ Proven ability to interact with business system analysts and end-users in design phase
â¢ Extensive experience in analyzing complicated business processes, effort estimations and creating technical specifications.
â¢ Experienced with onsite-offshore support model of work and lead the team
â¢ Excellent communication skills, team participation, inter-team co-ordination, team leadership abilities and customer oriented attitude
â¢ Experience in pre-sales activities
â¢ Thorough understanding of the project management and quality processes"
Automation Testing,"* Excellent grasping power in learning new concepts and technology. * Highly motivated team player with strong work ethics, committed to hard work. * Ability to work and co-ordinate in a team effectively. * Enthusiastic self-starter and team player. * Quick and independent learner.Education Details 
January 2014 Bachelor of Technology Information Technology branch  BPUT University
January 2010 Diploma Engineering Brahmapur, Orissa U.C.P Engineering SchoolSoftware Testing & Automation EngineerSoftware Testing & Automation Engineer - Tech MahindraSkill Details 
Company Details 
company - Tech Mahindra
description - India
Duration       Oct 2017- Till Date

Project Description
BT Group plc (trading as BT and formerly British Telecom) is a British multinational telecommunications holding company with head offices in London, United Kingdom. I worked for Air Logistics Program under the banner of British Telecom. This project handles all the web applications to carry out the whole logistics operation over United Kingdom through various airlines.

Roles & Responsibilities â¢ Design and develop framework for the Test scenarios and Test cases.
â¢ Developing automation test scripts on the existing application â¢ Executing Test Cases for every new release..
â¢ Involved in running test cases and logging defects through the HPQC tool.
â¢ Involved in formulating test Summary Report.
â¢ Conduct Internal Test Case Peer Reviews.
â¢ Participated in Daily Scrum Meetings.
â¢ Participated in weekly status meetings with the team, developers to discuss open issues and communicating with onsite team.
company - Tech Mahindra Pvt. Ltd Pune, Tech Mahindra
description - is an Indian multinational company with around 115, 000 Employees spread across 90 countries globally.

Total Experience: 2 Years 7 Months

Organization              Designation         Duration
company - Tech Mahindra
description - Project Description
AT&T Inc. is an American multinational conglomerate holding company headquartered at Whitacre Tower in Downtown Dallas, Texas. During my serving as software engineer at AT&T, I have worked for CSI-CAM (Common Service Interface) team which is responsible for running of AT&T's centralised solution hub web application called myatt.com.

Roles & Responsibilities â¢ Design, develop and maintaing Automation Test Scripts and Test cases using Selenium WebDriver and several desktop window automating tool such as Sikuli and AutoIT.
â¢ Executing Test Cases and check the working functionality of  the existing application.
â¢ Involved in tracking/manging test life cycle and logging defects using JIRA and HPQC/ALM.
â¢ Involved in formulating test Summary Report.
â¢ Conduct Internal Test Case Peer Reviews.
â¢ Participated in Daily Scrum Meetings.
â¢ Participated in weekly status meetings with the team, developers to discuss open issues and communicating with onsite team.
company - Tech Mahindra Pvt. Ltd Pune, Tech Mahindra
description - Till Date"
Automation Testing,"SOCIAL SKILLS: â¢ Ability to establish trust and work with a team. â¢ Determined with good work ethics. â¢ Ability to work under difficult situations.Education Details 
January 2011 to January 2016   Mumbai, Maharashtra MUMBAI UNIVERSITY
 Bachelor of Electronics Engineering Electronics Engineering Mumbai, Maharashtra Atharva College of Engineering at MaladAutomation and Electrical engineerAutomation and Electrical Engineer - SMEC AUTOMATION PVT. LTDSkill Details 
Company Details 
company - SMEC AUTOMATION PVT. LTD
description - -18, Supply, installation, testing and commissioning of water level sensor and valve actuators for water tanks at BARC hospital Anushaktinagar, Trombay and BARC facility at Kalyan.
â¢ Implemented of academy projects like Automatically tank filling with relay and float sensor level control, bottle filling on conveyor belt motion, and waste material crashing then separated on conveyor belt motion with PLC controlling.

Role of Automation and Instrumentation Engineer (Marine and Industrial Automation):
â¢ Re-calibration of field Instruments like control valve actuators, sensors and transmitters for pneumatic and electrical.
â¢ Research and developing on new automated system.
â¢ Level, flow and temperature process control with PID of various brands (Nippon, Selec etc.)
â¢ PLC panel wiring, DOL and Star delta control wiring of 3 phase induction motor.
â¢ Panel testing IP knowledge.
â¢ Control Panel quality check and assurance.
â¢ Eletrical Single Line Daigram (SLD) and power wiring daigram.

Internship experience:
company - SMEC AUTOMATION PVT. LTD
description - Company Profile
SMEC Automation provides specialized technical consulting services and custom engineered products to the marine, energy and offshore industries applying advanced methods and technologies. SMEC Automation is one of India's leading manufacturers of automation and monitoring equipment for ocean-going vessels especially in Marine Boiler and Main Engine Control, IGG (Inert Gas Generator) and Alarm System.

Project:
company - SMEC AUTOMATION PVT. LTD
description - Completed Internship of Instrumentation and Calibrations in SMEC Automation Pvt. Ltd. for 6 months duration from 8th June 2016 to 30th November 2016.

TECHNICAL QUALIFICATION:
â¢ Underwent Post Graduate Diploma in Industrial Calibration and Instrumentation and Post Graduate Diploma in Industrial Automation in duration of 3 months each with hands on practical exposure to Industrial Automation Tools like PLC, PAC, SCADA, HMI, INTERFACING, DRIVES, MCC PANEL and VFD.
â¢ Successful in the examination as Post Graduation Diploma in Industrial Automation and Post Graduation Diploma in Industrial Instrumentation with TUV Rheinland Certified Qualification on 07-11-2016.
TECHNICAL SKILLS:
â¢ Calculation and selection of cable, switchgear and protection.
â¢ Expertise in PLC Hardware like AB, Schneider, Siemens, Omron, Delta, ABB and GE-Fanuc. Knowledge on control relay logic and ladder logic.
â¢ Having good knowledge in designing and interfacing PLC with SCADA such as Intouch, Vijeo-Citect, WinCC, iFix and Factory TalkView. Real time data acquisition from OPC (Kepware), HMI & interfacing.
â¢ Handling VFD for motor control (Schneider-Electric ALTIVAR 28 Series)
â¢ Calibration of Pressure gauge, Temperature gauge, Control Valve, Level or Flow measurements and control, Druck Injector, Smart DP transmitter using HART COMMUNICATOR. Piping and Instrumentation Diagram, Hand Tools, Tubes and Pipe fitting, Cable and Cable Glanding.
â¢ Have basic knowledge of ""HONEYWELL DCS C-300"" and its programming.
â¢ Completed 5 days Robotic workshop (KUKA KR 16) at Atharva College of Engineering.
â¢ Training of 2 weeks at RK Electricals.

COMPUTER SKILLS:
â¢ Electrical designing software: basic AutoCAD and EPLAN.
â¢ PLC Software: Rockwell, Twido-suite, Step7-Microwin SP2, Unity Pro XL, CX-Programmer, WPL Software, Codesys and Versa Pro. Designing software: Wonderware, CitectScada7.4, Proficy HMI SCADA/iFIX and FactotyTalk View.
â¢ Successfully completed Spoken Tutorial program on ""C"", ""C++"" and ""JAVA"" Programming individually conducted by IIT-BOMBAY.
â¢ Completed 5 days basic course for ""Basic Of Mac OS"" at Atharva College of Engineering. Completed Phython Workshop conducted by CodeStrike of 1 day in '15 at Atharva College of Engineering.
â¢ Operating System: Windows, Mac."
Automation Testing,"TECHNICAL SKILLS Automation Testing â¢ Selenium WebDriver, TestNG. Testing Framework â¢ TestNG, Data Driven, Page Object model. Applications & Tools â¢ Eclipse, Postman Building Tool Version Control Continuous Integration â¢ Maven â¢ SVN â¢ Jenkins Database â¢ SQL Languages â¢ SQL, Java, C++ Scripting Languages â¢ HTML, JavaScript Operating System â¢ Windows Domain â¢ Insurance, ERP, E-Commerce Education Details 
January 2016 B.E. Computer Pune, Maharashtra Savitribai Phule University of PuneAutomation TestingJava Developer and AutomationTestingSkill Details 
C++- Exprience - 6 months
DATABASE- Exprience - 6 months
ECLIPSE- Exprience - 6 months
EMPLOYEE RESOURCE GROUP- Exprience - 6 months
ENTERPRISE RESOURCE PLANNING- Exprience - 6 monthsCompany Details 
company - Phioenix Microsystem Pvt.Ltd.
description - 
company - Phioenix Microsystem Pvt.Ltd.
description - Organization Role
Phoenix Microsystems Pvt.Ltd. Jr. Java Developer

PROJECTS
Project #1
Project SWB-EBS_Support
Role Java Developer
Description
Inventory Operation System provides with a way to manage the inventories which are conducted on a day to day basis. It is used by account owners and district offices to manage their inventory cycles, plan their events, schedule their stores and people, enter employee time sheets and approve customer invoicing. In addition, it provides several tools which assist the users in completing a profitable inventory, while satisfying the needs of their customers.

Responsibilities

Environment

â¢ Accounts/Customers/Stores
â¢ Cycles
â¢ Planning/Scheduling/Rescheduling/Splits
â¢ Time Collection
â¢ Invoicing
â¢ Closing the week Use of bulk updates- bulk inserts. Updates to be performed based on primary

â¢ Java 1.6, Ice faces 1.8.2, Ibatis 2.0, Jboss Seam, EJB 2.0, HTML

Project #2
Project INTELLIGEN_FOR MOBILEAPP_DEV_KAUAPC
Role Java Developer
Description
Inventory Management for a retailer project is to develop Inventory Management System (IMS), accessible on Android, for retailers to track their outstanding inventory, cost of inventory, sales by day and thus empower retailer to better manage their business.

Responsibilities
â¢ Cost of Inventory
â¢ Inventory Management System
â¢ Time Management
â¢ Machine Learning Platforms

Environment â¢ Java 1.6, Core Java 6.0, JSF 2.0, Rich Faces 4.1, Spring 3.2, Restful Web service using Jersey API

Project #3
Project Loan LeLo Automation
Role Software Test Engineer
Description
Loan Le Lo Offers for the selected loan from multiple banks. Customer can select particular loan offer and will have to fill the details in the form and will get registered. Email will be sent to his registered email id with login id and password.

Responsibilities
â¢ Development of Page Object Model Framework for Automation Testing.
â¢ Development of Automation Test Script using Selenium WebDriver & TestNG in JAVA.
â¢ Execution of Selenium Test cases and Reporting defects.
â¢ Debugging & Executing Automation test script using Selenium Web Driver.
â¢ Involved in Functional & Regression Testing.
â¢ Worked on Jenkins for Continuous Integration Requirements.
â¢ Reviewed Test Reports and Prepared Summary Reports.

Environment â¢ Selenium Web Driver, Java, Eclipse, TestNG, SQL

Project #4
Project DDSM Automation
Role Software Test Engineer
Description
The objective of this project is to automate a system for ZZ server. ZZ server that is Data Divider team has a manual system to enter a customer details & its user details. DDSM system have multiple roles. Each role have set of permissions.

Responsibilities
â¢ Responsible for Integration Testing, Functional Testing, System Testing.
â¢ Creating automation test cases for existing POM Framework using Selenium Web Driver.
â¢ Enhanced Test cases using Java programming features and TestNG Annotations.
â¢ Debugging & Executing Automation test script using Selenium Web Driver.
â¢ Followed Data driven approach: Reading the data from Excel file
â¢ Worked on Jenkins for Continuous Integration (CI) Requirements.
â¢ Involved in Regression Testing, API Testing.
â¢ Preparation of weekly and monthly status reports.

Environment â¢ Selenium Web Driver, Java, Eclipse, HTML, TestNG, SQL, Postman"
Automation Testing,"SKILLS Agile Methodology Scrum, Kanban, Extreme Programming(XP), Test-driven development (TDD) and Feature Driven development(FDD) Domain Industrial Automation, Retail, Banking, Insurance, Health care. Automation Framework Modular, Data driven, BPT, Hybrid Test automation tool HP UFT, TAF, Load Runner, Selenium, Rational Robot, DCMTK, sikuli languages VB Script, C++, Python, Shell Script Bug Tracking Tool Bugzilla, Jira, HP Quality Control, AP Test, Clear Quest Version management Clear case, Win CVS, SVN Database Oracle, SQL server, MySQL Network Protocols TCP/IP, HTTP, HTTPS, VPN, FTP, LDAP Healthcare DICOM, PACS, HL7, Image Archiving, Image reconstruction, Rockwell Automation Logix Designer, Factory Talk, RSLinx Classic, Control Flash, Compare Tool, I/O modules and Profiles. Education Details 
 Bachelor of Science Computer Science Mumbai, Maharashtra Mumbai UniversityQA Automation LeadQA Automation LeadSkill Details 
C++- Exprience - 63 months
PYTHON- Exprience - 109 months
UFT- Exprience - 85 months
SELENIUM- Exprience - 85 months
SQL- Exprience - 69 monthsCompany Details 
company - Leading Industrial Automation
description - Responsibilities:
â¢ Test Rockwell Automation embedded systems such as Logix Designer, Factory Talk, RSLinx Classic, Control Flash, Compare Tool, I/O modules and Profiles.
â¢ Coordinate with Onsite and Offshore QA leads to design and develop test strategy and test plan.
â¢ Document test plan and test cases base on product Requirement Document and functional specification. Discussed and explained the requirement to QA team.
â¢ Work in Scrum base Agile Methodology and Test Driven development environment with frequent changing requirements and lots of challenges.
â¢ Document and Published Program Increment Objectives, Program Increment planning report, Feature Epics, Backlog and Stories.
â¢ Conduct and Participated in Program Increment Planning, Backlog Grooming, Sprint Planning, Daily Stand Ups, Sprint Review and Retrospective.
â¢ Perform Automation testing tasks such as Design, Create, Maintain, Execute and peer review test scripts using Test Automation Framework(TAF)
â¢ Develop and Execute System integration and GUI integration test using C++.
â¢ Interact with Business Analysts and Software Developers for bug review and participated in QA meetings.
â¢ Work closely with development and engineering department to ensure proper resolution of bugs.
â¢ Proactively came up with innovative methods to improve software quality, test coverage, efficiency and regression coverage.
â¢ Created and Maintained testing documents such as High Level Requirement, Test strategy report, Test plan, Test cases, Test execution results, Bug report, Quality Metrics, Traceability Matrix and Testing Summary Reports.
Environment: Logix Designer, Factory Talk, RSLinx Classic, ControlFlash, CompareTool, I/O modules, Profiles, Test Automation Framework(TAF), C++, Python, Selenium, Clear Case, Clear Quest, UFT, Collaborative Lifecycle Management, and Version-one.
company - GE-Healthcare
description - Responsibilities:
â¢ Validated GE CT scanners based on FDA compliances and regulations.
â¢ Interacted with Clinical experts, Business Analysts and Development architect for creation of test strategy and test plan.
â¢ Coordinated with the Offshore QA and Onsite QA team to work on design and develop of test strategies and requirement traceability matrix.
â¢ Designed, Created and executed manual test cases base on product requirement documents.
â¢ Increased productivity by initiating use of automation tools like DICOM and QTP.
â¢ Trained and guided GE team on automation test creation and execution using QTP.
â¢ Performed testing types such as functional, integration, performance and regression and Provide test reports to management.
â¢ Involved in release support, test bed creation & testing throughout release process.
Environment: Agile, Test-driven development (TDD), DICOM, HL7, Image reconstruction, Windows, Linux, SQL, C, Python, QTP, HTML, XML, QTP, VBScripts and QC
company - Patni-GE-Healthcare PACS
description - Responsibilities:
â¢ Validated GE PACS application and tested web application defined for Administration, Authentication and Archiving of DICOM images.
â¢ Involved in the creation of Automation framework by using Hybrid and BPT approach.
â¢ Responsible for creation of Test Bed and Golden Data using DCMTK.
â¢ Prepared the Test Scenarios, Test plan, Test cases and Automation Scripts for Integration, System and End-to-End testing on all applications.
â¢ Designed and developed User defined and Generic functions with well-versed VB scripting.
â¢ Descriptive programming in scripting dynamic objects and reusable actions in defining scenario flows in UFT/QTP.
â¢ Reviewed the QTP Scripts developed by the team members.
â¢ Supported the QA/SIT and Regression testing team for environmental issues and Interact with other downstream environment system team for the clarifications.
Environment: Agile, Test-driven development (TDD), GE PACS, DICOM, HL7, Image reconstruction, Image Construction, Windows, Linux, SQL, C, Python, Selenium, QTP, HTML, XML, QTP, JavaScript, VBScripts and QC
company - HDFC Bank Limited
description - Responsibilities:
â¢ Reviewed Business requirements, design documents and prepared test cases for assigned project releases.
â¢ Performed testing types such as API, functional, integration, System, performance, regression, security and network. Provide test reports to management.
â¢ Involved in integration testing for release of new functionality.
â¢ Testing critical bug's fixes and co-ordinate with developer in fixing these critical bugs.
â¢ Involved in backend database testing in oracle and Microsoft SQL.
â¢ Involved in release support, test bed setup & testing throughout release process.
â¢ Automation of test flow's using automated tools QTP 10.
Environment: Windows, Linux, SQL, C, Python, QTP, HTML, XML, QTP, JavaScript, VBScripts and QC
company - Vistaar Systems Limited
description - Responsibilities:
â¢ Referred Business requirements documents, Use case documents and Design documents provided by client and developer for creation of Manual and Automation test cases.
â¢ Involved in Analysis, Design, Creation, Execution and Debugging of automation scripts using automation tools like Rational Robot, QTP and Shell scripting.
â¢ Testing critical bug's fixes and co-ordinate with developer in fixing these critical bugs.
â¢ Involved in Installation of build, Maintaining test bed, Smoke and Sanitary testing, Regression testing, Performance testing and Database testing using Rational Robot, QTP and Shell scripting.
Environment: Windows, Linux, SQL, Rational Robot V2002 and QTP V 9.2."
Automation Testing,"Technical Skills Summary I have completed ""CORPORATE TRAINING in Manual and Automation Testing"" at Source-Code Technology, Pune. â Manual and Automation Testing â¢ SELENIUM IDE, TestNG, SELENIUM Grid, JENKINS, Apache POI. â¢ Good knowledge in SDLC. â¢ Excellent understanding of White Box Testing and Black Box Testing. â¢ Good knowledge in Functional Testing, Integration Testing and System Testing. â¢ Good Exposure in writing Functional and Integration Scenarios. â¢ Good understanding of writing of test cases including test case design technique. â¢ Good understanding Build and release. â¢ Good knowledge on Ad hoc and smoke testing. â¢ Excellent understanding of usability, reliability and exploratory testing. â¢ Excellent knowledge of Globalization and Compatibility Testing. â¢ Excellent Understand of STLC. â¢ Good knowledge of regression and retesting. â¢ Excellent knowledge on Defect tracking and Defect Life Cycle. â¢ Good Knowledge on Test plan and Traceability Matrix. Internship Project Project Name: Resume Extractor Duration: 6 months Role: Manual And Automation Testing Environment: Jdbc, Servlets, Jsp, Technologies: Web based Application, MS Access2007 The project involved development of a web application. Resume extractor provides the technology to analyze mass volume of data to detect resume in data covered which company have into valuable information. This project is company site's based on recruitment process. Strengths â¢ Able to work in a team â¢ System and Operational Analysis â¢ Good Communication Skills â¢ Active learning and critical thinking â¢ Good interpersonal skills, willing to take challenges and more responsibilities. â¢ Ability to learn new technologies with minimal time period. Education Details 
January 2015  BCS Computer Science  MGM's College
 MCA  Pune, Maharashtra Computer Science fromJSPM College
 HSC  Nanded, Maharashtra Maharashtra state board
 SSC  Nanded, Maharashtra Maharashtra State BoardSoftware testingSoftware testingSkill Details 
APACHE- Exprience - 6 months
BLACK BOX- Exprience - 6 months
BLACK BOX TESTING- Exprience - 6 months
FUNCTIONAL TESTING- Exprience - 6 months
INTEGRATION- Exprience - 6 monthsCompany Details 
company - Tech Mahindra
description - Software testing in manual and Automation
company - 
description - software Test engineer"
Automation Testing,"Education Details 
 B.Tech Electronics And Instrumentation Engineering Jaunpur, Uttar Pradesh VBS Purvanchal UniversityAutomation TesterAutomation Tester - Tech MahindraSkill Details 
Company Details 
company - Tech Mahindra
description - Mumbai		May 2018 to Present

Project & Contribution
Tech Mahindra

Project Title: Payment Gateway Jio Money
Role: Automation Tester
Responsibility: * Analyzing the manual test cases to create automation scripts.
* Working on Redwood tool for Automation.
* Maintained regression pack as per the project requirement.
* Performed API testing.
* Created Automation scripts for API testing.
* Enhancing framework to support cross functionality testing * Execute test cases and evaluate test results for both manual and automated testing.
* Maintaining the scripts as per the requirement.
* Adding new automated tests to improve automated test coverage for both functional and regression.
* Performed automation testing, analyzing test results, and report defect into the bug tracking system and drive issues to resolution.
* Preparation of test data with different test conditions to ensure coverage of business rules.
* Performed Sanity, Ad hoc and Regression testing.
* Participated in defect triage meetings with developers to validate the severity of the bug and responsible for tracking of the bug life cycle.
* Worked with development team to ensure testing issues are resolved.

Project Description
JIO MONEY- Jio Payment Gateway provides the Facility to Merchants and Users to enable to pay through the JIO MONEY. Features include: Purchase, Bill Payment, load Money, short cash purchase, Pay to Merchant and Pay to User etc.

Inscripts
Project Title: CometChat
Role: Automation Tester
Responsibility:
Created automation framework with bug report using page object and data driven framework with automated email test scripts
Handling QA tickets, Coordinate with the development team

Project Description
CometChat is the chat solution for your site/app which will help you grow your customer base exponentially, drastically increase the time spent by users. The CometChat has several useful features like one on one chat, group chat, audio/video call, screen sharing, Games, real time chat translation, Mobile apps and desktop messenger.

Project Title: Web tracker
Role: Sr. Software Tester
Responsibility:
Creation Test Scenarios, Test Script and Test Case
Execution of Test Case
Ad-Hoc Manual Testing
Regression Testing
Automation Testing, Test Scripts using tools such as Selenium WebDriver 2.0

Project Description
Accomplishment Web Tracker aims to provide time sheet facility to its customers. Release contains the following features which are related to employee time tracking, task assignments, tracker submission, reminders, approvals & its notifications.

Hayaan InfoTech

Project Title: Real Estate Agent Website
Role: Sr. Software Tester
Responsibility:
Creation Test Scenarios and Test Case
Execution of Test Case
Smoke Testing
Black Box Testing
Ad-Hoc Manual Testing
Regression Testing

Project Description
This project has WEB page graphical HTML representation of a neighborhood made up of different types of houses and apartments. There are several sales people around the country, who are all responsible for selling the houses and apartments through Web Site. This Web Site help user to Purchase or Request for any Estate Property.

Project Title: E-commerce Website
Role: Software Tester
Responsibility:
Creation Test Scenarios and Test Case
Execution of Test Case
Ad-Hoc Manual Testing
Smoke Testing
Black Box Testing
Regression Testing

Project Description
This project includes Order Processing, Invoice Generated and Printing, Packaging Slip, Order Payment, Return material Authorization, Label Sheet Printing. The Order Processing of that application is very big. There are 3 main entities which are involved in the Order Processing Customer, Sales Person, and Admin.

Project Title: Enquiry to Invoice System
Role: Software Tester
Responsibility:
Creation Test Scenarios and Test Case
Execution of Test Case
Smoke Testing
Black Box Testing
Ad-Hoc Manual Testing
Regression Testing

Project Description
This application is browser-based application to reduce investment in hardware and software. The proposed system contains following module, which offers database management and reporting of various activity of company. This application comprise of following module Inquiry and Estimation, Quotation & Negotiation, Purchase Order System, Delivery System, MIS Reports.
company - Inscripts (India) Pvt. Ltd
description - 
company - Haayan InfoTech Pvt. Ltd
description - "
Automation Testing,"TECHNICAL SKILLS Languages: Core Java Automation Testing Tool: Selenium with web driver and IDE. DBMS: MS SQL Server 2000/2005, Oracle 10g. Operating System: Windows-XP/7/10 Education Details 
 BSCIT   Mumbai UniversityAutomation TesterAutomation and Manual TesterSkill Details 
DATABASE MANAGEMENT SYSTEM- Exprience - 6 months
DBMS- Exprience - 6 months
JAVA- Exprience - 6 months
MS SQL SERVER- Exprience - 6 months
MS SQL SERVER 2000- Exprience - 6 monthsCompany Details 
company - Capgemini India Pvt Ltd
description - Worked on Manual as well as automation testing.
Started carrier as manual tester then after that moved to automation testing. Now working as automation tester. Worked on Selenium Webdriver.
company - Na
description - Currently working with Capgemini, Airoli as Consultant from 06th May 2013 to till the
date."
Electrical Engineering,"Skills: 1) MC Office 2) AutoCAD 2016 3) Introductory Knowledge of EPLAN 4) SAP-ERP for Product specific coding Strengths: 1. Ability to complete the work within stipulated time. 2. Keeping calm under pressure. 3. Multi-Tasking 4. Experienced in a project engineering role. 5. Good interpersonal skills, team spirit, flexibility, can cope with stress. 6. Fluent in English. 7. Good knowledge of international standards and rules like ISO, ATEX, NFPA, IEC, NEC, Shell DEP etc. College Final Year Project: Topic: Study & Control of State Power Grid, Maharashtra. â¢ Supervisor: Prof. Jawaleker â¢ Place: Shri Sant Gajanan Maharaj College of Engineering, Shegaon, Maharashtra. â¢ Description: The project aims to study of Power Grid network & Operation for Maharashtra state at Regional Power Grid Centre, Nagpur, India. Education Details 
  Thermal Power Plant Engineering Nangal, Punjab National Power Training Institute
 B.E. Electrical Engineering Shegaon, Maharashtra Shri Sant Gajanan Maharaj College of Engineering, Shegaon, Maharashtra, India
 HSC  Chandrapur, Maharashtra Vidya Niketan Jr. College
 SSC  Chandrapur, Maharashtra Vidya Mandir High SchoolIndustrial Electrical Design Engineering along with project managementSkill Details 
AUTOCAD- Exprience - 74 months
ENTERPRISE RESOURCE PLANNING- Exprience - 6 months
EPLAN- Exprience - 6 monthsCompany Details 
company - Osmoflo Engineering Services Pvt. Ltd.
description - along with project management:
1) Preparation of Electrical Design Basis, Electrical Scope of work, Electrical Technical Specification, Electrical Data Sheet and Electrical Single Line Diagram (SLD) as per Project Standards.
2) Preparation and Review Electrical Drawings layouts like Hazardous Area Classification, Electrical Indoor and Outdoor Lighting Layout, Earthing and Lightning Protection Layout Drawing, Substation Equipment and Trench Layout Drawing Overall Cable Layout Drawing, Bill of Material (BOM) MR for Electrical Equipment and Cable Schedules.
3) Oil and Gas, Water Treatment Plant for Overseas customer at Australia.
company - M/s VIVID ELECTROMECH PVT. LTD
description - for Whole Pune region)
Period: 06th June 2017 to 20th Aug 2018.
Nature of Job - Electrical Sales / Marketing / Project Management of HT/LT Panels, Bus Duct, Voltage Stabilizers for Residential / Commercial / Industrial Projects:
1) Responsible for Sales and Marketing of all HT/LT Panels, Bust Duct, Voltage Stabilizer in whole Pune Region and overall project management of awarded orders.
2) Having Developed Big customer base in Pune region useful for business development of Residential/Commercial/Industrial Projects.
3) Technical design as well as Commercial Bidding, Negotiation meetings with Customers, Market survey.
4) Authorised Panel Builder for Schneider & System House for Ti Design of L & T.
company - M/s ADOR WELDING LTD
description - Period: 17th July 2012 to 30th May 2017.
Nature Of Job - Industrial Electrical Design along with project Operation management:
1) Prepare Electrical Design Basis, Electrical Scope of work, Electrical Technical Specification, Electrical Data Sheet and Electrical Single Line Diagram (SLD) as per Project Standards.
2) Prepare and Review Electrical Drawings layouts like Hazardous Area Classification, Electrical Indoor and Outdoor Lighting Layout, Earthing and Lightning Protection Layout Drawing, Substation Equipment and Trench Layout Drawing Overall Cable Layout Drawing, Bill of Material (BOM) MR for Electrical Equipment and Cable Schedules.
3) Conversant with AutoCAD with 5 years of exp., introductory knowledge of ETAP, with Excellent Verbal / Written Communication.
4) 4+ years of experience with applying NEC, IEC, CSA, IECEx, ATEX, UL, FM, NFPA, and API codes and standards to Electrical Engineering Design.
5) RCA, DFMEA, Part of Design Modification & Innovation Team.
6) Technical Quotation, Technical Query and Technical Bid Evaluation, Co-Ordinate with Clients / Vendors during the Projects.
7) Vendor Development Team lead, Review Vendor Documents, Factory Acceptance Test (FAT), Site Acceptance Test (SAT)
Responsibilities Handled:
1) Project Lead for YIBAL - Petroleum Development of OMAN's project.
company - M/s PRAKASH INDUSTRIES LTD
description - Period: 1st Sept 2011 to 6thMay 2012.

Responsibilities Handled:
1) Operation & Maintenance of Various Electrical Drives (Related with CFBC Boiler as well as Turbine section)
2) Daily Operation & Routine Maintenance of 132kV AC Switch-yard.
Nature of Job:
1) Electrical Operation & Maintenance of 100 MW Power Plant.
2) Shift In-charge for 3 months
Certification Courses:
Certified Course in AutoCAD from CADD Centre, JM Road, Pune."
Electrical Engineering,"Education Details 
Electrical engineeringSkill Details 
Microsft office and excell, power point- Exprience - 120 monthsCompany Details 
company - Indian Navy
description - ACADEMIA
â¢ Matric
â¢ Diploma in Electrical Engineering
â¢ Nuclear biological Chemical Defence and Damage Control including Fire fighting
â¢ Leadership Certificate Course		-	02 weeks
â¢ Management Certificate Course		-	04 weeks
â¢ Advance Fire Fighting including Industrial
Safety		-	24 weeks"
Electrical Engineering,"â¢ Achievement oriented with people management skills and an ability to manage change with ease. â¢ Proven strength in problem solving, coordination and analysis. â¢ Strong communication, interpersonal, learning and organizing skills matched with the ability to manage stress, time and people effectively. â¢ Able to handle multiple task & projects simultaneously. â¢ Willing to travel & relocate.Education Details 
 B.E Electrical and Electronics Engineering Gulbarga, Karnataka VTU (Vishweshwariya University)
 HSC  Wai, Maharashtra Kisanveer Mahavidyalay WAI
    Kohlapur UniversityElectrical EngineerElectrical Engineer - REFCON ENGINEERING PVT. LTDSkill Details 
Company Details 
company - REFCON ENGINEERING PVT. LTD
description - GREENFIELD PROJECT: -		Project Status 
1) LIBS Braseries, Burkena Faso (Africa)		Completed 2) Citrus Processing India, Nanded		Completed 3) Carlsberg Group, Myanmar (Yangon)		Completed 4) U.B (United Breweries), Mysore.		Completed 5) U.B (United Breweries), Hyderabad.		Completed 6) Haldiram Foods Pvt. Ltd. Nagpur		Completed 7) Tetra-Pak India Pvt. Ltd. Amreli		Completed 8) U.B (United Breweries), Rajasthan		Completed â Handling Activities in Project: - 
â¢ Preparing project schedules / plans, engineering designs / drawings and technical specifications as per clients' requirements.
â¢ Review on Process P&ID's for Selection of Instrument to get Energy efficient process with Material Specifications. Approval & modifications of Electrical drawing as per requirement of Process Flow diagrams & General Electrical Standards.
â¢ Carry out Automation Engineering which includes hardware and software specification, Generate control philosophy for process automation as per P&ID process. Necessary technical documentation, O&M manuals.
â¢ Calculate the Estimation value of projects for price bidding with clients includes MCC & PLC panel, power & control cabling, trays & earthing material Etc.
â¢ Selection of Electrical Switchgear for HT, LT, PCC, MCC, APFC Panels, Power & Control cable, Motors, Protection & metering CT & PTs, ACBs, Earthing, lightning, Analog & Digital Instruments.
â¢ Floating project enquiries & communicate with Vendors, Evaluate Vendor quotes for Technical Alignment.
â¢ Generate Power distribution SLD drawings as per the load flow, General arrangement drawings for panels, Controllers logic diagrams, Developing of Cable route plan and schedules, Cable Tray layout on AutoCAD tool with detailing.
â¢ Installation, Commissioning & Troubleshooting of MCC, PLC & LT panels, VFD Drives, Soft-Starters, Programming of Danfoss, Schnider, Vacon, Siemens, ABB Drives.
â¢ Working experience on Siemens S7-300, 1200, 1500, Et-200S Series & Allen-bready RS-Logix5000 PLC.
â¢ Installation, Calibration & Troubleshooting of field Instruments as Pressure & Temperature Transmitter, Pneumatic valves, Actuators, Modulating valves, Solenoid valve, flow meter & flow switches, Flow Transmitter, Level Transmitter etc. Carry out Loop testing as per control cable schedule, â¢ Hands on experience in Troubleshooting, Engineering Services and Maintenance & Operation & managing of various electrical power system equipments. Supervising commissioning activities.
â¢ Handling Mechanical Maintenance & Service activities in operating plant.
company - KISANVEER SUGARS PVT. LTD
description - Handling Activities: - 
â¢ With over 1.5 years of experience worked as Asst. Electrical Engineer from ERECTION FIELD across 22 MW Power Plant for Testing and Commissioning, Electrical Maintenance, Operation, and allied activities.
â¢ Testing, Commissioning & installation of HT & LT panels, VFD drives & DC drives Panels.
â¢ Scheduling and planning predictive, preventive maintenance for process plants, and root cause analysis including budgeting and cost control activities.
â¢ Managing Overhauling and maintenance of LT Motors & DC Shunt Motors, Panel Switchgears, VFDs & soft starters, DG Set with AMF panel & Plant Lighting.
â¢ Installation, commissioning & Maintenance of Power & Distribution Transformer, ACB, VCB, SF6 Breaker, CT & PT units, FCBC Panels, UPS, â¢ Protection system include Microprocessor based relay, Numerical relay with relay co-ordination.
â¢ Monitoring plans/schedules to start maintenance activities & achieving targets and implementing maintenance systems During Shut down Time.
â¢ Daily Power monitoring, calculations and reporting on monthly basis."
Electrical Engineering,"Education Details 
July 2016 to May 2019 BE Electrical And Electronics Engineering Bhopal, Madhya Pradesh RGPV
July 2012 to May 2015 Diploma in Electrical Electrical Engineering Bhopal, Madhya Pradesh Rgpv university Bhopal
 Matric   GHS BHULIElectrical EngineeringSkill Details 
Basic Computer, Electrical Machine, Electrical wiring, Solar power Plant, Distribution Substation- Exprience - Less than 1 year monthsCompany Details 
company - Enigma Business Advisor
description - Solar Energy 
1. Solar power plant Installation
2. Maintenance 

Transmission and Distribution Line 
1.  Installation"
Electrical Engineering,"Education Details 
January 2012 to January 2013 B.E. Electrical  Shivaji University
September 2008 HSC  Pune, Maharashtra Pune University
July 2006 SSC  Pune, Maharashtra Pune UniversityElectrical EngineerElectrical Engineer - R K ELECTRICAL PVT. LTDSkill Details 
Company Details 
company - R K ELECTRICAL PVT. LTD
description - Experience:- 1 Year 3 Months

Troubleshooting and Maintenance of following Electrical Equipment:- â¢ All Type of Maintenance of Utility.
â¢ Electrical and Mechanical Maintenance.
â¢ Two 625 KVA Diesel Generator Set (Kirloskar)
â¢ HT/LT Switchgear With Protection System Using Relays and Provision For Interlocking (C&S, Kirloskar)
â¢ Handling HT Vacuum & SF6 Circuit Breaker, Transformer Up to 5000 KVA, LT Air circuit Breaker  2000A
â¢ Maintenance of STP and WTP Plant.
â¢ Maintenance of Air Blower, Actuators, Soft Starter, EOT Crane, Mono Rail, Centrifugal or Vertical Pumps, Hydraulic Machine, Rolling Machine, Lath Machine, Drill Machine, AHU, HVAC, Chiller etc.
â¢ Basic knowledge of PLC/SCADA Operation.
â¢ Trouble shooting of Switchgear and Control Panel, Pump and Motor
â¢ Maintenance of UPS, Battery Charger and Battery Bank
â¢ Motor Testing Both HT & LT Up to 450 KW
â¢ Monitoring and Controlling the 110V Control Panel and Relays Panel
â¢ Involved in Fault Finding & Relay Resetting
â¢ Monitoring and Correcting Power Factor
â¢ Service and Maintenance of Up to 55 KW Submersible Pump
â¢ Maintenance of MCC and PCC Panel
â¢ Servicing of Motor and Associated Component and Motor Operated Valve
â¢ Problem Solving of Power Contactor, Auxiliary Contactor Relay, CT and PT
â¢ Effecting Preventive/Predictive Maintenance Schedules Equipment in Order to Increase the Uptime/ Reliability
â¢ Maintenance & Operation in Day to Day Activity
â¢ Operation, Preventive Maintenance, Day to Day Breakdown Maintenance Conventional Maintaining of Log Book and Check List.
â¢ 33/22kV Main Feeder & 22/11kV Distribution Line Maint. & H.T/L.T S/S Break Down Work.

ELECTRICAL SAFETY (Knowledge of Various Aspect of Safety & Its Application)
â¢ Requirement, Familiar With Various Safety Equipment and Tools
â¢ Lockout, Tag out of Electrical Switchgear During Work
â¢ Issue of Work Permit Line Clearance to Work on Electrical Distribution Network
â¢ Requirement & Proper Usage of Protective Equipment
â¢ Accident Statistics"
Operations Manager,"Education Details 
 BCA   Vinayaka Missions UniversityOperations ManagerOperations ManagerSkill Details 
Operations Management- Exprience - Less than 1 year monthsCompany Details 
company - BNY Mellon International Operations (India) Pvt. Ltd
description - Organization: BNY Mellon International Operations (India) Pvt. Ltd.: Sept 2009- till date
BNY Mellon provides a broad range of products and services in the areas of Investment Management, Investment Services and Treasury Services. Our customers are institutions, corporations, and high-net-worth individuals, as well as consultants and advisors who work with them.

Line of Business (LOB): Revenue &Billing Services (R&BS)
Revenue and Billing Services is a Global Center of Excellence that generates invoices and collects fee revenue from Investment Services clients. 218 staff across five locations (Brooklyn, Pittsburgh, Syracuse, Pune, and UK) . Various Businesses supported are Asset Servicing, Corporate Trust, Treasury Services, Broker Dealer Services and Global Collateral Management.
Support U.S., EMEA, and APAC. Regions.

Job Profile: Operations Manager - BNY Mellon International Operations (India) Pvt. Ltd.

Responsible for daily operations of the Billing Department providing services to institutional clients globally. Led a team of professionals responsible for accurate and timely Account setups and Invoice Production on the Billing platforms like Advantage (Fiserv.) & FiRRe (SunGard) . Tasked with improving service, quality and reducing the overall unbilled revenue.
Provide guidance to staff in their professional development plans by facilitating appropriate coaching and feedback.
Other responsibilities include but not limited to audit, business resumption planning, evaluating emerging technologies, fee contract reviews and negotiations with vendors, human resources, hiring, performance management, employee personal development, rewards &recognition programs, and defining the strategic direction of the business unit.

Operations / Service Delivery Management
â¢ Monitor Client KYC's and Compliance monitoring for Institutional clients.
â¢ Manage support and train the team in the drafting and reviewing of standard operating procedures to ensure compliance with US and Global regulations for Billing.
â¢ Assist clients with contract reviews and negotiations.
â¢ Monitor, analyze and draft metrics reports on a weekly/monthly basis to track progress of compliance efforts to senior management.
â¢ Coordinate with internal stake holders and draft responses to escalated enquiries and client requests.
â¢ Participate in the Risk and Control Self Assessments (RCSA) concurrence meetings for the LOB.
â¢ Form the first Line of Defense as Operations and take accountability for any Operational Risk Events (ORE's)
â¢ Perform root cause analysis of issues and document them accordingly for incident reporting.
â¢ Ensure service delivery standards are always met. (Green- SLA/TAT)
â¢ Monitor employee productivity/efficiency and performance management.
â¢ Initiate the goal setting exercise for the operations team in the LOB.
â¢  Provide direction to employees according to established policies and management guidance.
â¢ Consistently meet the onshore and offshore stake holders to provide real time updates.
â¢ Organize meetings with functional peer groups to discuss monthly MIS.
â¢ Actively involved in hiring and resource management for all levels within the department.
â¢ Oversee the Employee Assistance Program (EAP) for the underperforming employees.
â¢ End to end Performance management of staff during semiannual and annual appraisals.
â¢ Established Training and Development as one of the top priority goal for the department and secured senior management funding.
â¢ Cut new-hire to on- the- job competency from three months to two, by introducing e-learning modules and Brainshark presentations.
â¢ Encourage the team to understand and communicate the company vision and competitive strategy for driving business. (PLAN TO WIN &PLAY TO WIN)
â¢ Initiate quarterly Risk and control self-assessments (RCSA) concurrence reviews with all the process stake holders.

Project Management/Transitions Management -PMO
Worked as a project manager in BNY Mellon PMO to manage the Transitions of Billing Operations.

The responsibilities included but were not limited to:

â¢ Understanding expectations, technology, documentation, assessing capacity & skillset
â¢ Executing transition plan (process timelines, SLAs, Escalation Matrix etc.)
â¢ Ensuring smooth go live and regular review meetings post transition.
â¢ Conducted analysis on projects to determine ROI and viability of project undertaking before presenting it to the Senior Operations Directors.
â¢ Conceptualized and implemented strategy for capturing Project and Program metrics and funneling that information to easy-to understand dashboards.
â¢ Direct Management oversight for a team of 11 employees, 8 of which were direct reports.
â¢ Work directly with the Information Technology Architecture team to identify strategic project breakdowns within the program.
â¢ Ensure complete adherence to project management and Toll gate framework.
â¢ Ensure users of the Operations team get the required application accesses.
â¢ Determined lessons learned through post-project reviews and integrated findings into program plan.

Talent Management (team & function)
â¢ Re-structured Organization design to differentiate roles, develop career paths, easy work rotation
â¢ Performed skill-gap analysis to identify Training and Development opportunities. Ensured participation and also tracked effectiveness of Training Programs
â¢ Developed and implemented Function-wide Rewards and Recognitions (R&R) programs.

Previous Job Profiles in BNY MELLON during overall tenure: Assistant Manager and Team Leader

Line of Business (LOB): Global Corporate Trust Billing (Enterprise Billing Unit) ; Alternate Investment Services (AIS), Broker Dealer Services (BDS) &Sub Custodian Fee Billing.

The Trustee services that are provided e.g. Admin Services, Paying agent services, Registrar by BNY Mellon to the Corporate Trust Clients are being billed and Invoiced for generating revenue.

Worked in the capacity of Assistant Manager and Team Leader during the overall tenure managing multiple teams for different sub functions within billing department.

Other responsibilities included:

â¢ Assign the Daily work to the team from the respective Mailboxes.
â¢ Ensure equal distribution of all sub activities among the team depending on the volumes and ensure timely completion.
â¢ Monitor the log spreadsheets for all the activities and sub activities which are saved in designated Shared Drive.
â¢ Quality Check of the Transactions processed.
â¢ Work on Efficiency projects.
â¢ Monitor the Onshore and RM emails and respond within the same day after getting them actioned.
â¢ Attend the Daily Handoff call to discuss processed work and updates with onshore counterparts.
â¢ Work on Efficiency /six sigma/Quality projects.
â¢ Conduct monthly one on ones with the team members and schedule team meetings.
â¢ Mentoring the new and old hires in the team, discuss process updates regularly.
â¢ Managing Performance appraisals of employees in the team.
â¢ To be actively involved in the hiring process.
â¢ Participate in Internal and External audits.
â¢ Attend concurrence meetings for Risk and Control self-assessment (RCSA)

WORK EXPERIENCE
company - Wipro Technologies
description - CLIENT: Microsoft

Wipro Ltd. is an Indian Information Technology services corporation; it's a leading global information technology, consulting and business process services company.

Job Profile: Worked as an ""Application Support Engineer Tier II''

â¢ Provided Technical support to Siebel CRM users across the globe from Microsoft Corp.
â¢ Communicated with the users to obtain an understanding of their environment and challenges to troubleshoot the issue and provide solutions.
â¢  Utilized existing structure to translate ticket categories & group names to the system structure.
â¢ Identified and escalated issues to the TierIII production support teams as required to ensure they were addressed and resolved.
â¢ Ensured all Siebel CRM user sites were up and running by utilizing proactive monitoring tools, which increased customer up time to 95%.
â¢ Trained new engineers in providing proactive support to users.
â¢ Compiled data and department metrics into reports for Management examination of processes. (MIS Reporting)
â¢ Coordinated development team with business users for system deployment and modifications.
â¢ Actively participated in the STB&QTP testing's during the application version releases.
â¢ Perform periodic Application health checks.
â¢ Tested, implemented, and supported database replication including SQL Server Replication types in an editing environment.
â¢ Perform Role flip of servers at the backend to reduce application downtime.
â¢ Maintained a consistent ETA of 85% month on month for help desk tickets within a 24-48 hour time span.
â¢ Ensured the Knowledge base article's repository is updated regularly, to enable the new Engineers to go through them for troubleshooting issues.

WORK EXPERIENCE
company - Dell Global Financial Services
description - for  Dell  Computer  Corporation, Aug 29th' 2005 till  Feb '2007.

DELL Inc. is one of the world's leading computer systems company. Dell designs, builds and customizes products and services to satisfy a range of customer requirements from the server storage and professional services needs of the largest global corporations, to those of customers at home across 85 countries of the world. Dell does business directly with the customers to meet unique needs.
Job Profile: Worked as an ""Account Receivables Senior Associate"" for DELL Inc.
â¢ Handling a portfolio of about 500 U.S corporate customers.
â¢ Handling a team of 8 senior credit controllers.
â¢ Walking the floor (Coaching and guiding the controllers)
â¢ Minimizing credit losses by managing the team to collect delinquent accounts.
â¢ Handling Escalations (through Emails and over the phone)
â¢ Identify late stage delinquent accounts and resolve with appropriate verbal and written correspondence or notices.
â¢ Performing Ledger/ Accounts review
â¢ Discussing Interim and Annual Appraisals with the team members and to ensure their closure in the stipulated time.
â¢ Research and resolve problem transactions identified on a daily basis Maintain records concerning changes in accounts as a result of negotiations on the collection of customer's outstanding accounts, refunds, tax credits, etc.
â¢ Release Orders with Credit Task Codes from Assigned Queues.
â¢ Intervening & communicating with Supporting Departments such as Sales, Order Processing etc. when there is delayed/no response from the same.
â¢ Mentor New hires and assists team members in improving performance as required.
â¢ Setting up and Leading the conference calls with customers and other internal departments to discuss the issues on the accounts and deriving measures to resolve them.
â¢ Taking team hurdles and driving team results. Attending weekly and monthly review calls with the Management to discuss process related issues and ways to improvise them.
â¢ Work through real time cases made up of potential order errors or situations where specific customer requirements are not met.
â¢ Makes decisions on how to resolve issues in most expeditious manner.
â¢ Makes recommendations on ""Business Rules"" additions designed to capture commonly occurring order errors.
â¢ To keep the motivation level high among the team members by organizing Fun at work activities. Also have been nominated as the HR and Transport SPOC for the COE.

WORK EXPERIENCE
GE CAPITAL INTERNATIONAL SERVICES. (GECIS)
GE Capital is the financial services unit of the American multinational conglomerate General Electric. It provides commercial lending and leasing, as well as a range of financial services for commercial aviation, energy and support for GE's Industrial business units.
company - 
description - Made outbound calls and sent emails informing Institutional clients about their delinquent payments as per Standard operating procedures.
â¢ Developed One Point Lessons for key customers and shared with the team
â¢ Maintained 95%+ accuracy rate throughout the year.
â¢ To generate a daily report on the team's performance, this included ways and means of improvement in COE revenue.
â¢ Had gained credibility among the team members and first level of escalation for any issues in the absence of the manager. Been single point of contact (SPOC) for any HR issues in team."
Operations Manager,"KEY COMPETENCIES â¶Multi - Operations Managementâ¶People Management â¶Customer Services - Emails â¶ MIS â¶Vendor & Client Services Managementâ¶Cross Functional Coordinationâ¶Banking & Financial Servicesâ¶ Transaction Monitoring * ATM Operations â¶ & Prepaid Card Operations (Pre-Issuance & Post-Issuance) â¶ POS Operations * JOB PROFILE & SKILLS: â¢ An effective communicator with excellent relationship building & interpersonal skills. Strong analytical, problem solving & organizational abilities. â¢ Extensive experience in managing operations with demonstrated leadership qualities & organisational skills during the tenure. â¢ Managing customer centric operations & ensuring customer satisfaction by achieving service quality norms. â¢ Analyzing of all operational problems, customer complaints and take preventive and corrective actions to resolve the same. â¢ Receive and respond to Key customer inquiries in an effective manner and provide relevant and timely information. â¢ Deft in steering banking back-end operations, analyzing risks and managing delinquencies with dexterity across applying techniques for maximizing recoveries and minimizing credit losses. â¢ Analyzed & identified training needs of the team members and developing, organizing and conducting training programs and manage bottom quartile team to improve their performance. â¢ Preparing and maintaining daily MIS reports to evaluate the performance and efficiency of the process relate to various verticals. â¢ Measuring the performance of the processes in terms of efficiency and effectiveness matrix and ensuring adherence to SLA. â¢ Major Activities Define processes for Field Services were monitored and necessary checks were executed and controlled. Also measured Vendor SLA by analyzing the TAT of vendors & the Client SLA provided to us. â¢ As per company procedures, handling & ensuring vendor's payment issues to be sorted out &payments are processed on quarterly basis. â¢ Appropriately plan and execute each skill of operations in accordance with the department's policies and procedures. â¢ Manage relationships with business team, software development team and other services to achieve project objectives. Different software Worked till now: - a. CTL prime - Axis Bank Credit Cards b. Insight - For POS Machine technical operations for Amex (MID & TID Generation- ATOS (Venture Infotek) c. Ticket Management System - TATA Communications Private Services Ltd (ATM - NOC Operations) d. Branch Portal (Yalamanchili Software Exports Ltd) - Prepaid Cards (SBI Bank & Zaggle Prepaid Oceans Services Ltd) Zaggle Prepaid Ocean Services Pvt Ltd Oct, 2017 to Till Date Designation: Manager - Operations (Payment Industry - Prepaid Cards - INR) Education Details 
  Commerce Mumbai, Maharashtra Mumbai UniversityOperations ManagerService Manager - Operations (Payment Industry - Prepaid Cards - INR & FTC)Skill Details 
OPERATIONS- Exprience - 73 months
SATISFACTION- Exprience - 48 months
TRAINING- Exprience - 24 months
NOC- Exprience - 23 months
POINT OF SALE- Exprience - 20 monthsCompany Details 
company - Zaggle Prepaid Ocean Services Pvt Ltd
description - Card Operations
company - Yalamanchili Software Exports Ltd
description - 24*7 Operations Pvt Ltd) Dec 2015 to Feb 2017

Designation: Service Manager - Operations (Payment Industry - Prepaid Cards - INR & FTC)

Key Contributions: â¢ A result-oriented business professional in planning, executing& managing processes, improving efficiency of operations, team building and detailing process information to determine effective result into operations.
â¢ Ensuring PINs generation (SLA) is maintained and chargeback cases are raised in perfect timeframe.
â¢ Managing email customer services properly and ensuring the emails are replied properly. Also, ensuring transaction monitoring is properly managed 24/7.
â¢ Assisting Bankers (SBI & Associated Banks) for their BCP plans by getting executed in the system with the help of DR-PR plans & vice versa or any other business requirements.
â¢ Expertise in maintaining highest level of quality in operations; ensuring adherence to all the quality parameters and procedures as per the stringent norms.
â¢ Lead, manage and supervise the execution of external audit engagements and responsible for presenting the findings & developing a quality reports to the senior Management and Clients.
â¢ Coach/mentor (20) team members to perform at a higher level by giving opportunities, providing timely continuous feedback and working with staff to improve their communication, time management, decision making, organization, and analytical skills.
â¢ Providing the solutions and services to the client in their own premises with aforesaid count of team members.
â¢ Also ensuring end to end process of PR & DR as per client requirements (PR- DR & DR -PR) by interacting with internal & external stakeholders.
â¢ Determining process gaps and designing & conducting training programs to enhance operational efficiency and retain talent by providing optimum opportunities for personal and professional growth.
company - Credit Cards
description - Ensured highest standard of customer satisfaction and quality service; developing new policies and procedures to improve based on customer feedback and resolving customer queries via correspondence, inbound calls & email channels with the strength of (12-16) Team members.
company - AGS Transact Technologies Limited
description - Key Contributions: Lead - SPOC to Banks
company - TATA Communications Payment Solutions Ltd
description - To make ATMs operational within TAT by analyzing the issue is technical or non-technical and also by interacting with internal & external stakeholders.
company - Vertex Customer Solutions India Private Ltd
description - Key Contributions: â¢ Build positive working relationship with all team members and clients by keeping Management informed   of KYC document collection & con-current audit progress, responding timely to Management inquiries, understanding the business and conducting self professionally.
company - Financial Inclusion Network & Operations Limited
description - Key Contributions: POS-Operations â¢ Cascading the adherence of process is strictly followed by team members & training them to reduce the downtime.
â¢ Managing Stock of EDC Terminals â¢ Managing Deployments of terminals through Multiple teams â¢ Would have worked with multiple terminal make & model â¢ Managing Inward, Outward & QC of applications installed in the POS machines.
company - Venture Infotek Private Ltd
description - Key Contributions: POS-Operations
company - Axis Bank Ltd - Customer Services
description - Aug 2006 to Oct 2009 (Ma-Foi&I- smart)

Designation: Team Leader/Executive - Emails, Phone Banking & Correspondence Unit (Snail Mails)"
Operations Manager,"IT SKILLS â¢ Well versed with MS Office and Internet Applications and various ERP systems implemented in the company ie.SAGE, Flotilla, LM ERP, Tally 9, WMS, Exceed 4000 etc PERSONAL DOSSIER Permanent Address: Bandra West, Mumbai 400 050Education Details 
 B.Com commerce Mumbai, Maharashtra Bombay University
   Mumbai, Maharashtra St. Andrews College
 DIM Business Management  IGNOUOperations ManagerOperations Manager - Landmark Insurance Brokers Pvt LtdSkill Details 
EMPLOYEE RESOURCE GROUP- Exprience - 6 months
ENTERPRISE RESOURCE PLANNING- Exprience - 6 months
ERP- Exprience - 6 months
MS OFFICE- Exprience - 6 months
Tally- Exprience - 6 monthsCompany Details 
company - Landmark Insurance Brokers Pvt Ltd
description - Jan 2019 till Date
About the Company
One of India Largest Insurance Brokerage firms with offices across 24 states PAN India and a part of the LandmarkGroup with an annual turnover of 2200 cr

Position: Operations Manager
Leading and overseeing a team of 12 people to ensure that the correct work processes and TAT are followed with regards to complete insurance handling from cheque submission right upto policy issuance and support to all offices PAN India for Motor and Health Insurance.
 â¢   Ensuring all the data entry of customers is correctly done by the customer service â¢   Underwriting of documents and verification ( Health & Motor) â¢   Costings of Insurance products â¢   Followups with Insurance companies â¢   Customer complain handling and solutions â¢   MIS reporting â¢   Ensuring the team is working in line with the operations SOP â¢   Conducting weekly briefing with the team â¢   Liasing with all PAN India offices for all operational support â¢   Handling daily cash transactions â¢   Reporting to the Vice President Operations
company - Trippereri Travels & Tours
description - Jan 2017 to Mar 2018
About the Company
A Startup Travel Agency company organising local and international Tours.

Operations: Looked after overall Operations and Administrative / Sales functions
company - Vish Hotel Supplies Pvt Ltd
description - Jan 2015 Nov 2016

About the Company
A distributor of hotel supplies to 5 star International hotels Groups in India for hotel amenities from a international supplier to more than 50 hotels all across India

Position: Operations Manager (Supply Chain Logistics)
Complete Incharge of running the business from Shipping, Imports, warehousing right upto Distribution and overseeing all operational related activities for the company 
â¢ Overlooking and managing all aspects of the business and implementing procedures for successful shipping / inventory management at the warehouse / delivery of material all India plus exports â¢ Overseeing all vendor negotiations in addition to the customs warehouse and cha â¢ Negotiating with Freight forwarders for best freight rates and quickest delivery times â¢ Overseeing the clearance of import shipments and export shipments and liasing with freight forwarders â¢ Ensuring that all government and tax compliances are adhered to by the company.
â¢ Ensuring that all benefits of government rebates and duty schemes are received and availed by the customer and company as per the EXIM license held â¢ Overseeing that all customers are satisfied with the overall supplies and services of the company â¢ Ensuring that all obstacles faced in the supply chain management is smoothened out for hassel free delivery to the customers across India â¢ Overseeing account paybles and receivables â¢ Overseeing that all stock are maintained in the warehouses and accounting books as per implemented procedures â¢ Sourcing out new vendors with high quality manufacturing capabilities for new product manufacture â¢ Visiting customers to survey customer satisfaction and address any shortfalls â¢ HR function like recruitment, interviewing & finalizing candidates for the company
company - GEA Ecoflex Middle East FZE
description - About the Company
GEA Ecoflex part of the GEA Group Germany is one of the largest suppliers of process technology and components for the food and energy industries. As an international technology group, the Company focuses on sophisticated production processes.
GEA generates revenues in excess of EUR 5.7 billion annually

KEY DELIVERABLES
Freight Management: Acquire, develop and enhance relationships for economical & faster modes of freight forwarding for various requirements of the company. Developing pricing strategies with an with an eye towards maximizing company's profits by reducing its freight costs by negotiating the pricing for each individual sectors with freight forwarders/shipping carrier and ensuring timely delivery of goods to the respective destinations.

Manage and negotiate the import freight charges with various freight forwarders and Air Lines for Bulk Air shipments moving from Americas, Europe, Dubai, India, China & Other GCC Countries.
Identify possible snag & loopholes for all consignments moving in and out, which may be late for the required deadline and find alternate solutions for meeting the commitment.

Logistics Management: Managing the logistic functions; negotiating with transporters for cost effective transport solutions.
Coordinating with Custom House Agents for ensuring timely clearances and effective transport solutions at reasonable costs.
Interfacing with airlines & shipping lines for transport & conducting negotiations for finalizing freight rates to optimize transportation costs & ensure damage free transit. Monitoring and analyzing the loss in transit and undertaking measures to control the same. Co-ordinated the schedule / mobilization of After sales team to various GCC/ On-shore / Off shore /European sites.
Materials / Inventory Management: Handling the inventory function so as to curtail inventory holding expenses. Ensuring the specifications of materials, establishing quality & quantity for effective inventory control and reducing wastages.
Managing the disposal of obsolete and retired inventory.

Warehousing & Distributions: Planning and maintaining efficient warehouse operations. Monitoring receipt, inspection, storage, and distribution of stock. Resolving all problems affecting the stores service including staff punctuality, day-off, vacation plan, etc. Overseeing that all paper work is processed in a timely manner. Following up on discrepancies, damage/expired goods and claims & returns back.

Purchase / Procurement: Providing support for developing and implementing key procurement strategies for commodities and ensuring that plans are aligned with all contractual & statutory requirements while ensuring project schedule. Identifying new potential vendors for strategic sourcing; ensuring purchase schedules against the purchase plan for timely procurement of all items to ensure smooth manufacturing.

Vendor / Supplier Development: Providing support for identifying and negotiating with vendors for procuring essential materials at reasonable price, developing vendors for better price, quality, delivery & increased volumes and identifying alternate vendors. Developing long-term partnerships with local & foreign suppliers; acquiring techno-commercial offers from various vendors. Handled Supplier Pre-Qualification and Vendor Registrations

SIGNIFICANT CONTRIBUTIONS 
â¢   Successfully managed the overall Operations including: o   Receiving stock, Order Dispatch, Warehouse Management & Special Processes.
o   Inventory Control and Global Purchase.
o   Facility in compliance with ISO 9001 -2008 standards.
o   Coordinating and follow up with various factories in Europe, US, Asia for timely delivery of materials o   Carrying out receiving, picking, packing, and shipping average of 45 orders monthly.
o   Maintaining and handling AED 15 million inventories stored at three external warehouses.
â¢   Holds the credit of serving clients in Power, Marine Oil and Construction industries including SABIC, KSA.
â¢   Played a key role in streamlining inventory identification and tracking system.
â¢   Pivotal in introducing systematic analysis of daily discrepancy reports.
â¢   Generated reports on Standardized receiving, stocking, checking, and housekeeping procedures.
â¢   Abridged a decrease in inventory discrepancies by developing tracking system.
â¢   Successful in meeting 24-hour turnaround goal for most of shipments.
company - Kuehne + Nagel LLC
description - About the Company

A worldwide leader in Logistics .The company activities are in the global seafreight, airfreight, overland and contract logistics businesses.
 â¢   Successfully managed a team of 3 Executives and reported to General Manager.
â¢   Handling the top Key accounts of the company â¢   Successful handling of the entire export Coordination, Documentation.
â¢   Played a pivotal role in supervising receiving, inventory control, storage, distribution, traffic, etc.
â¢   Responsible for maintaining scheduled drivers, negotiated rates and routes with truck lines/carriers.
â¢   Essayed a key role in handling product distribution, security, and receivables for clients like United nations and Johnson & Johnson.
â¢   Handled Hotel logistics, inventory and distribution across the middle east for Marriot and Hilton Group â¢   Managed and monitored the smooth movements of all consignments moving as direct imports, exports or as transshipment consignments from the Far East/ Middle East/ Amman or Egypt and its other branches to its respective destinations & also as transshipment into USA by Air, Land and Sea.
â¢   Supervised the Entire Customs Clearance for all consignments at various exit/entry points for all the direct imports, exports or transshipment consignments â¢   Billing each Account as per the job completed â¢   Keeping track of Revenue of each client on a monthly basis â¢   Regular customer visits to ensure smooth operations and address any grievances.
company - DHL Express
description - About the Company

A worldwide leader in Air Express Courrier wholly owned by DPWN (Deutsche Post World Net) 
â¢   To handle shipments of Key Accountholders with DHL India â¢   Tracking and Tracing of shipments â¢   Solving problems of undelivered shipments â¢   Working as per the DHL standards and service procedures â¢   Liasing with operations and network at all DHL stations worldwide to ensure timely deliveries of   shipments â¢   Proactively keeping the customer informed about the status of their shipments while in transit to delivery â¢   Keeping track of the productivity of each team member and updating records â¢   Liasing with India operations for inbound freight shipments for clearance and timely deliveries â¢   Informing customers about the various paperworks required for sending different shipments to various countries â¢   Handled projects for State Bank of India for sending Interest warrants to their customers Globally. Keeping complete track of shipments status and informing SBI proactively.
company - WNS Global Services
description - 
company - Airlink International
description - About the company
An International company with various departments and activities i.e Cargo, shipping, Ticketing
And freight forwarding

Designation Held: Logistic Assistant / Operations Executive

Job responsibilities: â¢ Handling the top accounts of the company.
â¢ Answering customer queries, request and complaints â¢ Sending quotations â¢ Billing each Account as per the job completed â¢ Preparing export documentation.
â¢ Preparing free Zone documents â¢ Liaising with Jebel Ali Port and Dubai / Sharjah port.
â¢ Liaising with Shipping companies for export and imports.
â¢ Coordinating Barge loadouts.
â¢ Complete Inventory Management.
â¢ Adhering to professional standards and procedure to achieve the ISO certificate.

Worked on software packages Exceed 4000 and Flotilla.
company - Serck Services International
description - Job responsibilities â¢ Liaising with the Purchasing Manager in local and international purchases of technical parts, â¢ Involving import and export formalities.
â¢ Soliciting quotes, preparing purchase orders and communicating with suppliers both local and international.
â¢ Receiving and dispatching goods â¢ Liaising with suppliers to ensure timely supply of equipments â¢ Stock control and inventory.
â¢ Maintenance of reorder levels and par stock.
â¢ Preparing the daily operations report â¢ Maintaining the ISO standards, which involves efficient recording of data and systematic filing of data.
company - Serck Services International
description - March 2000 - April 2003
About the company
A British based multinational company, manufacturing radiators, oil coolers, heat exchangers and
Other cooling elements for all purposes
Joined the company as Accounts Assistant and promoted to Logistic Assistant in Aug 2001
company - Serck Services International
description - Job responsibilities â¢ Maintaining and updating the database of customers and suppliers.
â¢ Handling walk in customers and after sales service calls. Preparing Job order.
â¢ Providing timely information and data for the preparation of reports.
â¢ Circulating information through memos and reports.
â¢ Invoicing for four major departments.
â¢ Banking â¢ Petty cashiering and preparing reports â¢ General office duties."
Operations Manager,"Education Details 
August 2000 B.E Electronics Pune, Maharashtra Pune UniversityOperations ManagerOperations Manager - Delta Controls, Dubai FZCOSkill Details 
Company Details 
company - Delta Controls, Dubai FZCO
description - Heading Pune Branch [M/s Deltannex Integrators Pvt. Ltd.]                        From Aug '17 till date
Designation - Operations Manager [1st Employee] 
1. Reporting to the GM-Operations 2. Review of SOW, RFQ, Assist the proposal Team on the Engineering man hours, Project Schedule, Organization
Chart and Meeting the client for pre-sales support 3. Review of SOW, RFQ, Client's Purchase Order or contract terms & conditions 4. Project Execution Plans after through discussion with client & consultants 5. Kick of meeting & Ad-hoc meetings with client 6. Project evaluation in terms of outlays & profits 7. Ensuring an appropriate project management framework 8. Management of all contractual and commercial issues related to the project 9. Monitoring, reviewing & reporting of the project progress 10. Coordinating and fostering teamwork & prioritization of team activities 11. Monitor the project budget 12. Apply quality management system and processes 13. Liaison with and reporting to client.
14. Monitoring & complying with invoicing schedules.
15. Compilation and submission of Change Orders / Waivers / Concessions. Responsible for Variation claims with support of proposal team 16. Approving the MRN & follow up with procurement team 17. Planning & Co-ordination of site activities 18. Receive final payment & project official closeout 19. Give a Constant Feedback to proposal based on the lessons learnt to correct the earlier mistakes in the future Proposal 20. Preparation of Project Management Reports in terms of Planned vs Actual based on EV Analysis and Scheduled Variances on biweekly / Monthly basis 21. Exception/ Alert reporting to GM on critical issues 22. Maintains contact with all clients in the market area to ensure high levels of client satisfaction 23. Adheres to all company policies, procedures and business ethics codes and ensures that they are communicated and implemented within the team.
24. Transfer the project to the Project Team with proper internal kick-off and provide all the information including e-mail communication.
25. Clarification e-mail/ phone to customer if any and responses from Customer 26. Site visit for Sales & technical discussions 27. Co-ordinates with Accounts for Tender Bond if any 28. Submission of offers/Tenders 29. Attending TQs/ Revisions / changes from customer 30. Managing revision of offers and costing and submittal if any
company - HONEYWELL AUTOMATION INDIA LTD
description - since May 2015 to June 2016 at HONEYWELL, Seoul, South Korea]

Roles and Responsibilities: 1. Discuss with Honeywell Korea LE / PM and Engineering Manager, understand the work forecast and communicate it to GES stake holders.
2. Understanding Project / Work pack scope and collect required design inputs from Honeywell Korea PM and LE.
3. Communicating Project / Work pack scope and sending required design inputs to GES stake holders (OM / EM) 4. Helping GES office in preparing estimation for GES scope of activities.
5. Discuss GES estimation with Honeywell Korea PM / LE.
6. Coordinate between GES and Honeywell Korea PM / LE so that estimation is agreed by both parties.
7. Helping GES in preparing JAs.
8. Coordinate with Honeywell Korea PM / PC to get purchase orders as per JA.
9. Coordinate with GES and Honeywell Korea for following activities during project execution: a. Understanding GES queries and getting them resolved by discussing with Honeywell Korea LE.
b. Communicating Honeywell Korea requirements / schedules / revised design inputs / revised scope / Concerns / issues / Honeywell Korea comments / customer comments to GES.
c. Understanding GES Change orders and discussing with Honeywell Korea LE / PM.
d. Finalizing change orders.
e. Ensure good quality of GES deliverables by doing spot quality checks.
f. Ensuring that all the procedures standards are being followed at both sides.
g. Ensuring smooth project execution as per agreed project schedules.
h. Ensuring that Deliverables are being sent as per agreed deliverables schedule.
i. Identifying possible issues in project and suggesting corrective actions to both GES & Honeywell Korea.
j. Scheduling Project review meetings whenever required.
k. Attending Proto type tests / demo / Design Review meetings / Pre-Inspection Meetings / Pre KOM / KOM whenever required.
l. Ensuring engineer's travel for Pre-FAT / FAT / attending important meetings like KOM, Proto type tests, etc. as mutually agreed for the project.
m. Arranging invitation letters / CCVI / Work permits, etc. for visa processing of GES engineers.
n. Ensuring that progress reports are being sent regularly by GES EM.
o. Ensuring that Pre-FAT / FAT is progressing smoothly.
10. Preparing and sending Monthly Progress reports to all the stake holders.
11. Arranging monthly teleconference with Honeywell Korea PAS Business Leader, Engineering Manager and GES Operations Manager to understand the progress and issues.
12. Attending Project review meetings with PM / LE / EPC whenever required.
13. Helping Honeywell Korea Sales / Proposal team in understanding GES capabilities and providing required information to them.
14. Reviewing customer RFQs and ensure that all requirements are captured in proposals or informing the requirements / comments to proposal team / Project Manager.
15. Attending estimation review meetings, TBEs, Technical clarification meetings whenever required.

Projects Completed at HONEYWELL, India:

Project (HONEYWELL)   Role   Project Highlights
FGP/WPMP
Client: TCO
MAC: MUSTANG/HONEYWELL

Team Lead
System: EPKS DCS + SIS + FGS
Job:- â¢ HARDWARE Configuration â¢ C-300 Application Development â¢ Timely delivery of the deliverable after ensuring quality check.
â¢ Attending weekly project review meetings (telephonic) with client.

THE PROJECT IS STILL IN STARTING PHASE SO SCOPE OF WORK IS NOT VERY CLEAR.

Grain LNG RTLF
Client: CBI / National Grid, UK

Team Lead
Process: LNG Tanker Loading
System: EPKS DCS + SIS + FGS
C-300 Controller = 1 No.
Job:- â¢ Planning and Monitoring the below mentioned engineering activities- â¢ Design and development of all HARDWARE part as below:- â¦ System Cabinet -01 No â¦ Marshalling Cabinet - 01 No.
â¦ Network Cabinet 01 No.
â¦ System Architecture â¦ Heat and Load Calculation â¦ BOM â¦ Network and Miscellaneous cord schedule â¦ Wiring Schedule â¢ C-300 Application development monitoring â¢ HMI C&E Graphics development monitoring â¢ Testing of simple and complex loops using standard methodology along with HMI (Integrated Testing + Test Reports) â¢ Focal point for communication with client â¢ Timely delivery of the deliverable after ensuring quality check.
â¢ Attending weekly project review meetings (telephonic) with client
This was a FAST track project completed successfully
Got Appreciations from client.

AMC TO HPM Migration-MX8800
Client: Thai Oil Public Co. Ltd.

Team Lead
Process: Furnace
System: TPS
HPM Controller = 1 No.
Job:- â¢ Planning and Monitoring the below mentioned engineering activities- â¢ Loop drawing design using Microsoft VISIO â¢ Logic Point building using HPM builder â¢ PU Calculation and IO Allocation â¢ HPM Database preparation â¢ Downloading of points on HPM test system â¢ Testing of simple and complex loops using standard methodology â¢ Focal point for communication with client â¢ Timely delivery of the deliverable after ensuring quality check.
â¢ Attending weekly project review meetings (telephonic) with client

AMC TO HPM Migration-CCR1
Client: Thai Oil Public Co. Ltd.

Team Lead
Process: Furnace
System: TPS
HPM Controller = 1 No.
Job:- â¢ Planning and Monitoring the below mentioned engineering activities- â¢ Loop drawing design using Microsoft VISIO â¢ Logic Point building using HPM builder â¢ PU Calculation and IO Allocation â¢ HPM Database preparation â¢ Downloading of points on HPM test system â¢ Testing of simple and complex loops using standard methodology â¢ Focal point for communication with client â¢ Timely delivery of the deliverable after ensuring quality check.
â¢ Attending weekly project review meetings (telephonic) with client

Utilities and Offsites
Client: ENPPI
End Client: ETHYDCO, EGYPT

Team Lead
Process: Utilities for ETHYLENE plant.

System: Experion R410.2,
Redundant Controllers = 2Nos.
System Cabinets = 4 Nos.
Marshalling Cabinets = 10 Nos.
Redundant Servers = 1 Pair
Experion Backup Restore = 01 No
Process History Database = 01 No.
FDM Server = 01 No.
Engineering Station - DCS = 01 No.
Operating Stations - DCS = 05 No.
Job:- â¢ Planning the activities like Hardware Engineering, Software Engineering and HMI development..
â¢ Responsible for Hardware Engineering for System, Marshalling, Network, Power Distribution Cabinets etc.
â¢ Preparation of BOM â¢ Answering the queries or doubts from Client â¢ Attending weekly project status meetings with client through video/audio conference.
â¢ FAT completed, at Abu Dhabi, successfully.

Control Plant-3/ Control Plant6
Client: JBK Controls
End Client: Qatar Foundation
Qatar.

Team Lead
Process: HVAC
System: ML200 R (PLC) + Experion
R410.2 (For SCADA only)
Redundant Controllers = 26 Nos.
System/ Marshalling Cabinets = 60 Nos.
Redundant Servers = 2 Pair
FDM Server = 01 No.
Engineering Station - DCS = 02 No.
Operating Stations - DCS = 10 Nos.
Job:- â¢ Monitored the Wiring schematic preparation activity for all 30 PLC control panels (UPS + SYSTEM + MARSHALLING) â¢ Monitored the control panel manufacturing activity for all 30 PLC panels.
â¢ Monitored the internal testing activity of all 30 PLC panels.
â¢ Monitored the FDS document development activity.
â¢ Monitored the FAT document development activity.
â¢ Performed the 2nd level Quality Checks for Wiring Schematics, FDS and FAT document.
â¢ Delivered all the deliverables as mentioned above on time.
â¢ Successfully completed the Pre-FAT and FAT, in presence of client from QATAR.

NORTH SIDE UTILITY TUNNEL HVAC System.
Client: JBK Controls
End Client: Qatar Foundation
Qatar.

Team Lead â¢ Monitored the Wiring schematic preparation activity for all 28 PLC control panels (UPS + SYSTEM + MARSHALLING) â¢ Monitored the control panel manufacturing activity for all 28 PLC panels.
â¢ Monitored the internal testing activity of all 28 PLC panels.
â¢ Monitored the FDS document development activity.
â¢ Monitored the FAT document development activity.
â¢ Performed the 2nd level Quality Checks for Wiring Schematics, FDS and FAT document.
â¢ Delivered all the deliverables as mentioned above on time.
â¢  Successfully completed the Pre-FAT and FAT, in presence of client from QATAR.

TRUCK MARSHALLING AREA HVAC System.
Client: JBK Controls
End Client: Qatar Foundation
Qatar.

Team Lead â¢ Monitored the Wiring schematic preparation activity for all 6 PLC control panels (UPS + SYSTEM + MARSHALLING) â¢ Monitored the control panel manufacturing activity for all 6 PLC panels.
â¢ Monitored the internal testing activity of all 6 PLC panels.
â¢ Monitored the FDS document development activity.
â¢ Monitored the FAT document development activity.
â¢ Performed the 2nd level Quality Checks for Wiring Schematics, FDS and FAT document.
â¢ Delivered all the deliverables as mentioned above on time.
â¢  Successfully completed the Pre-FAT and FAT, in presence of client from QATAR.

Graphics Development
Client: Chevron Oronite.
Singapore.

Team Lead â¢ Prepared the estimation for GRAPHICS DEVELOPMENT job using EPKS HMIWEB Display Builder.
â¢ Completed the development activity within schedule with a very proficient team of 3 engineers.
â¢ Performed the 2nd Level Quality Check for all 66 graphics.
â¢ Delivered the developed graphics as per the delivery schedule.
â¢ Very few defects / comments noticed by clients. (which were accepted and implemented)

TGI Modernization
Client: KH Engineering B.V.
End User: Shell Nederland Raffinaderji B.V.
Amsterdam

Team Lead â¢ Application engineering for Highway Gateway (HG) to EPKS migration based on CMPI database â¢ Application development for Furnace COIL BALANCING loop â¢ Flow Compensation logic development â¢ HMI testing and converting old system HMI scripts to ACE Control Modules â¢ Performed Quality Check activities by extracting logic parameters 
â¢ Trainings Completed: -
company - FOX CONTROLS
description - â¢ Strategically planning and analyzing the basic requirements while setting up the technical infrastructure of the project and reviewing all the project proposals.
â¢ Accountable for PLC and SCADA based application development.
â¢ Perform engineering and commissioning of Process Plants using PLC, DRIVES and SCADA.
â¢ Carrying out thorough Documentation of Engineering details related to the project.
â¢ Organizing and managing resources while creating estimates for the project, Work Breakdown Structure, Project Plan, Contingency Plan & Schedules, preventive maintenance related to instrumentation, PLC, identifying risks within defined scope, quality, time and cost constraints â¢ Interacted with the customers\clients on their projects for providing technical advises & feedbacks, creating high-level requirements.
â¢ Schedule tracking in coordination with site to ensure timely delivery of the Project as planned.
â¢ Proficient in various documentation processes.
â¢ Expert in ensuring the effective utilization of resources; human, material, and facility. Plans and project schedules, tests etc â¢ Adopt at maintaining perfect coordination with the work team & all internal/external parties to freeze the technical parameters/ work scope to iron out any ambiguities; carrying out work measurements and providing technical validation for jobs.
 *Please see Annexure for project details
company - Fox Controls, Dubai
description - Worked as a foundation member for this startup of business by contacting local customers and providing services as per their requirements.

ADDITIONAL RESPONSIBILITY: â¢ Worked as Management Representative for maintaining the ISO 9001:2008 QUALITY SYSTEM.
â¢ Carried out the INTERNAL AUDITS for all the departments in the company for the year 2011.
company - ENERCON (INDIA) LIMITED
description - â¢ Completed 7 projects of capacity ranging from 1.2 MW to 8.4 MW as a Commissioning Leader.
â¢ Got hands on experience with Inverter-Converter system, various power devices like IGBTs, thyristors etc.
â¢ Handled SAP system"
Python Developer,"Technical Skills / Responsibilities: â¢ Hands on Experience with Production and Maintenance of Projects. â¢ Experience in handling projects in agile methodology. â¢ Experience in handling projects in SDLC, Involved in each stage of Software Development Life Cycle. â¢ Responsible to gather requirement (Customer Interaction) and providing Estimate & solution document then as per process FS, TS, Coding, UTP, UTR, PTF, SOW submission to customer. â¢ Having strong knowledge of Debugging and Testing based on Python and AS/400. â¢ Worked as Change Controller - Responsible for promoting changes in Development to UAT and LIVE environment through Pivotal Cloud Foundry. â¢ Have good communication skills, Inter personal skills, hardworking and result oriented as an Individual and in team. Certification and Trainings: â¢ Completed Internal Python training. â¢ Completed Internal Python Web Crawling training. â¢ Completed Internal Python Web Scraping training. â¢ Completed Internal Python for Data Science training. â¢ Completed Internal MongoDB training. â¢ Completed Internal MySQL training. â¢ Completed Internal PostgreSQL training. â¢ Completed Internal DJango training. â¢ Completed Internal Angular 6, HTML, CSS training. â¢ Completed German A1 level and preparing for A2 from Goethe-Institute. â¢ Completed Internal Core Java training. â¢ Completed IBM I series AS\400 Training course at Maples Institute, Pune. â¢ Complete Internal MOVEX ERP training (Techn: AS400/RPG/RPGLE) â¢ Completed Internal M3 ERP training (Techn: Java) â¢ Completed Internal Stream serve training. â¢ Completed M3 Enterprise Collaborator (MEC) training.Education Details 
 M.Sc. Computer Science Pune, Maharashtra Pune University
 B.Sc. Computer Science Pune, Maharashtra Pune University
 H.S.C.  Pune, Maharashtra Pune UniversityPython RESTful API developerPython developer - KPIT TechnologiesSkill Details 
Flask- Exprience - Less than 1 year months
Python- Exprience - Less than 1 year months
Restful- Exprience - Less than 1 year months
Rest- Exprience - Less than 1 year months
Numpy- Exprience - Less than 1 year months
AS/400- Exprience - 90 monthsCompany Details 
company - KPIT Technologies
description - since 6th July 2011 to till date:

â¢ Currently working as a Python API developer having 2 years of experience in Python- MongoDB/MySQL development/support project.
â¢ Worked as a M3 Java developer and Stream serve developer of Movex/M3 ERP for 1
year.
â¢ Worked as a Senior AS400 and Stream serve developer of Movex/M3 ERP for 4 years.

Technical Expertise:
â¢ Python development:
â¢ Python - MongoDB
â¢ Python - MySql
â¢ Python Cache & Memoization
â¢ Python GIT
â¢ Python PWS (Pivotal Web Service - Cloud Foundry)
â¢ German A1 Level

â¢ M3/Movex ERP development:
â¢ M3 Java of Movex/M3 ERP
â¢ AS400 development of Movex/M3 ERP
â¢ Stream Server development of Movex/M3 ERP
â¢ Movex/M3 Standards, RPG/400, CL/400, ILE RPG, ILE CL, DB2/400, QUERY400 and SQL/400, Subfiles, Printer Files, PF ,LF
â¢ Movex/M3 Flows, Programs & database structure, MI Programs."
Python Developer,"Education Details 
June 2013 to June 2016 Diploma Computer science Pune, Maharashtra Aissms
June 2016 BE pursuing Computer science Pune, Maharashtra Anantrao pawar college of Engineering & Research centrePython DeveloperSkill Details 
Company Details 
company - Cybage Software Pvt. Ltd
description - I want to work in organisation as a python developer to utilize my knowledge & To gain more knowledge with our organisation."
Python Developer,"TECHNICAL PROFICIENCIES Platform: Ubuntu/Fedora/Cent OS/Windows Database: MySQL Languages: Python, Tensorflow, Numpy, C, C++ Education Details 
January 2016 ME Computer Engineering Pune, Maharashtra Savitribai Phule Pune University
January 2014 B.E Computer Engineering Pune, Maharashtra Savitribai Phule Pune University
January 2010    RYK Science College, Maharashtra state board
January 2008    Maharashtra state boardPython developerPython DeveloperSkill Details 
C++- Exprience - 6 months
MYSQL- Exprience - 6 months
PYTHON- Exprience - 6 monthsCompany Details 
company - Fresher
description - Python programming"
Python Developer,"Technical Skills: Languages Python Python Framework Django, DRF Databases MySQL, Oracle, Sqlite, MongoDB Web Technologies CSS, HTML, RESTful Web Services REST Methodologies Agile, Scrum Version Control Github Project Managent Tool Jira Operating Systems Window, Unix Education Details 
 BE   Dr.BAMU,AurangabadPython DeveloperPython Developer - Arsys Inovics pvt ltdSkill Details 
CSS- Exprience - 31 months
DJANGO- Exprience - 31 months
HTML- Exprience - 31 months
MYSQL- Exprience - 31 months
PYTHON- Exprience - 31 months
web services- Exprience - Less than 1 year months
Logger- Exprience - Less than 1 year months
Mongodb- Exprience - Less than 1 year months
json- Exprience - Less than 1 year months
Unix- Exprience - Less than 1 year months
Rest- Exprience - Less than 1 year months
Sqlit3- Exprience - Less than 1 year monthsCompany Details 
company - Arsys inovics pvt ltd
description - Project - F-MAS (Frequency Monitoring and Analysis Systems - (F-MAS))

F-MAS is a project for managing network inventory, network communication, fault management & network traffic analysis. The telecommunications service providers, are used to support a range of telecommunication services. The Operations Support Systems (OSS) collectively provides support for various elements used in Public Switched Telephone Networks, for example processing an order may require information on the services the customer already has, the network they are using, and currently available resources.

Responsibilities:
â¢ Participated in entire lifecycle of the projects including Design, Development, and Deployment, Testing and Implementation and support.
â¢ Developed views and templates with Python and Django's view controller and templating language to created user-friendly website interface.
â¢ Implemented navigation rules for the application and page outcomes, written controllers using annotations.
â¢ Created this project using Django, Django REST API, MYSQL, PyMYSQL, Python, HTML5, CSS3.
â¢ Created CRUD methods (get, post, put, delete) to make requests to the API server and tested Restful API
using Postman.
â¢ Created Unit test cases for unit testing.
â¢ Worked with JSON based REST Web services
â¢ Wrote Python routines to log into the websites and fetch data for selected options.
â¢ Used Python modules such as requests, urllib for web crawling.
â¢ Added the navigations and paginations and filtering columns and adding and removing the desired columns for view.
â¢ Created a Git repository and added the project to GitHub.
â¢ Utilized Agile process and JIRA issue management to track sprint cycles.
â¢ Worked in an agile development environment.

Environment: Python, Django, MySQL, HTML, CSS, SQLAlchemy, JSON, agile, Web Services (REST), Urllib.
company - Arsys
description - 1. Working as back end as well as front end developer
2. working on rest and restfull api's.
3. Design and develop a project in Agile scrum.
4. Git hub for code deployment
5. Working on MVT ."
Python Developer,"Training attended: 1. Successfully completed ESD program conducted by Zensar Technologies, Pune in 2017. 2. Successfully completed Employability training conducted by Barclays, Global Talent Track, and NASSCOM foundation in 2015. Achievements: 1. Treasurer in IEEE student branch at JSCOE, Pune for 2017-18. 2. Worked as team leader in collegeâs various technical and cultural events from 2016 - 2017. 3. Project idea got selected for final prototyping round in KPIT-Sparkle 2018, Pune. 4. Participated in Avishkar 2017 conducted by Savitribai Phule Pune University. 5. Project idea submitted in Accenture Innovation 2018, Pune. 6. Brought sponsorship of Rs. 15,000 from Platinum Auto (formerly Royal Enfield) in 2017, Pune. 7. Secured 1 st Rank for college level competition of Poster presentation on Smart ambulance in 2017, Pune. 8. Organized IEEE workshop on âExcellence in English and Public Speakingâ in 2017, Pune Workshops attended: 1. Successfully completed 4 daysâ workshop on âMedical IOTâ conducted by IEEE standardâs association at VIP in 2017, Pune. 2. Successfully completed 2 daysâ workshop on âIntroduction to Arduinoâ at SCOE in 2016, Pune. 3. Successfully completed 3 daysâ workshop on âRobotics for Juniorsâ conducted by Computer Society of India at SKNCOE in 2016, Pune. 4. Participated in various inter-college technical competitions at SCOE, PICT, and AISSMS, Pune. Education Details 
June 2018 Bachelor of Engineering Computer Pune, Maharashtra Savitribai Phule Pune University
June 2014 HSC   Maharashtra State Board
June 2012 SSC   Maharashtra State BoardPython DeveloperPython Developer - Atos SyntelSkill Details 
PYTHON- Exprience - 15 months
DATABASE- Exprience - 7 months
MYSQL- Exprience - 7 months
DJANGO- Exprience - 6 months
HTML5- Exprience - 6 months
REST API- Exprience - 6 monthsCompany Details 
company - Atos Syntel
description - Working as a developer in the field of computer vision for a US based client in banking domain.
1. Design and development of computer vision based algorithms for image preprocessing using OpenCV, PIL, and Numpy.
2. Unit testing and debugging the code and maintaining the versions using Git."
Python Developer,"â¢ Operating Systems: Windows â¢ Others: MS Excel, MS Office, MS Power Point Key Projects Handled Project Title: fruit sorting and disease detection Client: Kranti Dynamics Team Size: 5 Education Details 
January 2014 B.E. Electronics Mumbai, Maharashtra University of MumbaiPython Developer/analystpython developer and data analystSkill Details 
python scripting,programming,developing- Exprience - 12 months
frontend  ,html- Exprience - 12 months
python liabrary, numpy,pandas,matplolib,requests,beautiful soap- Exprience - 12 months
mysql- Exprience - 12 months
django- Exprience - 12 months
web scrapping- Exprience - Less than 1 year monthsCompany Details 
company - Ace The Power of 5
description - The Accountabilities:

â Understanding the functional requirements of the application given by the client.

â Participated in walkthroughs of business requirements, functional requirements and technical design to ensure their testability.

â Responsible for Software Configuration Management of project deliverables.

Technical skill set:

â¢ Languages: C, C ++, Java, python,python liabray,mysql,django,html

â¢ Scripting: Python,
â¢ GUI development: Tk, Java
company - kranti dyanamics
description - programming,scripting,developer,web scrapping"
DevOps Engineer,"Skills VISA B1-VISA (USA) Onsite Visits to Sweden & US (Seattle) Education Details 
January 2013 Post Graduate Diploma Information Technology Pune, Maharashtra Symbiosis Institute
January 2007 Bachelor of Engineering Electronics and Telecommunications Pune, Maharashtra Pune UniversityCloud Operations Architect (DevOps)Cloud Operations Architect (DevOps) - DevOpsSkill Details 
Cloud Computing- Exprience - 48 months
Shell Scripting- Exprience - 96 months
Python- Exprience - 6 months
Automation- Exprience - 72 months
Solution Architect- Exprience - Less than 1 year months
Azure- Exprience - Less than 1 year months
AWS- Exprience - Less than 1 year monthsCompany Details 
company - DevOps
description - Type: DevOps Engineer.
Platform: AWS Cloud, Azure Cloud.
Services: AWS EC2, RDS, CloudFormation Template, Lambda, Dynamo DB,		Cloud Watch, Auto-scaling, Elastic Bean stalk, Appdynamics.

Here I manage Tibco Spotfire enterprise & Cloud Product support. Being the only Ops member in India I got a chance to recruit & build entire team of 15 members. I also worked on 4 different Projects / products simultaneously and added the hired members into these products.

My responsibilities in this project include:
1. Managing Tibco Spotfire Enterprise & Cloud environment.
2. Helping India QA team with the pre-production environment for testing.
3. Coordinating production deployment & hot-fixes.
4. Leading the team and handling 4-projects.
5. Arranging workshops over new AWS services for entire Team.

Shell scripts for Automation:

â¢ Wrote shell script to extract the AWS running instances and shut it down.
â¢ Wrote shell script to extract free EIP and release those.
â¢ Wrote Lambda function to trigger important scripts.
company - Synechron Technologies Limited
description - Type: Administration, automation & Monitoring.
Platform: Linux/Unix, Linux-Xen Servers,
Software: Puppet, Redhat Satellite server, my-sql database shell scripting.

My responsibilities in this project included:
1. Managing linux media servers farms and provide application support.
2. Patching Linux physical and xen boxes.
3. Creating Satellite channels on Satellite server.
4. Automation via shell scripting and mysql DB support.
5. Troubleshooting the customized applications.
company - Tsys Limited
description - Type: Development, automation & Monitoring.
Platform: Linux/Unix, windows
Software: mysql database, shell scripting.

Here my responsibility was to provide access to the users on RHDS, Cvs and dot project servers.
It also included providing access to users on dev/prod VDI servers. Handling mysql database and shell scripting to automate the tasks.

My responsibilities in this project included:
1. Requirements and Analysis: Understanding the requirements of the project and planning the environment and the access required.
2. Implementing mysql replication over linux servers.
3. Fine tuning the existing applications.
4. Testing & debugging the scripts for any errors.
company - Patni Computer Systems Limited
description - Project: CPI
Type: Development, automation & Monitoring.
Platform: P8 Filenet, Linux/Unix, IBM AIX
Software: Sound knowledge of HTML, shell scripting.
Client: Genworth Financial
Duration: 2 yrs 2 months.
Role: Senior System Engineer

CPI is a Maintenance Project that caters to maintenance/enhancements of different applications, which are a part of Genworth Imaging Solutions. It has different applications that process the scanned Insurance documents sent from different Providers and stores the information in Oracle Database and images in P8 Filenet. It has multiple applications to generate reports to be sent to providers.

Administration Support:
â¢ Providing support to the L1 engineers.
â¢ Monitoring P8 Filenet application.
â¢ Handling Tickets raised by the users.
â¢ Administration of the 10 Linux Proxy servers and mysql servers.
â¢ Implementing mysql replication.
â¢ Checking the logs of the sites visited by the users and the data downloaded.

Mysql / Oracle / SQL Support:
â¢ Preparing SQL queries for the client users.
â¢ Handling Oracle database.
â¢ Testing insert, select, update queries over servers and deploying over production.
â¢ Handling the bugs raised by the users.
â¢ Implementing mysql replication over linux servers.
â¢ Taking database backup through mysql dump.

Application Support:
â¢ To make sure that the customer applications like eProcess, Trexo are working fine.
â¢ To make sure all the customized filenet instances are working fine.
â¢ Writing scripts to automate few of the applications.

Shell scripts for Automation:
â¢ Wrote shell script to delete the logs older than five days which was successfully deployed over Production servers."
DevOps Engineer,"Software Proficiency: â¢ Languages: Basics of C, SQL, PL/SQL,JAVA,JAVAEE,Javascript,HTML,CSS,jquery,mysql,Spring ,Hibernate. â¢ Software Tools: Xillinx, Modelsim, Matlab, Multisim. â¢ Operating Systems: Windows XP, Vista, 07, 08, Ubuntu. Project Profile: B.E. Project FPGA Implementation of Team Size: 4. Role: Programmer. AES Algorithm AES is Advanced Encryption Standard which is used in cryptography by which we can protect our data. It encrypted by a Secret Key. T.E. project Sorting Robot. Team Size: 3. Role: Mechanism designer. The TCS 230 sensor sorts the RGB color balls according to their color. Diploma Project RFID Based Student Team Size: 4. Role: Interface. Attendance System Using GSM. In this student show RFID card of his own and then message send via GSM to their parent that his ward is present.Education Details 
May 2016 B.E. Savitribai Phule Pune, Maharashtra Pune University
March 2010 S.S.C   Maharashtra BoardDevOps EngineerSkill Details 
C- Exprience - 6 months
C++- Exprience - 6 months
Sql- Exprience - 6 months
Pl/Sql- Exprience - 6 months
Core Java- Exprience - 6 months
Javascript- Exprience - Less than 1 year months
HTML- Exprience - Less than 1 year months
CSS- Exprience - Less than 1 year months
Jquery- Exprience - Less than 1 year months
JavaEE- Exprience - Less than 1 year months
Mysql- Exprience - Less than 1 year months
Python- Exprience - 6 monthsCompany Details 
company - Parkar Consulting and Labs
description - I'm working on the DevOps team in Parkar Consulting and Labs. I have hands on the AWS as well as Python"
DevOps Engineer,"CORE COMPETENCIES ~ Ant ~ Maven ~ GIT ~ Bitbucket ~ Jenkins ~ Linux ~ Ansible ~ Shell Scripting ~Requirement Gathering ~Continuous Integration and Continuous Deployment ~ Software Development Life Cycle ~ Software Testing Life Cycle ~ Documentation & Reporting ~ Test Reports IT SKILLS â¢ Primary Skills: Dev-ops methodologies â¢ Programming Languages: C, Core Java â¢ Version Controls: GIT, Bitbucket â¢ Build Tools: ANT, Maven â¢ CI/CD Tools: Jenkins â¢ Configuration management: Ansible â¢ Scripting: Shell Script â¢ Application Servers: Apache Tomcat serverEducation Details 
June 2015 to June 2017 Masters of science information technology Hyderabad, Telangana JNTUDevops EngineerDevops Engineer - Nendrasys Technologies Pvt LtdSkill Details 
DEPLOYMENT- Exprience - 27 months
Git- Exprience - 27 months
DOCUMENTATION- Exprience - 26 months
CHANGE MANAGEMENT- Exprience - 10 months
CONFIGURATION MANAGEMENT- Exprience - 10 monthsCompany Details 
company - Nendrasys Technologies Pvt Ltd
description - Date

Project Description:
The Scope of the project is to design & develop e-commerce product features for sloan project. It's a basically B2B project where customer can buy all fixers, sinks related product. It focuses on the company, the stakeholders and applications, which allow for online sales, distribution and marketing of product.

Responsibilities:
o Detailed technical Knowledge and hands-on experience on DevOps, Automation, Build Engineering and Configura -
tion Management.
o Creating fully automated CI build and deployment infrastructure and processes for multiple projects.
o Developing scripts for build, deployment, maintenance and related tasks using Jenkins.
o Installing, configuring and maintaining Continuous Integration, Automation and Configuration Management tools.
o Developing Ant, Maven and Shell scripts to automatically compile, package, deploy WAR, EAR and JAR files of mul- tiple applications to various platforms.
o Creating Repositories, branches, managing the permissions of users and maintaining GIT, Bitbucket
o Deploying and maintaining the code and application respectively and configuring the components which increase the re-usability.
o Working closely with Architecture, Development, Test, Security and IT Services teams.
o Supporting Business Analysts in getting used to the newly created jobs and Release activities.
o Communicating daily with On-Site Team
o Analyzing the requirements and identifying gaps & tracking logs, issues.

PROJECT 2

Nendrasys Technologies Pvt Ltd.,
Project Name: Bangkok Bank Limited (BBL) Thaitrade (Commercial, E-commerce & Payment portal)
company - Nendrasys Technologies Pvt Ltd
description - Role: Devops Engineer
â¢ Devops Engineer for a team that involved different development teams and multiple simultaneous software re- leases
â¢ Participated in weekly release meetings with different teams to identify and mitigate potential risks associated with the releases.
â¢ Responsible for Creating Repositories, branches, managing the permissions of users and maintaining GIT.
â¢ Creating fully automated CI build and deployment infrastructure and processes for multiple projects.
â¢ Analyze and resolve conflicts related to merging of source code for GIT
â¢ Responsible for creating Ant and Maven scripts for build automation.
â¢ Implemented the setup for Master slave architecture to improve the Performance of Jenkins.
â¢ Handled end-to-end deployments and code propagation's across different environments, DEV to PROD.
â¢ Handled the tasks of developing and maintaining the Change Tasks.
â¢ Closely working with Developers, QA, Performance, UAT testers, IT Team.
â¢ Created and maintained documentation of build and release processes and application configuration
â¢ Coordinated with developers, Business Analyst and Mangers to make sure that code is deployed in the Production
environment.

PROJECT 1
Nendrasys Technologies Pvt Ltd.,
Project Name: Sloan Global Holdings (Sloan) (E-commerce)
company - Nendrasys Technologies Pvt Ltd
description - Project Description:
BBL is development of an e-commerce payment portal System. As such BBL require a system which can support multiple
merchants B2B/B2C e-commerce portal with various payment options via both online and offline. To achieve this BBL had
made an agreement with Thai Trade, Thai Trade is one of the leading e-commerce website recognized by Thai
government. In present Thai trade has a good base of users and sellers in several categories .The scope of the project is to design and develop e-commerce product features for BBLWTP project. It focuses on the company, the stakeholders and
applications, which allow for online sales, distribution and marketing of electronics.

Responsibilities:
o Plan and track activities involved for build and deployment.
o Resolving build issues
o Developing scripts for build, deployment, maintenance and related tasks using Jenkins.
o Collaboration with Development, QA, Product managements on build plans & schedules
o Send daily and weekly reports to the team.
o Involved in creating repositories, branches, merging activities, backup and restore activities in subversion
servers.
o Maintenance of Maven, shell scripts for safe builds and deploys."
DevOps Engineer,"Technical Skills Key Skills MS Technology .Net, SharePoint, MS SQL and Oracle PL/SQL Project Management & Execution Cloud Computing using Windows Azure and Amazon AWS/ EC2 Cloud XML/XAML/XSLT, HTML5, DHTML, CSS3, JavaScript/JQuery/JSON, AngularJS, Web API, OData Service, VBScript, Node JS, Handling Dynamic QA Activities, Project Delivery Frameworks UI Design and Mobile Development, JAVA, JSP, SWING, J2EE Service Oriented Architecture (SOA), Web Service/WCF Service/Web API, Requirement Gathering Design Pattern like MVC, MVP, Abstract Factory OOAD with UML Implementation .NET SAP Connector to interact with SAP through ASP.NET Client Management SAP Web Channel and SAP Enterprise Portal Environment Android, iOS, Team Foundation Server 2010/12(TFS), GitHub, IBM Rational Key Account Management DevOps, Team Forge and SharePoint Portal Administration /Development, CollabNet, JIRA. IOT Implementation, Web/Data analytics, Working with the road map of Machine learning and AI. Expertise BI tools like Crystal report, Escalation Management SSRS, Tableau, Micro Strategy, QlikView. IT Network/Infrastructure Monitoring tool Centreon, Cisco Meraki, BizTalk BAM. Team Building & Leadership Liaison & Coordination Soft Skills Reporting & Documentation Leader Technical architect Time Management Thinker Collaborator Planner Information Security management Communicator Quality assurance Career Timeline (Recent 5 Companies) Reliance ADA Annet Technologies Aditya Birla Group as Software as Project Group as Project consultant Lead/Technical Manager Architect 2006-2007 2008-2009 2009-2012 2012-2013 2013-till date Northbound eClerx Software LLC as Service as Programmer Technical Project Analyst ManagerEducation Details 
January 1998 Bachelor of Art Economics Dibrugarh, Assam, IN Dibrugarh University
 MBA Information Technology  Sikkim Manipal University
  Software Engineering and Computer Science Pune, Maharashtra NIITProject ManagerProject Manager - Aditya Birla GroupSkill Details 
MICROSOFT SHAREPOINT- Exprience - 147 months
SHAREPOINT- Exprience - 147 months
SQL- Exprience - 92 months
ASP- Exprience - 79 months
ASP.NET- Exprience - 76 monthsCompany Details 
company - Aditya Birla Group
description - Reporting to Vice President)
company - OPEX., Monitoring multiple DB server, Web server and Cloud base
description - Period: Apr 2013 to till Date
Working with: â¢ Project management processes in ongoing projects. Coordinating with different projects Team (Internal
Development team and Vendor side team) and other QA activities.
â¢ Information security audit. Budgeting CAPEX/OPEX., Monitoring multiple DB server, Web server and Cloud base servers Resource utilization in Networking infrastructure using different Hardware/Networking surveillance tools.
â¢ Tracking different team projects activities and involved with generating Final monthly report with different KPI for organization higher authority.
â¢   Managing all the SharePoint Portal Admin activities and all the Project Functional, Technical and Business documents.
â¢   Involved in Technology Road map group to upgrade new technology like IOT, Virtual reality, Wearable technology, Machine learning/AI, 3D Printing.

At eClerx Software Service Mumbai
company - eClerx Software Service
description - 
company - Dell Site Search/Natural Search DB
description - Environment: ASP.NET 4.0, C#, WCF, SSRS, SQL Server 2008 R2
At Annet Technologies Mumbai
company - NextGen Enterprise
description - ARIA) and 2) Re-manage Portal Integration in Apple iPhone/iPad and Different Android Devices
Domain- Real State, Period: Mar 2011 - Oct 2012
Environment: ASP.NET 4.0, C#, WCF, WWF, JQuery, JSON, JavaScript, HTML 5, CSS3, Windows Azure Cloud, SharePoint
company - Annet Technologies
description - Key Result Areas:
Administering project progress as per scheduled deadlines for various tasks and taking necessary steps for ensuring completion within time and effort parameters
Working towards mapping requirements & providing best solutions for evaluating & defining scope of project and finalizing project requirements
Interfacing with clients for business gathering, conducting system analysis & finalizing technical specifications
Verifying the project documentation (Technical/Functional); using SharePoint Documents Library Environment and performing all the SharePoint administrative activities

Significant Accomplishments:
Rolled out project performance metrics reporting, implemented profitability improvement plans and enhanced operational efficiency
Received the following awards: o Excellent Design and On-time Delivery Award o Design and Delivery Excellence Award

Previous Experience
company - Genentech, Biogen, Astellas Pharma, Polaris
description - Client: Genentech, Biogen, Astellas Pharma, Polaris, New York, USA
Environment: ASP.NET 3.5, C#, N-Hibernet, WCF, WWF, JavaScript, JQuery, BizTalk Server 2009, SQL Server 2008 R2

At Northbound LLC Sunnyvale California
company - iOS
description - SQL Server 2008R2, Android, iOS
company - Logistic Transport System, Ohio
description - USA
Environment: ASP.NET, C# 3.5, JavaScript, NUnit Test, Telerik Controls, SQL Server 2005, T-SQL, MOSS 2007, SSIS,
SSRS, BizTalk2006, Crystal Report, N-Hibernet, Dreamweaver
company - Northbound LLC
description - 
company - Biz Automation CRM
description - California, USA
Environment: ASP.NET 3.5, C#, SQL Server 2005, Android/iOS Development Platform, XML, XSLT, JavaScript, JQuery,
JSON, SharePoint Design, WCF, WWF, SharePoint 2007, Performance Point, SSRS
company - Wachovia, North Carolina USA
description - Environment: ASP.NET, C#, WCF, SQL Server 2005, Web Parts, JavaScript, AJAX

At Reliance ADA Group Mumbai
Title: 1) Complain Management System and 2) Company Information System
company - Domain- Manufacturing and Human Resource
description - Environment: ASP.NET, C#, Oracle 8i, PL/SQL, Crystal Report, SAP Connector, VPN
company - Reliance ADA Group
description - 
company - PF Trust System
description - Environment: VB.NET, XML, Oracle 9i, PL/SQL, Toad, Crystal Report 11

Sure Solution - Prasanna Group of Company IT Pune
company - Prasanna Group
description - 
company - Online Bus Booking Portal
description - Environment: ASP.NET, C#, SQL Server 2005, Web Services, Web Parts, MS Office SharePoint Design, AJAX and Java cript
At Info Dynamic Software System Pvt. Ltd. Pune

Title: Info-Banking / E Banking
Domain- Banking Period: Jan 2005 - Aug 2005
Environment: VB6, Oracle 8i, PL/SQL, Crystal Report, Java JSP/Servlet/Swing.

At PAN Software System Pvt. Ltd. Pune
Title: E-commerce Site for Online Sales and Purchase
company - Info Dynamic Software System Pvt. Ltd
description - 
company - ZD Doll Inc
description - Newcastle, UK
company - PAN Software System Pvt. Ltd
description - 
company - Online Procurement of Materials
description - 
company - Shipping Company
description - Tokyo Based, Japan
Environment: Java, JSP/Servlet, CSS, JavaScript, SQL Server 2000, T-SQL
Title: Studio Automation System
Domain- Multimedia Period: Jan 2004 - May 2004
Environment: VB 6, VC++, MS SQL Server, MS Data Report, Macromedia Flash, Fire works

At Solid State Technologists (I) Pvt. Ltd. Pune
company - National Dairy Development Board
description - Environment: C++, VB6, SQL2000, Lotus Notes, Crystal Report 8, MS Active Report 2.0
company - Solid State Technologists (I) Pvt. Ltd
description - 
company - Shell/Bharat Petroleum
description - Title: Computerized Fuel Density Measurement Automation System
Domain - Manufacturing and Automation Period: Dec 2002 - Mar 2003
Client: Shell/Bharat Petroleum
Environment: C++, VC ++, MATLAB, VB6, SQL Server 2000, Crystal Report

Title: CMCS
Domain - Finance and Accounting Period: June 2002 - Nov 2002
Environment: VB6, SQL Server 2000, Crystal Report

Title: Food Testing System
company - Solid State Technologists (I) Pvt. Ltd
description - Environment: VB .net, SQL Server 2000, Crystal Report

Title: Customer Care System
company - Himalaya Industries
description - Environment: ASP .Net, C#, SQL Server 2000, Crystal Report
company - MAPCO India Ltd and BERCO
description - Environment: VB6, Oracle, Active/ Data Report"
DevOps Engineer,"Core skills â¢ Project / Program Management â¢ Agile / Scrum Management â¢ Risk /Client Management â¢ Process Improvements â¢ Proposals/RFE â¢ Selenium â¢ RALLY / IBM TD Platform â¢ Python â¢ DevOps / DevSecOps â¢ SAFe - Agile Craft â¢ Delivery / Test Management â¢ Project / Program Finance â¢ Profit Maximization â¢ Internal / External Audits â¢ Software Testing / Quality Assurance â¢ Visual Basic 6.0 â¢ MS-SQL Server / Oracle 8/8i â¢ Conformiq â¢ HP QC / QTP â¢ Crystal Report 8.5 Linguistic Skills English Hindi Marathi Tulu Education Details 
January 2013    Harvard
January 2004  Software Engineering  Aptech
January 2001 Bachelor of Commerce Commerce Mumbai, Maharashtra Mumbai UniversityProject ManagerProject Manager - AT&TSkill Details 
TESTING- Exprience - 63 months
ORACLE- Exprience - 6 months
SQL- Exprience - 6 months
AUDITS- Exprience - 6 months
CLIENT MANAGEMENT- Exprience - 6 monthsCompany Details 
company - AT&T
description - Thunderbird program is a One-Stop-Test-Shop catering to AT&T's Enterprise-wide business critical applications. As part of this Fixed Price engagement various types of testing like System, Regression, Sanity and UAT support gets executed through Agile Methodologies on applications under Billing and Sales domain. The project has various critical applications as listed below with a team size of 80+ members.
Sales Express: An iPad & Browser based Mobile Sales application used by Sales Representatives & Service/Solution providers to sell Enterprise Products/Solutions
ESIGN: Allows customer to sign and AT&T to countersign digital version of contract document
AMPD: Enterprise billing application through which AT&T offers new and emerging billing products to the Business community
BEST: Business Solution application which performs consolidated billing and reporting for CRU Billing and Reporting Foundation Accounts
BIZCOMP: Comp Engine for business that supports the commission calculations for Enterprise Business and Small-Business sales force via Revenue and Unit based plans
FAST: Provides rate discounts to eligible Schools and Libraries
CXMT: Lets customers manage their Centrex line and feature arrangements of their services without going through the standard service-order procedure.

Key responsibilities
â¢ Manage and lead all aspects of Transition / Pre-Transition comprising multiple vendors
â¢ Transition Drill down sessions, Forward/Reverse Shadow phases and Reverse Presentation
â¢ Transition Documentation & signoff of SMP & System Appreciation Document etc
â¢ Measure & track testing metrics scorecard as part of Vendor Quality Index (VQI) initiative of AT&T
â¢ Measure and track operating metrics like Onsite Subcon Index, Offshoring Index, Leakage Index, Utilization Index and Tail Index
â¢ Manage, Track and Submit Project Monthly Financials & Invoicing
â¢ Ensure 100% compliance to client / organization's quality and security processes, policies and SLAs
â¢ Plan, manage and mitigate Risks
â¢ Achieve Automation targets and SLAs using ConformiQ, Selenium & Oscar Tools
â¢ Participate in proposal preparations to expand revenue and new business developments
â¢ Manage & Track Work Allocation, Resourcing, Issue Resolution and Testing activities for the project
â¢ Ensure Monthly Delivery / TL9K Metrics and CSL-KMs are submitted on time
â¢ PMR, Internal Quality / Security Group, SQA, Quality Gates and external Ernst & Young / TL9K Audit
â¢ Share weekly highlight reports, Project Health Report and regular Dashboard Reporting activities
â¢ Prepare CSAT action plan for the team
â¢ Prepare and implement Business Continuity Plan for the project
â¢ Timely conduct of Business Continuity drills like Call Tree, Table Top and Project Rehearsal
â¢ Comparative analysis, Root Cause Analysis and Defect Prevention exercises for ST, E2E and UAT
â¢ Coordinate with Resource Management, Quality, Learning, Security, PMO and Business Unit
â¢ Sub-Contractors and Rebadged (Ex-AT&T) staff management
â¢ Participate in external technical interview / hiring drives for new engagements
company - Esaya Software India Pvt Ltd
description - "
DevOps Engineer,"Total IT Experience 15 years. Core expertise in Data Base Design, PHP, Python, MySql, JavaScript, HTML 5, Ajax, Jquery, XML, Agile Methodology, DevOps Methodology, Scrum Framework, JIRA Tool, GIT, Bitbucket, Anjular JS 1, Angular JS 2, Core Java, J2EE. Education Details 
April 2004 MCM Computer Management Pune, Maharashtra Pune University
April 1998 B.Sc Maths  Kerala UniversityProject ManagerProject ManagerSkill Details 
Data Base Design, PHP, Python, MySql, JavaScript, HTML, Ajax, XML, Agile Methodology, DevOps Methodology, Scrum Framework, JIRA Tool, GIT, Bitbucket, Jquery,  AngularJs, Amazon MWS , Bootstrap, Node.js, Laravel, Scrum- Exprience - 120 monthsCompany Details 
company - Knoxed Infotech Pvt. Ltd.
description - ï· Client interaction
ï· Maintain work processes
ï· Creates project plans through Agile Model & Methodology
ï· Maintains project objectives
ï· Working with multi-profiled teams of technical and non technical stakeholders
ï· Monitors production and quality to customer/stakeholder/sponsor standards
ï· Conduct office management tasks
ï· Ensuring that the day-to-day operations of the business run smoothly
ï· Introducing key performance indicators (KPI's) and ensuring that these measurements are tracked
and reviewed on a regular basis
ï· Prepare, revise and submit weekly-monthly reports, budgets and other documentation as
necessary
ï· Document current policies and procedures in all departments as well as implement new procedures
for improvement
ï· Maintain smooth running of the office, filling in where needed
ï· Implement quality management and regulatory compliance strategies
ï· Dealing with HR related tasks
ï· Administering payroll
ï· Perform training sessions
ï· Regular meetings with Top Management
Project Undertaken : Internal ERP system For Knoxed Ltd, UK, With PHP, Mysql, Ajax, XML,
Amazon AWS, Raspberry pi Server, Python.
company - Venturus International
description - ï· Client interaction
ï· Creates project plans through Agile Model & Methodology
ï· Manage teamâs workload and workflow
ï· Allocate and track resources as required
ï· Set and monitor deadlines
ï· QC
ï· Maintain tasks and jobs on task management system
ï· Conduct research and development
ï· Create new systems, databases and websites as necessary
Project Undertaken : Internal ERP system For Knoxed Ltd, UK, With PHP, Mysql, Ajax, XML,
Amazon AWS
company - SmashingDay
description - ï· Client interaction
ï· Creates project plans through Agile Model & Methodology
ï· Manage teamâs workload and workflow
ï· Allocate and track resources as required
ï· Set and monitor deadlines
ï· Project documentation
ï· Conduct and maintain appraisals and progress of each employee
ï· Adhere to deadlines as necessary
ï· Maintain work logs
ï· Conduct research and development
ï· Create new systems, databases and websites as necessary
Project Undertaken :
a) www.SmashingDay.com
b) www.viralsocials.com
company - Xento Systems Pvt. Ltd
description - ï· Client interaction
ï· Creates project plans through Agile Model & Methodology
ï· Manage teamâs workload and workflow
ï· Allocate and track resources as required
ï· Set and monitor deadlines
ï· QC
ï· Maintain tasks and jobs on task management system
ï· Conduct research and development
ï· Create new systems, databases and websites as necessary
Project Undertaken :
a) www.familylink.com
b) www.propertysolutions.com
c) www.speedyceus.com
d) www.ceus-nursing.com
company - STP Global Solutions Pvt. Ltd.
description - ï· Client interaction
ï· Creates project plans through Agile Model & Methodology
ï· Manage teamâs workload and workflow
ï· Allocate and track resources as required
ï· Set and monitor deadlines
ï· Conduct and maintain appraisals and progress of each employee
ï· QC
ï· Maintain tasks and jobs on task management system
ï· Conduct research and development
ï· Create new systems, databases and websites as necessary
Project Undertaken :
a) www.stplafricaonline.com
b) www.stplafrica.com
c) www.1stexpert.com
d) www.jcca-net.org
e) www.rimsys.eu
f) www.prayerlister.org
company - Promark Infotech Pvt. Ltd
description - ï· Development & Design
ï· Create new systems, databases and websites as necessary
Project Undertaken :
a) www.justbe.com/
b) www.mtpian.com/
c) www.sababa.nl/booking/
d) www.physicaltherapy-hiu.com
company - 7cees Group
description - ï· Development & Design
ï· Create new systems, databases and websites as necessary
Project Undertaken :
a) Golwin-reality
b) Maza"
DevOps Engineer,"TECHNICAL SKILLS â¢ HP ALM, RTC and JIRA â¢ AS400 (iSeries) with SQL â¢ Test Automation: X- Framework (In hours HSBC Framework), UFT and LISA â¢ Test Data Automation - CATDM â¢ Test Scheduling - Jenkins and Maven â¢ Test Document Versioning - Confluence and SharePoint.Education Details 
January 2002 Bachelor of Engineering Computer Engineering Mumbai, MAHARASHTRA, IN Mumbai University
January 1999 Diploma Computer Technology Mumbai, MAHARASHTRA, IN Maharashtra State Board of Technical EducationProject Manager(Global Test Manager)Project Manager(Global Test Manager)Skill Details 
AS400- Exprience - 25 months
ISERIES- Exprience - 25 months
MICROSOFT SHAREPOINT- Exprience - 6 months
SCHEDULING- Exprience - 6 months
SHAREPOINT- Exprience - 6 monthsCompany Details 
company - CAREER TIMELINE
description - HSBC Software India Pvt. Ltd.Pune, India   Project Manager(Global Test Manager)
company - Disha Technologies
description - 
company - Total Solution (I) Pvt. Ltd
description - Key Deliverables Across The Tenure
As a Global Test Manager/Project Management:
â¢ Acting as Global Test Manager, steering a team of 25+ resources across the globe
â¢ Spearheading the testing teams in India, China, Malaysia and Poland
â¢ Current role involves entire test delivery of large programs under banking domain which includes leading a team comprising of individuals (Client & Vendor) from functional (Regression, Integration) & shared services (Security, Performance, accessibility and Automation) groups
â¢ Defining and devising the ""HSBC Core Banking"" IT Transformation strategies from solution to execution level.
â¢ Experience in latest specialize testing methods - CBIL - API, Conversion, Cloud, Digital UI, Infrastructure, Exploratory and Accessibility Testing.
â¢ Experienced in Planning the OAT and various non-functional like performance and volume testing of Core banking.
â¢ Coordinating with the regional business and testing teams to map their testing approach
â¢ Following with various teams to ensure end to end release is successful and a quality product is delivered to production.
â¢ Steering the Global product testing, regional deployments testing and  extending support for UAT and live proving
â¢ Technical lead to implement the in house ""Test Automation"" framework (X-framework) solution on AS400 (iSeries) system.
â¢ Managing the implementation of Test automation to enable DevOps via tools Cucumber & Selenium (BDD), UFT and TOSCA for function testing
â¢ Leading the CI/CD implementation by executing automated scripts from Jenkins
â¢ Working on Test Data Management by using CA TDM and Internal HSBC Tool)
â¢ Handling tasks pertaining to Resource forecasting, effort estimation and resource on boarding process
â¢ Ensuring seamless communication with vendor partners to arrange for resource onboarding based on the forecasting
â¢ Reviewing the project progress on a daily basis; implementing corrective measures to ensure the project is ontrack
â¢ Overseeing people management tasks comprise monthly 1:1 with leads, reviews
â¢ Providing conflict resolution when required"
Network Security Engineer,"Skill Set â¢ Experience in Implementing, and troubleshooting network security solutions â¢ Planning and Implementation knowledge of multi vendor firewalls (Cisco ASA, Checkpoint (Upto R.80) Juniper/Netscreen, Fortinet, FWSM) â¢ Familiarity with the latest hardware and network security technologies â¢ Excellent analytical and problem solving skills â¢ Skilled in analyzing and monitoring network security solutions using a variety of Monitoring solutions (Zenoss, Solarwinds, Cisco Prime) â¢ Work Experience on multi client data center environments. â¢ Knowledge and Work experience on Firewall IOS Upgrade projects â¢ Configuration of F5 load balancers, SSL certificate updates, I-Rule. F5 upgrades â¢ Configuration of Cisco Routers ( series- 1800, 1900, 2500, 2600, 2800, 3600, Nexus - 5k, 7k) â¢ Configuration of Cisco switches (series - 2960, 3750, catalyst, 4500, 3600) â¢ Working knowledge of Bluecoat Proxy â¢ Knowledge of ITIL process.Education Details 
September 2006 to August 2011 Bachelor of Engineering (BE) Electronics Pune, Maharashtra A.I.S.S.M.S College of Engineering, University of Pune
July 2004 to February 2006 Higher Secondary Certificate Science Pune, Maharashtra Sinhgad College, University Of Pune
June 2003 to March 2004 secondary school certificate (SSC) science Pune, Maharashtra M.E.S Boys High School, Maharashtra, PuneNetwork and Security EngineerNetwork and Security Engineer - CapitaSkill Details 
Network Security- Exprience - 72 months
CHECKPOINT- Exprience - 72 months
CISCO- Exprience - 72 months
CISCO ASA- Exprience - 72 months
Cisco routing and switching- Exprience - 60 months
Loadbalncing F5- Exprience - 60 months
security- Exprience - Less than 1 year months
Cisco- Exprience - Less than 1 year months
VPN- Exprience - Less than 1 year months
LAN- Exprience - Less than 1 year months
Networking- Exprience - Less than 1 year monthsCompany Details 
company - Capita
description - Work on Client Shared Network and Security infra
â¢ Plan, Implement and troubleshoot customer requests.
â¢ Monitor Datacenter infra 24*7
â¢ Work as On call engineer for weekends to provide Out of office support
company - Capgemini India Pvt. Ltd
description - Part of UK India NOC.
â¢ Work on Client dedicated infra.
â¢ Undergo Client infra handover sessions to streamline client on boarding process
â¢  Act as mentor for juniors.
â¢ Attend Weekly CAAB calls to represent critical Changes.
company - Sungard Availability Services
description - Plan, Troubleshoot and Implement Client network requests
â¢ Project Work - Internet BW upgrade/Downgrade, Decommission
â¢ DR test planning and implementation.
â¢ Setting up L3VPN's for customers
company - SunGard Software Solutions
description - Maintain Client Documentation
â¢ Work on datacenter Remediation Project
â¢ DNS record Management

SunGard Availability Services"
Network Security Engineer,"Skill Set â¢â¢Cisco Certified Network Associates (CCNA): - â¢ Basic knowledge of networking such as Ethernet mediums, ethernet communication, types of Ethernet communication devices etc. â¢ IPv4 (Subnetting, Supernetting) â¢ Basic configuration of Routing, Switching, Access lists, Network Address Translation (NAT), Virtual LANs (VLANs) etc â¢â¢Cisco Certified Network Associates (CCNA-Security): - â¢ Basic security goals and need. â¢ Different types of attacks like access attacks, reconnaissance strategies etc. â¢ Basic configuration of firewalls. â¢ Mitigation techniques such as access control lists, private VLANs, VLAN hopping, IP source guard, DHCP snooping, Authentication, Authorization & Accounting (AAA), IP Security (IPsec) etc. â¢â¢Cisco Certified Network Professional (CCNP-Routing, Switching & MPLS): - â¢ Routing-Configuration and concept of EIGRP, OSPF and BGP. â¢ Switching-Virtual LANs (VLANs), spanning tree protocol (STP), queuing etc. â¢ MPLS (Multi-Protocol Label Switching) -Basic idea about working, types and modes of MPLS, penultimate hopping in mpls etc. â¢â¢Cisco Certified Network Professional Security (ASA Module): - â¢ Knowledge of Control Plane Security, Management Plane Security and Data Plane Security. â¢ Knowledge of Network Telemetary (NTP, NetFlow, Logging), NAT fundamental on ASA. â¢ Firewall filtering technologies, ASA modes of deployment, ASA failover, Security context, Reputation based firewall, Identity firewall, Zone based firewall. Projects T.E. Project: Title: -Temperature Controlled Cooling Fan Academic Year: 2016-2017 (sem-5) Description: â¢ This project simply senses the temperature level and turn ON the FAN when temperature is above set limit. This project was developed with an aim to control the temperature of devices and reduce the damages of the devices because of heating. Title:- Automatic Plant Watering System Academic Year: 2016-2017 (sem-6) Description: â¢ This project simply senses the moisture level and turn ON the water pump when moisture is Education Details 
January 2018 B.E Technology Mumbai, Maharashtra Mumbai university
 H.S.C Mahatma Gandhi High  Maharashtra State Board
 S.S.C. Mahatma Gandhi High  Maharashtra State Board
    School & Junior CollegeNetwork Security EnginnerNetwork Security EngineerSkill Details 
Networking- Exprience - Less than 1 year months
Subnetting,Supernetting- Exprience - Less than 1 year months
Access Lists- Exprience - Less than 1 year months
ACLS- Exprience - 6 months
Network Address Translation(NAT)- Exprience - Less than 1 year months
VLAN- Exprience - Less than 1 year months
AAA- Exprience - Less than 1 year months
IPSec- Exprience - Less than 1 year months
ACCESS CONTROL LISTS- Exprience - 6 months
DHCP Snooping- Exprience - Less than 1 year months
Private Vlan- Exprience - Less than 1 year months
VLAN Hopping- Exprience - Less than 1 year months
VPN- Exprience - Less than 1 year months
Routing protocols-RIP,EIGRP,OSPF,BGP- Exprience - Less than 1 year months
DM-VPN- Exprience - Less than 1 year months
Switching- Exprience - Less than 1 year months
Control Plane Security- Exprience - Less than 1 year months
Data Plane Security- Exprience - Less than 1 year months
Management Plane Security- Exprience - Less than 1 year months
Network Telemetary- Exprience - Less than 1 year months
Firewall- Exprience - Less than 1 year months
Firewall Filtering Technologies- Exprience - Less than 1 year months
ASA Failovers- Exprience - Less than 1 year months
Zone Based Firewall- Exprience - Less than 1 year months
Reputation Based Firewall- Exprience - Less than 1 year months
Security Context- Exprience - Less than 1 year months
Security- Exprience - Less than 1 year monthsCompany Details 
company - Stormfur Technologies LLP
description - Network Security Engineer
Stromfur Technologies LLP,
August 2018-Present
â¢ Troubleshoot firewall related incidents for customers.
â¢ Solve technical problems relating to installing security solutions.
â¢ Analyze security requirements of network system.
â¢ Reviewed configuration and maintained Cisco ASA Firewalls, routers, and switches.
â¢ Reviewed and modified access control lists (ACLs) on network switching and routing equipment as needed to maintain security standards.
â¢ Participated in core network team to upgrade corporate network.
â¢ Maintained network security technologies and services.
â¢ Interacted with clients and analyzed operational requirements.
â¢ Install and configured of IPSEC and SSL VPN.
â¢ Reviewed and configured VLAN, Routing Protocols (RIP, OSPF, EIGRP), NAT.
â¢ Support for client in various networking issues."
Network Security Engineer,"Communication Skills My writing skills in English is very good and I am certainly a clear speaker. Being comfortable speaking in front of an audience, I can easily facilitate meetings and brainstorming sessions Interpersonal Skills As a friendly, polite person, with positive attitude I work well with others and deal with difficult situations in a careful and considerate manner. Learning Skills I have a high capacity for learning, pick up new skills and ideas quickly, and generally thrive on challenges. Enthusiasm and creativity give me an edge on difficult projects. Problem Solving Skills My analytical skills help me troubleshoot problems and uncover root causes. I am creative and tenacious.Education Details 
July 2012 to April 2015 Bachelor of Science INFORMATION TECHNOLOGY Mumbai, Maharashtra Akbar Peerbhoy College of Comm & Eco. (University of Mumbai)
June 2008 to April 2010   Jaunpur, Uttar Pradesh Janta Inter College Jaunpur
May 2006 to April 2008   Jaunpur, Uttar Pradesh Inter College JaunpurNetwork Security EngineerSkill Details 
Network Security, Network Administration, Firewall and VPN Configuration, Routing and Switching.- Exprience - 24 monthsCompany Details 
company - Karvy Innotech LTD.
description - ÃÂ Managing network of Mahindra & Mahindra Financial services limited, both internal and at PAN India level.
ÃÂ Installation, setup / configuration, troubleshooting, Tuning, diagnostics and maintenance of IT security related Equipment.
ÃÂ Maintaining all network devices such as Routers, Switches, Firewall, Wi-Fi Devices.
ÃÂ Troubleshoot performance and availability issues on security devices such as firewalls, UTM, Content Filtering solutions and Handling daily calls related to network and network security.
ÃÂ Manage and administer the VPN connectivity with organization's clients.
ÃÂ Implement Policy Changes, Configure URL filtering & security policies onÂ UTM appliances. 
ÃÂ Monitor alerts received from the monitoring systems and take appropriate action based on the defined processes.
ÃÂ Follow the SLAâs and procedures already defined for security device management.
ÃÂ Co-ordinating with ISPs like TATA, Airtel, Reliance, Vodafone, Sify, Tikona & HCL Comnet for all WAN related issues.
ÃÂ Configuration and Troubleshooting of Site to Site (IPSEC) VPN between HO and Remote Branches.
ÃÂ Maintaining inventory on monthly basis of all network devices.
ÃÂ LAN Connectivity issues, Crimping, Rack Mounting. 
Reporting to CISO of Mahindra Finance for all network related queries and solutions"
Network Security Engineer,"Technical Expertise: â¢ Cisco ASA, Checkpoint firewall (GAIA, Splat), Palo Alto firewalls â¢ Firewall management tools: Cisco security management (CSM), Checkpoint Smart center, Palo Alto Panorama â¢ F5 load balancer (LTM) â¢ Networking Hands on Cisco Router and Cisco Switches, â¢ Security Management & Event Management: RSAEnvision 4.1, â¢ Ticketing tools - BMC Remedy, Service-now Other Technologies: Software languages: Python, VB ScriptingEducation Details 
January 2006 to January 2010 Bachelor of Engineering Information Technology Mumbai, Maharashtra Mumbai UniversitySenior Network security AnalystSenior Network security Analyst - AccentureSkill Details 
FIREWALLS- Exprience - 97 months
CISCO- Exprience - 89 months
SECURITY- Exprience - 72 months
FIREWALL- Exprience - 45 months
CHECKPOINT- Exprience - 44 monthsCompany Details 
company - Accenture
description - Install, configure, upgrades and troubleshoot Cisco ASA firewalls ( 5500-X)  which includes Single and multi-context firewalls
â¢ F5 LTM  configuration (VIP, pools), SSL offloading, certificate renewals, Code upgrade and troubleshoot issues
â¢ Pulse secure (Juniper)  management includes creating Roles/ Realm, polices   for remote access, reverse proxy and Code upgrade
â¢ PAC file configuration
â¢ Preparing design documents (HLD, LLD), IP Schema and Configuration of all network devices as per customer requirements in data center environment
â¢ Incident Management, Change management, Problem Management ( ITIL process)
â¢ Key projects- Worked on resiliency project like firewall policy lockdown. Software code upgrade on all firewalls and load balancers. End to end co-ordination for firewall deployment
â¢ Tools: Cisco security manager 4.x
company - Zensar Technologies
description - Install, Upgrades Checkpoint firewalls GAIA R75, R77 and policy deployment
â¢ Troubleshoot checkpoint firewalls, Palo alto firewall and Cisco ASA's for multiple sites belongs to US, Canada and EMEA region.
â¢ Configure, upgrades, troubleshoot Cisco Routers ( Cisco 29xx, 28xx, 19xx) and Cisco switches (2960, 3560) and Cisco Meraki AP in Meraki Cloud
â¢ F5 LTM configuration and troubleshooting the operational issues
company - Infosys Technologies
description - Responsibilities:
â¢ Install, configure, upgrade and troubleshoot checkpoint firewalls, Cisco Routers, Switches
â¢ Configure, monitor and troubleshoot issues within organization LAN, WAN and customer
Connectivity networks.
â¢ Interface with vendors and service providers ensuring that hardware or software problems were dealt with efficiently and effectively, with minimal downtime
company - HCL-Comnet
description - Responsibilities:
â¢ Event moitoring and administration of RSA enVision 4.1 SIEM.
â¢ Proficient in researching traffic patterns to identify false-positives and/or malicious traffic within IDS, IPS, scanners (Qualis) and firewalls.
â¢ Experience in writing correlation rules, parsers & developing UDS for unsupported device logs.
â¢ analyze a large volume of security events while maintaining a high quality of analysis
â¢ Change Configuration Management exposure
company - Wipro Technologies
description - Administrator, Manage and troubleshoot multi-vendor Network Infrastructure consisting of Cisco, Juniper Platforms ( Cisco Router 3845, Cisco stack-wise switches 3750E and 2960 access switch, Cisco 4500 chassis, Cisco 5550/20 ASA firewalls, Juniper SA 6500 SSL VPN)
â¢ Provisioning and troubleshooting access issues related to RSA and Entrust (2FA) User Accounts.
â¢ Taking all network and security devices configuration back up on weekly and monthly basis."
Network Security Engineer,"Operating Systems: Windows, Linux, Ubuntu Network Technologies : Cisco Routing and Switching, InterVLAN Routing, Dynamic Protocols i.e. RIPv2, RIPng, OSPF, EIGRP. Static Routing, ACL, VTP, VLAN, EhterChannel, HSRP, STP, IPv6, Lan Troubleshooting, Structured Network Cabling, Cisco Firewall and Fortinet Firewall, RHEL 6 networks. Networking Devices : Cisco Routers: 1800s, 1900s, 2600s, 2900s, 3600s, 3800s, 7200s etc. : Cisco Switches: Cisco Catalyst 2900s, 3700s, 4850s etc. : HP ProLiant Servers, Dell Server, Lenovo Servers etc. : Fortinet Firewall, Modem etc. Server Technologies : AD, RODC, FTP, Print Server, SCCM, WDS, DHCP, Group Policies, DHCP Server, DNS Server, RIS Server, User policies, computer policies etc. Backup Technologies: Server 2016 backup tools, Symantec Backup EXEC 12D etc. Virtualisation: VMWare ESXi, VMware Workstation, Oracle VirtualBox, GNS3 Network Simulator etc. Education Details 
 MSC   AISECT university in distance education
 B. Com   Jiwaji UniversitySr. Network EngineerSr. Network Engineer - Cloudatix Network Pvt. LtdSkill Details 
CISCO- Exprience - 43 months
DHCP- Exprience - 43 months
DNS- Exprience - 43 months
FTP- Exprience - 43 months
LAN- Exprience - 43 monthsCompany Details 
company - Cloudatix Network Pvt. Ltd
description - 18.

Project Routers, Switches and Server Configuration with Group Policies
Client Railwire/Railtel Corporation of India Ltd., Mumbai
Type of Project Configuration of WiFi AP of Railway Station in Maharashtra State
Role Sr. Network Engineer
Roles & responsibilities
â¢ Earthing, trenching for equipment.
â¢ Making plans for preparing station to WiFi environment including cabling, installation, commissioning, handing over etc.
â¢ Implementing and maintaining backup schedules.
â¢ Upgrading and backups of Cisco router configuration files.
â¢ Installation, Configuration and Administration of Windows Servers 2008 R2, 2012 R2, 2016 Active Directory, FTP, DNS, DHCP, TFTP.
â¢ Troubleshooting LAN & WAN infrastructure.
â¢ Settings of the networking devices (Cisco Router, switches) co-coordinating with the system/network administrator during implementation.
â¢ Regular Health Checking of File Servers & Application Servers for Security violation, Disk Spaces etc.
â¢ Maintaining & Management, windows 7 & 10 based network.
â¢ Installing & Configuring Network Printers.
â¢ Assigning proper rights on shared drives.
â¢ User & Groups management and assigning rights according to organization requirements.
company - Asha MMPC
description - Project Routers, Switches and Server Configuration with Group Policies
Client Asha MMPC, Bali, Pali, Rajasthan
Type of Project Configuration DPMCU, Milknet Server, EMS Server, QMS Server etc.
Role IT Executive
Roles & responsibilities
â¢ Implementing and maintaining backup schedules.
â¢ Upgrading and backups of Cisco router configuration files.
â¢ Installation, Configuration and Administration of Windows Servers 2008 R2, 2012 R2, 2016 Active Directory, FTP, DNS, DHCP, TFTP.
â¢ Troubleshooting LAN & WAN infrastructure.
â¢ Settings of the networking devices (Cisco Router, switches) co-coordinating with the system/network administrator during implementation.
â¢ Regular Health Checking of File Servers & Application Servers for Security violation, Disk Spaces etc.
â¢ Maintaining & Management, windows 7 & 10 based network.
â¢ Installing & Configuring Network Printers.
â¢ Assigning proper rights on shared drives.
â¢ User & Groups management and assigning rights according to organization requirements.
company - Interface Techno System
description - Project           Routers, Switches and Server Configuration with Group Policies
Client            The Scindia School, Fort, Gwalior (MP)
Type of Project   Configuration and Implementation
Role              Network Engineer
Roles & responsibilities
â¢ Implementing and maintaining backup schedules.
â¢ Upgrading and backups of Cisco router configuration files.
â¢ Installation, Configuration and Administration of Windows Servers 2000/2003, 2008 R2, 2012 R2, Active Directory, FTP, DNS, DHCP, TFTP, Linux OS.
â¢ Working on troubleshooting of complex LAN infrastructure.
â¢ Settings of the networking devices (Cisco Router, switches) co-coordinating with the system/Network administrator during implementation.
â¢ Regular Health Checking of File Servers & Application Servers for Security violation, Disk Spaces etc.
â¢ Maintaining & Management, windows 7 professional based network.
â¢ Installing & Configuring Network Printers.
â¢ Installing Server side Software's & Utilities.
â¢ Assigning proper rights on shared drives.
â¢ Managing user account
company - Interface Techno System
description - Project           Network Implementation and Configuration
Client            M/s Teva API India Pvt Ltd, Bhind (MP)
Type of Project   Implementation and Configuration
Role              Network Support Engineer
Roles & responsibilities
â¢ Implementing and maintaining backup schedules.
â¢ Upgrading and backups of Cisco router configuration files.
â¢ Installation, Configuration and Administration of Windows Servers 2000/2003, 2008 R2, 2012 R2, Active Directory, FTP, DNS, DHCP, TFTP, Linux OS.
â¢ Working on troubleshooting of complex LAN infrastructure.
â¢ Settings of the networking devices (Cisco Router, switches) co-coordinating with the system/Network administrator during implementation.
â¢ Configuration, Maintenance, check Point Endpoint Security MI Management Console for Laptop Users
â¢ Regular Health Checking of File Servers & Application Servers for Security violation, Disk Spaces etc.
â¢ Logging & Monitoring Calls using CA Unicenter Service Desk Manager.
â¢ Maintaining & Management, windows XP professional based network.
â¢ Installation, Configuration & troubleshooting of Microsoft Outlook.
â¢ Installing & Configuring Network Printers.
â¢ Installing Server side Software's & Utilities.
â¢ Assigning proper rights on shared drives.
â¢ Managing user account
â¢ Issue E Token for laptop user
â¢ Managing more than 350 Desktops and Laptops
â¢ Taking differential and full backups of the server through Symantec Backup Exec 12D
â¢ Restoring the Files, which got corrupted or deleted accidentally.
â¢ Handling escalations of desktop calls from remote locations.
â¢ Monthly/Weekly Report Generations for the servers.
â¢ Preparing MIS, vendor & inventory management reports.
company - Nava Bharat Press (P) Ltd
description - Role   System Engineer
Roles & responsibilities
â¢ Server Management
â¢ Backup management
â¢ Helpdesk, SLA monitoring and incident management
â¢ Hosting client meeting at scheduled interval
â¢ System Management and Troubleshooting
â¢ Hardware Troubleshooting

Additional Qualification & Certifications
â¢ Manual and Computerized accounting with Tally ERP9. Tally certification from Tally Academy."
PMO,"CORE COMPETENCIES â¢ Maintain processes to ensure project management documentation, reports and plans are relevant, accurate and complete â¢ Report automation, Dashboard preparation and sharing feedbacks basis on performance of Project Manager â¢ Forecasting data regarding future risks, Project changes and updating the delivery team on timely basis â¢ Good understanding of project management lifecycle â¢ Proven excellence in Risk Management and control â¢ Good understanding of Software Development Lifecycle (SDLC) â¢ Ability to synthesize qualitative and quantitative data quickly and draw meaningful insights â¢ Knowledge of Programme/Project Management methodologies with full project reporting and governance â¢ Ability to work with different cross-functional stakeholders to establish and ensure a reliable and productive working relationship â¢ Strong time management and organizational skills â¢ Multitasking skills and ability to meet deadlines COMPUTER SKILLS AND CERTIFICATION â¢ Advance knowledge in MS office 2013 and Macros. SKILLS â¢ Strategic thinking and decision making ability â¢ Sound Analytical skills â¢ Multi-tasking skills in fast paced environment. â¢ Leadership and Inter Personal Skills. â¢ Strong information management ability, particularly MS excel extraction, formulae, pivots and graphs. Education Details 
January 2005 Bachelor of Business Administration Business Administration Pune, Maharashtra Modern College
 HSC  Pune, Maharashtra S.S.P.M.S College
 SSC  Pune, Maharashtra Saints High SchoolPMOHaving an exp of 6 years experience in Project Management in IT. Expertise in PMO, Team handling, Quality Analyst. Proficient in Data Analyzing tools and techniques.Skill Details 
DOCUMENTATION- Exprience - 47 months
GOVERNANCE- Exprience - 19 months
EXCEL- Exprience - 6 months
FORECASTING- Exprience - 6 months
MS EXCEL- Exprience - 6 monthsCompany Details 
company - Capita India Pvt ltd
description - Pune

Key Result Areas
Responsible for successful transition of knowledge, system and operating capabilities for Prudential, Multiclient, Pheonix & Royal London.
 â¢ Travelled Onsite (Glasgow) and being part with UK team to understand the transition PMO work process and execute successfully at Offshore.
â¢ Successfully transitioned Work order Management, Governance and Reporting from UK.
â¢ Lead a team of 6 members and follow up on the development of new Ways of Working & documentation processes.
â¢ Manage internal and external stakeholder engagement, collaboration of teams, and global PMOs network â¢ Helps achieve robust operations with all the resources and infrastructure to execute steady state operations.
company - Saviant Technologies
description - for Multiple Projects 
â¢ Established a PMO from scratch and provided seasoned leadership to the technical operations staff â¢ Defined and implemented work priority management and resource management processes â¢ Established a supportive environment that allowed employees to grow and provide imaginative solutions to complex client need â¢ Track and monitor financial performance of the program. Report financials for actual to budgeted comparison for labor hours and dollars, operating costs and capital costs. Secure funding approvals for changes in scope â¢ Monitor program risks through an on-going process of identifying, assessing, tracking, developing and executing risk mitigation strategies â¢ Reviewed project documentation and document lessons learned and provide recommendations to mitigate them in future projects.
â¢ risk identification, mitigation strategy, issue escalation, client communication, project timeline, and resource management
company - Infosys
description - Pune

Key Result Areas
Responsible for:- â¢ Resource management, Budgeting, Billing.
â¢ Responsible for preparing and sharing different reports with Delivery Managers, Project Managers, Quality team â¢ Automation of reports for entire unit â¢ Interpret data, analyze results using statistical techniques and provide ongoing reports.
â¢ Preparing case diagrams & activity diagrams for various scenarios.
â¢ Collate data, study patterns and Conduct brainstorming sessions to identify outliers.
â¢ Review and approve project documentation.
â¢ Assist in identification of risks in the project and setting up of mitigation plan of the risk by reviewing dashboards and   reports.
â¢ Customer feedback information and analysis.
â¢ Reviews and validate the inputs from Project Mangers regarding Dashboards and PPT's â¢ Supporting TL by training people on process/domain as a part of the growth plan SLA compliance.
company - Capita India Pvt ltd
description - Pune

Key Result Areas
Audits â¢ Reviews and validate the inputs from Managers regarding Dashboards and PPT's â¢ Auditing work done by onshore agents and simultaneously auditing work done for my old team and their reporting part as well.
â¢ Assisting reporting manager in business transformation leadership skills with proven ability to influence and collaborate across all levels of the organization.
â¢ Helping line managers to solve specific audit problems, either on a one-to-one basis or in groups.

Reporting â¢ Preparing weekly / monthly / quarterly / yearly MIS -Variance report, Performance report, Feedback analysis, Task activities report, publish relevant business Dashboards, Projects audit report."
PMO,"AREA OF EXPERTISE (PROFILE) Around 10 plus years' proven experience with best global brand Wipro with below expertise:- â¢ PMO â¢ ITIL Management â¢ Process Improvements â¢ Project Process Audits â¢ Planning, Scheduling, Effort/Issue/Risk Tracking â¢ Risk & Issue Management â¢ SLA Management â¢ Workforce (staffing) Resource Management. â¢ Transition â¢ Operations management SKILLS Project Management Tools: CA Clarity, Visio and Office, ITIL -Incident management, Recruitment and workforce management Technical: SAP- HR, MRS, CPRO, Confluence, Microsoft Office, Word, PowerPoint.Excellent knowledge & hands on experience in advanced MS Excel (Knowledge on MS Project, Sharepoint Reporting & Ticket Tool: Xtraction, CA Service Desk, I-Tracker, Education Details 
 MBA HR and Finance Bengaluru, Karnataka RKIMS CollegeSenior Executive PMOSenior Executive PMO ConsultantSkill Details 
OPERATIONS- Exprience - 125 months
STAFFING- Exprience - 125 months
HR- Exprience - 79 months
PMO- Exprience - 84 monthsCompany Details 
company - Ensono LLP
description - Roles &Responsiblites
Â 
ÃÂ Responsible for creation of Structured reports and present the same as to Senior Deliery management as per the business requirements.
ÃÂ Design and draft various reports as per the business requirements.
ÃÂ Responsible for creation of MOM, chasing people and getting the SLA driven on time by achieving the targets and results on time.
ÃÂ Assist the Project managers in creating the RRâs Deputation, invoicings, billing activites.
ÃÂ Maintaining Clarity and Sharepoint data for service delivery management
ÃÂ Perform customer invocing at the direction of the CEM and SDM.
ÃÂ Weekly preparation of SLA and KPI data based on the manual tracker & sharing with Client & senior management.
ÃÂ Project implementation management, invoicing and billing management, and participate in establishing clientâs contractual documentation
ÃÂ Experience in various delivery models like Managed Services, Fixed Price, T&M, SLA based Risk and Penalty is required.
ÃÂ Manage the SLA targets and save penalty towards customers . Drive SLA calls with 80 plus customers with multiple towers.
ÃÂ SPOC for time on floor analysis (TOFA) report & highlighting the employee tailgating data to high level management
ÃÂ Ensure for any compliance related issue and floor maintenance
ÃÂ Ensure asallÂ joining formalities and on boarding activities for new employees.
ÃÂ Identify and drivekey metrics like Billing efficiency, Resource Utilization.
ÃÂ Maintain the project library, filing, recording and reporting systems.
ÃÂ Monitor project progress, risks, roadblocks, and opportunities and manage communications to stakeholders.
ÃÂ Develop Flow charts /SOPâs ad maintain the process changes database& monitor the severity calls.
ÃÂ Prepare Monthly reports Operational report, Capacity/utilization report, Timesheet report, SLA compliancereport. Quarterly report Operational report (quarter trends)
 Internal report Allowances, Billing reports, Repository maintenance of documents.Create project/ sub-project plans & monitor progress against schedule, Maintain risk & issue logs
ÃÂ Actively participate in the project management communities
ÃÂ Responsible for Project Cost, Schedule, Budget, Revenue& Milestone Progress.
company - Wipro Technology
description - Roles &Responsiblites
Â 
ÃÂ Responsible for creation of Structured reports and present the same as to Senior Deliery management as per the business requirements.
ÃÂ Design and draft various reports as per the business requirements.
ÃÂ Responsible for creation of MOM, chasing people and getting the SLA driven on time by achieving the targets and results on time.
ÃÂ Assist the Project managers in creating the RRâs Deputation, invoicings, billing activites.
ÃÂ Maintaining Clarity and Sharepoint data for service delivery management
ÃÂ Perform customer invocing at the direction of the CEM and SDM.
ÃÂ Weekly preparation of SLA and KPI data based on the manual tracker & sharing with Client & senior management.
ÃÂ Project implementation management, invoicing and billing management, and participate in establishing clientâs contractual documentation
ÃÂ Experience in various delivery models like Managed Services, Fixed Price, T&M, SLA based Risk and Penalty is required.
ÃÂ Manage the SLA targets and save penalty towards customers . Drive SLA calls with 80 plus customers with multiple towers.
ÃÂ SPOC for time on floor analysis (TOFA) report & highlighting the employee tailgating data to high level management
ÃÂ Ensure for any compliance related issue and floor maintenance
ÃÂ Ensure asallÂ joining formalities and on boarding activities for new employees.
ÃÂ Identify and drivekey metrics like Billing efficiency, Resource Utilization.
ÃÂ Maintain the project library, filing, recording and reporting systems.
ÃÂ Monitor project progress, risks, roadblocks, and opportunities and manage communications to stakeholders.
ÃÂ Develop Flow charts /SOPâs ad maintain the process changes database& monitor the severity calls.
ÃÂ Prepare Monthly reports Operational report, Capacity/utilization report, Timesheet report, SLA compliancereport. Quarterly report Operational report (quarter trends)
 Internal report Allowances, Billing reports, Repository maintenance of documents.Create project/ sub-project plans & monitor progress against schedule, Maintain risk & issue logs
ÃÂ Actively participate in the project management communities
ÃÂ Responsible for Project Cost, Schedule, Budget, Revenue& Milestone Progress.
company - Wipro InfoTech
description - Responsibilities
â¢ Monitor and manage the headcount actual Vs plan for the region to maintain the headcount ratio with the revenue.
â¢ Maintain and monitor the correct tagging in SAP (Project tagging, supervisor tagging, org unit and cost center) for the region so that the financials are maintained properly.
â¢ Responsible in providing the exact and accurate headcount report for GM calculation.
â¢ Responsible in managing the bench management and deploy the resource.
â¢ Responsible in managing and driving tenure management for the eligible employee and deploy them according to their aspiration and business need.
â¢ Responsible in Hiring and maintaining the Rookie Ratio for the location and actively track their training and deploy them.
â¢ Analyze past volume and staffing patterns and will implement the actions based on the forecast provided so that the resource crunch can be addressed and the make sure the availability of the resources on time for go live.
â¢ Validate the head count plan for the project and work with Stake holders (Service Delivery Managers) in optimizing the resources.
â¢ Ensure all required WFM data is tracked and trended on a continuous basis by the NLD team.
â¢ Identify the resource that had completed tenure with the project and plan their training with the help of training team and elevate them to higher roles and back fill the same with the ROOKIE'S (TRB, TE, WIMS, and SIMS)
â¢ Interface with Service Delivery Managers/Director as needed for escalation on service impacting issues due to resource availability.
â¢ Coordinates with stake holders of Operations to interface with client and handle account management issues and add resources as per the requirement.
â¢ Manages the staff schedules and responsibilities of Workforce Management team for the Region/BU.
â¢ Prepare daily/weekly/monthly reports and distribute to the Management team.
â¢ Manages staffing ratios and seat utilization/optimization to ensure Project goals are met. Builds effective working relationships with internal departments.
â¢ Take care of special projects (PWD) and Rookie hiring model, Training, deployment.

PERSONAL DETAIL
DOB: 21/03/1986
PAN: **********
Passport: ********
Linguistic Ability: English, Hindi, Marathi, Kannada and Konkani
Location: Pune, India
Marital Status: Married"
PMO,"Skills Exceptional communication and networking skills Successful working in a team environment, as well as independently Ability to work under pressure and multi-task Strategies & Campaigns Corporate Communications MIS Reporting & Documentation Training & Development Sales Support & Back Office Operations New Process Development & Launch Handling customer escalationsEducation Details 
 BACHELOR OF BUSINESS ADMINISTRATION BUSINESS ADMINISTRATION  ICFAI Business School
    Integrated Institute Of Management &Technology
    HIGHER SECONDARY SCHOOL, B.I.S.S School
   Delhi, Delhi SENIOR SECONDARY SCHOOL, Delhi Public SchoolSenior Manager - PMOSkill Details 
TRAINING- Exprience - 30 months
DOCUMENTATION- Exprience - 16 months
OPERATIONS- Exprience - 16 months
SALES- Exprience - 8 months
CORPORATE COMMUNICATIONS- Exprience - 6 monthsCompany Details 
company - 
description - Review and understand existing business processes to identify functional requirements to eliminate
waste, improve controllership and deliver flexibility
Identify processes for re-design, prototype potential solutions, calculate trade-offs, costs, and suggest a
recommended course of action by identifying modifications to the new/existing process
Project Management of new requirements and opportunities for applying efficient and effective solutions
Responsible for delivering process reengineering projects across processes by closely working with the relevant businesses and operations units.
Responsible for documentation to train all stakeholders on any changes
company - 
description - Responsible for defining scope of project in accordance with the stakeholders, internal teams and senior
management team.
Prepare project charter with defined timelines for project related activities.
Preparation of Business Requirement Document (BRD), closing Understanding Document (UD) with development team, UAT completion and deployment.
Preparation of training documents, SLAs, SOPs etc. as required.
Conduct training for impacted teams to ensure smooth transition.
company - TELEPERFORMANCE INDIA
description - Driving sales through call center and achieve target with overall responsibility of exploring selling opportunities by understanding customer preferences and requirements.
Conceptualizing and implementing sales promotional activities as a part of pilot batch for new company launch.
Training new joiners through the process of call barging.
Interaction with client to understand requirements and expectations.
Handling call quality sessions with the client.
Handling adhoc requirements from client as well as senior management and delivering timely resolution for the same.
MASTER OF BUSINESS ADMINISTRATION"
Database,"TECHNICAL EXPERTISE â¢ DB Languages: SQL â¢ Database Tools: SQL Server 2014/ 2017 Postgresql 9.5, 9.6, Oracle 11gR2 â¢ Operating Systems: Redhat Linux, Oracle Linux, Windows Server 2012/ 2016 OTHER TECHNICAL SKILLS ORACLE 11G R2 â¢ Proficient in Oracle Database Software Installation, Creation of Database using GUI/Silent DBCA, Architecture, File management, Space Management, User Management, Creating Roles and assigning Privileges/Roles in 11gR2 and troubleshooting them. â¢ Hands on experience Control files/Redolog/Archive/Undo Management â¢ Configuring Listener.ora/Tnsnames.ora file using Netmgr/netca â¢ Generating AWR reports, ADDM, ASH reports to diagnose the problems â¢ Database Backup, Cloning/Duplicate using hot & cold backups using RMAN. â¢ Knowledge in Flashback Technologies & Expdp/Impdp â¢ Implemented Oracle11gR2 RAC on Oracle Linux Platform and knowledge of services for troubleshooting RAC (CRSCTL, SRVCTL) â¢ Knowledge on installation and configuration of RAC. Add/Remove Nodes on RAC â¢ Configuration of physical standby database (Data guard) â¢ Successfully upgraded from 11.2.0.1 to 11.2.0.4 & PSU patching using O patch. STRENGTHS â¢ Good Communication skills. â¢ Self-confident and can adapt myself to all work environments. â¢ Enjoy responsibilities as lead and team player. â¢ Patient listener & quick learner. â¢ Capable of explaining issues & solving them.Education Details 
 B.E Computer Engineering Mumbai, Maharashtra Mumbai University
 Higher Secondary Certificate   Dr. DY Patil Jr CollegeDatabase AdministratorDatabase Administrator - DBA in Marketplace Technologies LtdSkill Details 
DATABASE- Exprience - 61 months
BACKUPS- Exprience - 48 months
LINUX- Exprience - 48 months
MS SQL SERVER- Exprience - 48 months
SQL- Exprience - 48 monthsCompany Details 
company - DBA in Marketplace Technologies Ltd
description - Project Title: EBoss, Datafeed, MFDB, RTRMS, IndiaINX
company - Standard & Enterprise
description - Redhat Linux 7.4, Postgresql 9.5, 9.6
Duration: Feb 2017 - till date
Description: Bombay Stock Exchange BSE  is Asia's first & the Fastest Stock Exchange in world with the speed of 6 micro seconds and one of India's leading exchange groups provides an efficient and transparent market for trading in equity, currencies, debt instruments, derivatives, mutual funds. BSE SME is India's largest SME platform which has listed over 250 companies and continues to grow at a steady pace.

JOB ROLES & RESPONSIBILITIES
POSTGRESQL - â¢ Worked on Redhat Linux OS Cluster with Postgresql for High Availability (HA) using Pacemaker.
â¢ Coordinated with Developers/Linux teams for database knowledge and support.
â¢ Participated in implementation of new releases into production.
â¢ Installed /Configured Postgresql from source or packages on Redhat Linux servers.
â¢ Performed Postgresql Server Management tasks i.e. Backup & Restore, Configuration, Roles, Blockings, Tablespace creation and Troubleshooting.
â¢ Worked with Storage team for Disaster Recovery DR setup built on SAN using EMC technology â¢ Configured LDAP authentication & GSSAPI Authentication from Windows to Linux for Postgresql.
â¢ Configured logical replication for Database servers, hot standby Postgresql servers, faster database backup methods, schema and tablespace backups.
â¢ Configured maximum connections to database on Linux servers.
â¢ Installed tds_fdw from source for linked servers to connect to heterogeneous databases & other required extensions, backup configuration, PITR using base backups.

MSSQL - â¢ Day-to-day administration of live SQL Servers.
â¢ Participated in Live Primary Recovery PR & Disaster Recovery DR activities.
â¢ Participated in PR & DR mocks for new releases into production.
â¢ Configured Linked Servers, Transactional replication, Maintenance tasks like database backup & restore, recovery, scheduled jobs, maintenance plans.
â¢ Installed & Configured SQL server 2014, 2017 standalone and SQL Cluster servers.
â¢ Maintained the security of the database by providing appropriate SQL roles, logins and permissions to the users on demand.
â¢ Worked with teams on application rollouts, application issues and SQL server migrations.
â¢ Exposure in handling production system with skills and understand client's requirement.
â¢ Performed SQL Server service pack upgrades and hot fixes.
â¢ Handled multiple SQL Instances on Windows SQL Cluster environment built on  EMC SAN.
â¢ Worked on MSSQL DB clusters with active/active & active passive servers, Always-On Availability Groups (AAG) and HA/DR Setup.
â¢ Have experience on SAN and RAID levels and building and supporting SQL Cluster servers on SAN Environments.
company - BSE Bombay Stock Exchange
description - Environment: Windows server 2008 R2, 2012 R2, 2016 Enterprise & Standard,"
Database,"Technical Expertise Operating Systems Microsoft Window Server 2003/2008/2008 R2/2012 Database Technologies SQL Server, Sybase ASE Server, Oracle, MongoDB Monitoring and Ticketing Tools HP Service Manager 7.0/9.0, Solar winds DPA, JIRA and MongoDB OPS manager Web Server IIS 7.0 Database Tools SSMS, DBArtisan, Studio 3T, SnapShot Manager for SQL ServerEducation Details 
 B. Tech Computer Science Gulbarga, Karnataka PDACOE, Gulbarga, Autonomous InstitutionDatabase Administrator IIDatabase Administrator III - BNY Mellon International Operations (India) PVT. LTDSkill Details 
Sql Dba- Exprience - Less than 1 year monthsCompany Details 
company - BNY Mellon International Operations (India) PVT. LTD
description - SQL Server :
ï	Installation, configuration of database servers using slipstream and setup all the maintenance jobs as per the standard policy on standalone as well as cluster environments with latest service packs
ï	Installation of SSRS, uploading of .rdls and assigning correct data sources to reports. Grant necessary access to users & developers on reporting website. Aware of SSIS and designing packages as well.
ï	Create and manage logins, users for database applications, assigning permissions as per requests, resolving user login issues.
ï	Migration of all SQL server 2005/2008 servers to higher versions.
ï	Setup of database refresh jobs on QA, DEV and UAT environments and fixing orphaned users.
ï	Troubleshoot performance related issues. 
ï	Part of multiple projects to work with developers and provide all required support for testing in QA, UAT & DEV environment. 
ï	Lead the DR tests for database team.
ï	Participate in database purge and archive activities.
ï	Writing codes for automating database administration tasks.
ï	Worked on automating DR tasks to start the agent jobs on multiple servers, restore databases for log shipped databases without manual intervention for online databases post DR activities.
ï	Provide support to vendor databases, follow up with the vendor calls and timely escalate to next level when there is no update in predefined timeline.
ï	Installation and configuration of smsql on windows server. Schedule jobs for creation and deletion of clones on sql server. Maintain backups using smsql.

MongoDB Server:
ï	Installation and configuration of MongoDB server.
ï	Creation of databases and collection.
ï	Creation new user and grant access using Ops manager.
ï	Monitor database servers using Ops manager.
Oracle & Sybase Server
ï	Managing and maintaining multiple instances of Databases on Linux and windows servers.
ï	Monitoring daily jobs includes backups, refresh and maintenance jobs.
company - Hewlett-Packard India Sales PVT. LTD. On the payroll of Softenger India PVT. LTD
description - ï	Installation of SQL Server on standalone as well as windows cluster environments with latest service packs
ï	SQL server installation using slipstream.
ï	Installation of reporting services
ï	Creating logins and users, assigning permissions as per requests.
ï	Security audit for all logins includes maintenance of unused and orphan user logins
ï	Create & Maintain daily and weekly jobs/maintenance plans includes backup, index rebuild/reorganize , update statistics and database consistency check
ï	Create linked servers and ensure connectivity between servers
ï	Monitor disk space proactively & Space management using data and log file shrinking
ï	Monitor blocking, deadlocks, open transactions and slow running queries during performance issues and highlight costly queries to developers.
ï	Configure alerts for deadlock and blocking to maintain performance
ï	Implementing high availability technologies like log shipping, AlwaysON, mirroring and its troubleshooting, also have knowledge on replication
ï	Successfully completed migration of Databases from one server to another
ï	Performing DR drills (Online/Offline) on quarterly basis
ï	Power shell scripting to monitor, restart SQL service and get Email alert for the service status.
ï	Maintain TNS entries for oracle client as per client requests.
ï	Interacting with customers for requirements
ï	Contacting customer to update the status of handling issues and service requests at every stage of resolution
ï	Managing proper escalation and notification matrix for all support levels"
Database,"TECHNICAL SKILLS Operating Systems MS Windows Server 2012/2008/XP Software and Tools MS LiteSpeed, Idera SQL Safe, SSMS, Upgrade Advisor, SQL Server Profiler, SCOM, Diagnostic Manager, Remedy, Jira, Infopacc, Tivoli TDP backup tool, SQL Pack DatabasesMS SQL Server 2016/2014/2012/ 2008 R2/ 2008, Oracle 10g, Netezza Microsoft azure Education Details 
 Masters of Science Computer Science Pune, Maharashtra Indira College, Pune UniversityLead database administratorMicrosoft Certified Professional with 11 years of experience in database administration on MS SQL Server 2016/2014/2012/2008 R2/ 2008Skill Details 
MS SQL SERVER- Exprience - 110 months
Microsoft azure- Exprience - Less than 1 year months
Always on availabiity group- Exprience - Less than 1 year months
Database mirroring- Exprience - Less than 1 year months
Performance tuning- Exprience - Less than 1 year months
Log shipping- Exprience - Less than 1 year months
Installation , upgrade, migration and patching- Exprience - Less than 1 year monthsCompany Details 
company - Ensono
description - Employment transfer as a part of project acquisition to Ensono from Wipro.
SQL Server Database Administration
company - Wipro Technologies
description - Microsoft Certified Professional with 11 years of experience in database administration on MS SQL Server 2016/2014/2012/2008 R2/ 2008.
Experience with MS SQL Server 2016/2014/2012/2008 R2/ 2008 installation, upgrade, and administration
Microsoft Azure certified.
Have understanding of Azure VM, Azure Storage, Azure network, Azure AD and Azure SQL database.Â 
Incident management, change management and Problem management for SQL Server Database team.
Participating in meetings, conference calls with client, Service Delivery Manager and Application team for System improvements.
Participated in quarterly DR activity.
Involved in creation of SIP - Service Improvement Plans
Involved in handling of high severity issues and provided RCA for the same.
Worked on Always on availability groups, database mirroring, replication, clustering and log shipping.
Have basic understanding of Oracle and Netezza.
Provided on- call support during out of office hours and weekends.
Resource & shift management of 5 SQL DBAs from offshore in multi-client environment for Data center services.
Provided KT to team members, monitor and guide trainees.
company - Wipro Technologies
description - Responsibilities: â¢ MS SQL Server 2016/2014/2012/ 2008 R2/ 2008 installation, configuration, and administration.
â¢ Worked on Always on availability groups, log shipping, database mirroring and clustering.
â¢ Participated in  PCI scan report to perform installation of security hot fixes, service packs for SQL servers to remove vulnerability.
â¢ Participated in Holmes BOTS automation implementation of SQL Pack tool.
â¢ Worked on service requests, incidents and critical issues.
â¢ Involved in conference calls to provide DBA support for critical issues.
â¢ Performance tuning.
Environment: SQL Server 2016/2014/2012/2008R2/2008, Windows Server 2012/2008R2/2008
company - Mphasis
description - 
company - Mphasis
description - Responsibilities: â¢ MS SQL Server 2012/ 2008 R2/ 2008  installation, configuration, and administration.
â¢ Worked on Always on availability groups, log shipping, database mirroring and clustering.
â¢ Performed SQL server patching activity â¢ Worked on daily reports like cluster failover, backup, AG/LS/Mirror report and server disk space report.
â¢ Worked on service requests, incidents and critical issues.
â¢ Participated in quarterly DR activity.
â¢ Involved in conference calls to provide DBA support for critical issues.
â¢ Provided support to windows team during patching for AG-mirror-cluster failover/failback and database health check.
â¢ Performed all the health checks for market open servers and provided update in market open call â¢ Deeply involved in   resolution of the issue and finding the root cause analysis of the issue â¢ Performance tuning.
Environment: SQL Server 2012/2008R2/2008, Windows Server 2008R2/2008
company - Synechron Technologies Pvt. Ltd
description - Responsibilities: â¢ SQL server, Oracle and Netezza databases support tasks.
â¢ MS SQL Server 2008 R2/ 2008 installation, upgrade, and administration.
â¢ Done capacity planning for database growth for all SQL servers.
â¢ Troubleshooting alerts.
â¢ Worked on log shipping and mirroring.
Environment: SQL Server 2008R2/2008, Windows Server 2008R2/2008, Oracle 10g/RAC
company - Synechron Technologies Pvt. Ltd
description - 
company - Synechron Technologies Pvt. Ltd
description - Responsibilities: â¢ Pursued in-depth training on Oracle 11g Architecture and SQL Server.
Environment: SQL Server 2008R2/2008, Windows Server 2008R2/2008, Oracle 10g
company - Synechron Technologies Pvt. Ltd
description - Responsibilities: â¢ Carried out version changes for schemas from PE8 version to EE11 version as per the process given
Environment: Oracle 11g
company - Mastek Ltd
description - Responsibilities: â¢ SQL Server 2008 R2/ 2008 installation, upgrade, and administration â¢ database backup/restore.
â¢ Performed MS SQL Server audits â¢ Worked with database mirroring, replication, log shipping and clustering.
â¢ Supported UAT and PROD environments â¢ Performed deployment document review.
â¢ Carried out deployments for different applications
Environment: SQL Server 2008R2/2008, Windows Server 2008R2/2008
company - Mastek Ltd
description - 
company - PP Software and Systems Ltd
description - 
company - PP Software and Systems Ltd
description - Description: The system provides Master Data Management and Procurement modules for dairy industry.
Responsibilities: â¢ Designed, coded, and tested â¢ Customized ERP system as per the requirement
Environment: Core Java, PostgreSQL"
Database,"SKILLSET Oracle DBA, MySQL, MARIADB, PostgreSQL Database Administration ITSKILLS SQL Oracle 10g, 11g, MYSQL, MariaDB, postgreSQL Windows, Linux Putty Education Details 
January 2018 MCS  Pune, Maharashtra Pune UniversityDatabase administratorDatabase administrator  - Infiniteworx Omnichannel Pvt. LtdSkill Details 
DATABASE- Exprience - 17 months
MYSQL- Exprience - 17 months
ORACLE- Exprience - 17 months
SQL- Exprience - 17 months
DATABASE ADMINISTRATION- Exprience - 6 monthsCompany Details 
company - Infiniteworx Omnichannel Pvt. Ltd
description - Pune Sept 2017 to Present

RESPONSIBILITIES:
â¢ Creating tablespaces and planning the location of data, monitoring the tablespaces growth periodically.
â¢ All replication setup
â¢ Moved database Schema changes to stage.
â¢ Dba support query resolution.
â¢ Creating user and giving specific privileges
â¢ Database management.
â¢ Database recovery, moving data files to different locations.
â¢ Planning the backup policies and Backup/ Recovery of databases based on the criticality.
â¢ IMPORT/EXPORT.
â¢ Degine schemas

Key Result Areas:

â¢ Providing 24 /7 support to resolve database performance issues, Job failures, Sessions & diagnose root causes
â¢ Installation, configuring and updating Oracle server software and related Oracle products. Installation, configuraing and updating Mysql, Sql server, MariaDB, MongoDB
â¢ Supported multiple databases and administered Oracle Databases of Large DB Sizes for production, development & test setups.

â¢ Maintaining table spaces & data files, Control files, Online Redo log files

â¢ Creating Users, granting Roles & Privileges to users and managing tablespaces for different users by granting quota on Default & Temporary tablespaces.

â¢ Taking Oracle RMAN Backups (Scheduling for day wise backup)

â¢ Implementing the incremental, cumulative and full RMAN backup for each database to have space management and effective recovery.
â¢ Logical Backup Using Export & Import/datapump Export of important tables at regular intervals.

â¢ Regular checking of trace, alert log file, all ORA errors

â¢ Working on incidents like User creation/deletion incidents, backup failed incidents.
â¢ Checking Listener Status, connectivity Troubleshooting and fixing database listener issues.
â¢ Look for any new alert / error log entries / errors, error details in Trace files generated. Executing DDL & DML scripts as per customer requirements

â¢ Mentoring, coaching and appraising team members with active involvement in the recruitment process

â¢ Contributing in Project Documentation and generating daily reports

â¢ Ensuring compliance to quality norms and taking steps for any non-conformance Spearheading complete project activities ensuring timely completion of project
â¢ Implementing security policies on different database systems with granting and revoking privileges to the users

â¢ Following change management processes and participated in related meetings

â¢ Verifying all Instances/DB are running, Tablespaces are online, Monitor Backround processes and status.
company - InnovativeTechnologies
description - Clients: BANKING DOMAIN"
Database,"Education Details 
January 2016 BSc.  Mumbai, Maharashtra Mumbai University
January 2013 H.S.C.   Maharashtra Board
January 2011 S.S.C.   Maharashtra BoardMySQL Database Administrator2+ Years of experience in MySQL Database Administrator ( MySQL DBA)Skill Details 
MySQL DBA , Centos , Backup , Restore , Replication , Query Optimazation- Exprience - 24 monthsCompany Details 
company - Trimax IT Infrastructure & Services Ltd
description - Â·Â Â Â Â Â Â Â MYSQL Installation, maintenance and Upgrades (Version 5.5 , 5.6)
Â·Â Â Â Â Â Â Â MySQL database administration on a large scale MySQL installation
Â·Â Â Â Â Â Â Â Experience with MySQL on both Linux and Windows
Â·Â Â Â Â Â Â Â MySQL processes, security management and queries optimization.
Â·Â Â Â Â Â Â Â Performed query analysis for slow and problematic queries.
Â·Â Â Â Â Â Â Â Performed Structural changes to Database like creating tables, adding columns according to business requirement
Â·Â Â Â Â Â Â Â Creating and MaintainingÂ Database Maintenance Plans.



Â·Â Â Â Â Â Â Â Writing scripts to Create Jobs for Backup & Restore Plans.
Â·Â Â Â Â Â Â Â Working on MYISAM to INNODB engine.
Â·Â Â Â Â Â Â Â Working on Server shifting , tuning parameter , database purging
Â·Â Â Â Â Â Â Â Working on Mysql master slave Replication
Â·Â Â Â Â Â Â Â Handling Release management and user acceptance.
Â·Â Â Â Â Â Â Â Restore using xtrabackup.
Â·Â Â Â Â Â Â Â Responsibilities include monitoring daily, weekly and monthly system maintenance tasks such as database backup, replication verification, database integrity verification and indexing updates
Â·Â Â Â Â Â Â Â Work in 24/7 production database support.
company - Trimax IT Infrastructure & Services Ltd
description - Â·Â Â Â Â Â Â Â MYSQL Installation, maintenance and Upgrades (Version 5.5 , 5.6)
Â·Â Â Â Â Â Â Â MySQL database administration on a large scale MySQL installation
Â·Â Â Â Â Â Â Â Experience with MySQL on both Linux and Windows
Â·Â Â Â Â Â Â Â MySQL processes, security management and queries optimization.
Â·Â Â Â Â Â Â Â Performed query analysis for slow and problematic queries.
Â·Â Â Â Â Â Â Â Performed Structural changes to Database like creating tables, adding columns according to business requirement
Â·Â Â Â Â Â Â Â Creating and MaintainingÂ Database Maintenance Plans.



Â·Â Â Â Â Â Â Â Writing scripts to Create Jobs for Backup & Restore Plans.
Â·Â Â Â Â Â Â Â Working on MYISAM to INNODB engine.
Â·Â Â Â Â Â Â Â Working on Server shifting , tuning parameter , database purging
Â·Â Â Â Â Â Â Â Working on Mysql master slave Replication
Â·Â Â Â Â Â Â Â Handling Release management and user acceptance.
Â·Â Â Â Â Â Â Â Restore using xtrabackup.
Â·Â Â Â Â Â Â Â Responsibilities include monitoring daily, weekly and monthly system maintenance tasks such as database backup, replication verification, database integrity verification and indexing updates
Â·Â Â Â Â Â Â Â Work in 24/7 production database support."
Database,"TECHNICAL SKILL: Operating System LINUX, Windows Server 2012 R2, Windows 98, Windows 2000/ XP Tools & Utility Packages SQL* Loader, SQL*PLUS, OEM, Datapump, expdp/impdp, PLSQL Developer, Jenkins Database Oracle 10g, Oracle 11g, Oracle 12c Scripting UNIX Shell Scripting Language SQL Education Details 
January 2011 M.B.A.  Amravati, Maharashtra Amravati University
January 2007 B.C.A.  Nagpur, Maharashtra Nagpur UniversityOracle Database AdministratorORACLE DATABASE ADMINISTRATOR ON LINUX/MICROSOFT WITH 4 YEARS EXPERIENCE.Skill Details 
ORACLE- Exprience - 48 months
LINUX- Exprience - 6 months
ORACLE DBA- Exprience - Less than 1 year months
RAC- Exprience - Less than 1 year months
GOLDEN GATE- Exprience - Less than 1 year months
ASM- Exprience - Less than 1 year months
DATAGUARD- Exprience - Less than 1 year monthsCompany Details 
company - TIETO INDIA PVT. LTD
description - Pune From February 2015 till present

Project Profile:
Oil and Gas unit of Tieto India Pvt. Ltd. is working for Environmental Components (EC) application. Tieto is the authorized service provider in EC. Energy Components is a complete end-to-end hydrocarbon accounting solution following the hydrocarbons from production to transport, sales and revenue recognition. Globally market-leading hydrocarbon accounting software with functionality coverage exceeding other available solutions. Modern, flexible and scalable technology platform. Selected as the global standard and best practice by oil & gas super majors.
Responsibilities: â¢ Oracle Database Administration 11g R2, 12c and 18c â¢ Supporting databases in 24x7 environments and coordinate with Application, OS, Storage and Development Teams. Test and Production environments â¢ Regularly monitoring the trace files and Alert log files for database related issues.
â¢ Experience in monitoring the CPU usage, IO and memory utilization at OS level.
â¢ Checking the Alert log file to analyze the ORA errors if any to raise SR with Oracle.
â¢ Monitoring the log files, backups, database space usage and the use of system resources.
â¢ Configuring Backup (RMAN) for database and restoring database.
â¢ Installation, configuring and updating Oracle server software and related Oracle products of 11g and 12C.
â¢ Oracle Server installation, client installation and configuration, PLSQL developer installation.
â¢ Creating database using DBCA and manually.
â¢ Creating of Oracle user and granting proper privileges to user as per request.
â¢ Creating AWR, ASH and ADDM reports for database performance analysis.
â¢ Handling space management and performance issues in Oracle databases.
â¢ Creating remote database link.
â¢ Renaming and resizing of data files in Oracle database if needed.
â¢ Tablespace shrinking with regular time interval to reclaim server space.
â¢ Expertise in Export and Import using data pump in Oracle database.
â¢ Expertise in Configuration of Listener and Tnsnames through NETMGR and NETCA and statically also.
â¢ Managing Oracle Listener and Oracle Network Files.
â¢ Creating user Profiles, granting specific privileges and roles to the users in Oracle database.
â¢ Maintaining tablespaces & data files, Control files, Online Redo log files in Oracle database.
â¢ Worked on AWS cloud services like EC2, S3, RDS, ELB, EBS, VPC, Route53, Auto, Cloud watch, Cloud Front, IAM for installing configuring and troubleshooting on various Amazon images for server migration from physical into cloud."
Database,"Technical Skills Databases: Oracle RDBMS- 10g, 11g & 12c Technology/utilities: Data Pump, RMAN, Data guard, ASM, RAC, Golden Gate Tools: OCC, PUTTY, SQLPLUS, SQL Developer, Netbackup, SCOM, SCCM, VMWare Vsphere Operating Systems: RHEL 6.0, RHEL 6.5, UNIX and Microsoft WindowsEducation Details 
Database AdministratorDatabase Administrator - BNY MellonSkill Details 
DATABASES- Exprience - 24 months
ORACLE- Exprience - 24 months
RMAN- Exprience - 24 months
NETBACKUP- Exprience - 24 months
SCOM- Exprience - 24 monthsCompany Details 
company - BNY Mellon
description - Databases: 600+
Team Size: 8
Duration: Jan 2017 - Till Date
Clients: Over 130+ investment banking organizations who are hosted with Eagle

Responsibilities: Database Management (Support and managing critical production, Pre-production, test and reporting databases in different platforms), Capacity Management Upgrades.
â¢ Handling day to day database activities monitoring and incident management.
â¢ Building new databases as per the requirement and prepare them for go live with the help of multiple teams.
â¢ Working on scheduled activity of database patching (CPU, PSU) â¢ Installing latest path on production, Dev and Test databases as per the suggestion from Oracle support.
â¢ Database Upgrade from 11g and to 12c.
â¢ Adding disks to ASM disk groups.
â¢ Building DR database using Active Data guard, Make it sync with prod and resolving issues if persists â¢ Data Guard Management- Checking lagging status, removing lagging of archives, checking processes like RFS/MRP, Archives Management â¢ Working on tablespace related issues â¢ Managing user access and profiles â¢ Importing and exporting using datapump â¢ Maintaining inventory of all databases in the single centralize database â¢ Refreshing test environment from production database.
â¢ Working with Oracle Support to resolve oracle errors.
â¢ Schedule daily and weekly databases backup using RMAN, Troubleshooting in RMAN issues.
â¢ Database cloning using RMAN.
â¢ Take part in cutover to upgrade application to higher version.
â¢ Strictly following ITIL process in incident management and change management.
â¢ Providing weekly report of issues in team meeting also participating and suggesting service improvement plans.
â¢ Database Migrations from one server to another or to different platforms â¢ RCA and impact analysis reporting during any production outage.

Previous Organization: Brose India
Project I: Central IT Management
company - 
description - Responsibilities: Managing our internal databases and servers of Brose global.
â¢ Providing 24x7 on-call support in the rotational shifts.
â¢ Performing day-to-day database activity â¢ Monitoring and responding DBA group Mails for all alerts, issues and ad-hoc business user requests, etc.
â¢ Database creation, patching â¢ Backup of Database in frequent cycles using Data pump/RMAN.
â¢ Database refreshes using RMAN, Datapump.
â¢ Recovery using copy of data / RMAN â¢ Monitoring logs and trace for resolving issues.
â¢ Creating new VM servers and prepare it for go live, Also decommissioning as per requirements.
â¢ Weekly patching of windows servers using SCCM and taking actions for patching if needed â¢ Monitoring and troubleshooting of daily and weekly OS backup using Symantec Netbackup â¢ Managing user accounts of OS users and database users â¢ Monitoring OS level alerts using SCOM

Project II: Data Center Migration (Onsite Project)
Responsibilities: Data center migration was one of activity for migration of our datacenter from one location to another. Where our all servers and databases were moved successfully.
â¢ Installation of Oracle 11g on Linux platforms â¢ Worked on service requests (Incidents / Change / Request) â¢ Creation of users, managing user privileges â¢ Configured RMAN backup for databases â¢ Patching of databases â¢ Configuring physical standby database using Dataguard â¢ Cloning of servers and migrate to another cluster

ACADEMIA / PERSONAL DETAILS â¢ Bachelor of Engineering (B.E.) in Computer Science and Engineering From SGBAU Amravati University, Amravati in 2014 with CGPA of 7.21

Current Address:-       Mr. Yogesh Tikhat, C/O: Raj Ahmad, Flat# G2-702, Dreams Aakruti, Kalepadal, Hadapsar, Pune - 411028
Highest Qualification   BE (cse)

PAN: -                  **********"
Database,"Software Skills: * RDBMS: MS SQL SERVER 2000/2005/2008 & 2012, 2014 * Operating Systems: WINDOWS XP/7, WINDOWS SERVER 2008, 12 * Fundamentals: MS Office 03/07 * Tools: SSMS, Performance Monitor, Sql profiler, SQL lite speed. Company name: Barclays Technology Centre India. Team Size: 24 Role: Database Administrator Support Description: Barclays Technology is a UK based retail & invest bank and 300 years of old bank.. It has operations in over 40 countries and employs approximately 120, 000 people. Barclays is organised into four core businesses: Personal & Corporate (Personal Banking, Corporate Banking, Wealth & Investment Management), Barclaycard, Investment Banking. Responsibilities: â Attending various calls from all over the world on various database issues. â Working on Web Gui alerts and resolving incident tickets within the time lines. â Troubleshoooting log shipping issues and fixing the related alerts. â Identifying and Resolving Blocking and locking related issues. â Configuration and monitoring Replication, Log shipping and mirroring setup. â Working on replication issues and Always ON issue. â Granting and revoking permissions on various account provisioning tasks. â Working on call support during the weekend and performing DR test's. and working on weekly maintenance jobs and weekend change requests. Education Details 
 B.Sc. Maths  Kakatiya University Board securedSQL server database administratorDatabase administratorSkill Details 
DATABASE- Exprience - 120 months
DATABASE ADMINISTRATOR- Exprience - 72 months
MAINTENANCE- Exprience - 48 months
MS SQL SERVER- Exprience - 48 months
REPLICATION- Exprience - 48 monthsCompany Details 
company - Barclays global services centre
description - SQL server databases implementation and maintenances

Log shipping, replication, High availability, clustering, performance tuning, database mirroring, Installation, configuration, upgradation, migration
company - Wipro Infotech Pvt Ltd
description - SQL server database administrator
company - CITI Bank
description - Worked as Database Support at Accord Fintech, Sanpada from Sep 2008 to 2013 Feb.
company - 
description - 2012.
â¢     Sound knowledge in Database Backup, Restore, Attach, and Detach and Disaster Recovery procedures.
â¢     Developed backup and recovery strategies for production environment.
â¢     Ensuring data consistency in the database through DBCC and DMV commands.
â¢     Experience in query tuning and stored procedures and troubleshooting performance issues.
â¢     Having hands on experience in DR process including log shipping and database mirroring.
â¢     Experience in scheduling   monitoring of Jobs.
â¢     Experience in configure and troubleshooting in Always ON.
â¢     Creating and Maintaining of Maintenance Plan.
â¢     Expertise in planning and implementing MS SQL Server Security and Database permissions.
â¢     Clear understanding of Implementation of Log Shipping, Replication and mirroring of databases between the servers.
â¢     Performance Tuning (Performance Monitor, SQL Profiler Query Analyzer) â¢     Security for server & Databases Implementing security by creating roles/users,
Added users in roles, assigning rights to roles.
â¢     Create and maintaining the linked servers between sql Instances.
â¢     Create and maintaining and Database mail.
â¢     Monitor and troubleshoot database issues.
â¢     Creating DTS packages for executing the required tasks.
â¢     Experts in create indexes, Maintaining indexes and rebuilds and reorganizes.
â¢     Daily Maintenance of SQL Servers included reviewing
SQL error logs, Event Viewer."
Database,"Areas of Expertise â¢ Oracle Databases 12c, 11g, 10g â¢ Weblogic 12c, 11g â¢ Grid Infrastructure â¢ RMAN â¢ ASM â¢ Middleware: OIM, OAM, SOA â¢ Shell Scripts â¢ DataGuard â¢ Web servers - OHS, Apache â¢ Architecture Designs â¢ Proof of Concepts â¢ DevOpsEducation Details 
January 2007 Bachelor of Engineering Information Technology Sangli, Maharashtra Walchand College
January 2004 Diploma Computer Engineering Jalgaon, Maharashtra Govt. PolytechnicLead Database AdministratorLead Database Administrator - Tieto SoftwareSkill Details 
DATABASES- Exprience - 108 months
MIDDLEWARE- Exprience - 96 months
RMAN- Exprience - 84 months
SHELL SCRIPTS- Exprience - 48 monthsCompany Details 
company - Tieto Software
description - As a part of AO (Application Operations) team, scope in project is quite wide than typical database administration. Range of accomplishments are extended right from Data Tier to Middle Tier & Application Tier:
- Maximized availability of applications from 99.3% to 99.8%
- Raised business by presenting Proof of Concepts for 10+ cases
- Delivered upgrades of various applications time to time to keep it on supported platform
- Saved SLAs as per contract by means of handling P1, P2 issues effectively
- Produced Capacity reports comprising all layers (Data, Middleware, Web) of various applications
- Generated Work Orders as per customer need
company - Tieto Software
description - - Designed databases of various applications
- Planned RMAN backup and recovery, BCP strategies
- Executed Business Continuity Testing for various applications
- Introduced Zero Cost high availability solutions - Active-Passive Failover
- Optimized performance by means of scripting automation
- Established cloning procedures for all layers of various applications
- Delivered Infrastructure changes, like FW Openings & LoadBalancer configuration for new applications
- Eliminated downtime by troubleshoot issues for Middleware products - OIM, OAM, SOA
- Contributed to build & maintain Integration Layer- SMTP, ftp, Reverse Proxy, OCM
company - Tieto Software
description - - Provided database support to environments - PROD, UAT, TEST, DEV
- Performed Database Refresh/Cloning from production to development and support databases
- Reduced risk level by means of upgrading & patching databases time to time
- Protected databases by assigning appropriate roles and privileges as per SOD
- Generated & maintained Middleware schemas using RCU
- Exceeded scope of work by supporting & maintaining WebLogic platform - installation, patching, troubleshooting issues
- Expanded duty scope to web servers: Install & maintain- OHS, apache, tomcat
company - HSBC Software
description - Being part of project supporting HSBC Bank France, I achieved:
- Handled incidents & service requests as Day to day database administration tasks
- Delivered basic implementation services - Database installation, patching, upgrades
- Performed capacity planning - managing tablespaces, compressions
- Contributed in maintaining quality of databases - managing instances, indexes, re-organization, performance monitoring & tuning using AWR, ADDM reports
- Maintained backups & recovery of database - logical backups (exp/imp), datapump (expdp/impdp), cold backups, hot backups, RMAN backup/restore, RMAN Duplication
- Reduced efforts by automation - Value add initiatives which includes writing shell scripts for automated housekeeping operations, scheduling backups, use crontab/at to schedule tasks
- Implemented high availability solutions - Dataguard"
Database,"Education Details 
May 2011 to May 2014 Bachelor of science Information technology Mumbai, Maharashtra Mumbai universityOracle DBAOracle database administratorSkill Details 
Installation of Oracle on RH Linux & Windows. Creating/Managing user profiles and analyzing their privileges and tablespace quotas Backup of database Logical and Physical procedures. Recovery of database in case of database crash, disk/media failure, etc. Standard DBA functions like space management, Rollback segments, Extents. Database Management and Monitoring the database. Willing to learn new things. Being a constructive team member, contributing practically to the success of the team.- Exprience - 48 monthsCompany Details 
company - Accelya kale solutions ltd
description - Database Administrator working in 24*7 support environment maintaining Databases running on Oracle 11g, 12c.
Database Up-gradation from Oracle 11g to Oracle 12c.
Installation of Database critical patches.
Taking cold and hot backups on scheduled times and monitoring backups.
Importing the export dump to another database as per demands.
Automating most of the daily activities through cronjobs, shell scripts or schedulers.
Making Plan of Actions for Various Activities.
Raising SR with Oracle Support for different severity issues.
Handling the Userâs request and proper client interaction.
Monitoring & managing database growth, tablespaces, adding ,resizing and renaming the datafiles.
Restoration of database using RMAN backups for backup consistency checks.
Migration of Database using export / import and RMAN backups.
Configuring & managing Physical Standby database.
Creating database links, Tablespaces, database directories.
Managing network settings through listener.ora and tnsnames.ora files.
Restoration of data using old logical backup as per client request.
Schema replication across databases through data pump tool.
Taking cold and hot backups on scheduled times and monitoring backups
Taking EXPDP of database, database objects and a particular schema
Using SCP ticketing tool in order keeping track of client requests.Â 
Performing Maintenance Activities such as Index Rebuilding and stats gather.
Troubleshooting the Basic LevelÂ performance issuesÂ 
Setting up a new environmentÂ from database perspective within the requested timelines
Adding/Deleting disks in ASM and monitoring the ASM diskgroups.
Creating users & privileges with appropriate roles and levels of security.Â 
Database Administrator working in 24*7 support environment maintaining Databases running on Oracle 11g, 12c.
Performing database online and offline database re-organization for database enhancement.Â 
Migrating database from Non-ASM to ASM file system.
Grid up-gradation from 11g to 12C.
company - Insolutions Global Ltd
description - Oracle software installation(graphical/silent),Database upgrade,Patch upgrade.
Maintaining around 80+ UAT DB servers, 40 production DB and 28 standby/DR DB.
Managing/creating DR & standby servers, DB sync.
Backup and recovery (RMAN/ Datapump).
Performing activities like switchover and failover .
Allocating system storage and planning future storage requirements for the database system
Enrolling users and maintaining system security.
Monitoring Alert log, Snap ID generation, db size, Server space, OEM reports, User validity.
Controlling and monitoring user access to the database .
Scheduling shell scripts or dbms_jobs using Crontab or DBMS_SCHEDULER (monitoring script, listener check, backup script, AWR reports) etc.
Planning for backup and recovery of database.
Managing the production database for Oracle and SQL Server and resize the space of database/Datafiles/Tablespace/Transactional Logs.
Managing Temp and Undo tablespaces.
Creating primary database storage structures (tablespaces) after application developers have designed an application."
Database,"TECHNICAL SKILLS â¢ SQL â¢ Oracle v10, v11, v12 â¢ R programming, Python, linear regression, machine learning and statistical modelling techniques(obtained certification through Edvancer Eduventures training institute) KEY SKILLS â¢ Multitasking, working to meet client SLA in high pressure scenarios, handling sensitive clients along with improved skills at being a team player. â¢ Excellent communication skills and quick learner. â¢ Leadership qualities, team networking and courage to take up the problems proactively.Education Details 
June 2012    Sadvidya Pre-University CollegeApplication Database Administrator-DBMS (Oracle)Application Database Administrator-DBMS (Oracle) - IBM India Pvt LtdSkill Details 
CLIENTS- Exprience - 30 months
MACHINE LEARNING- Exprience - 30 months
ORACLE- Exprience - 30 months
SQL- Exprience - 30 months
EXCELLENT COMMUNICATION SKILLS- Exprience - 6 monthsCompany Details 
company - IBM India Pvt Ltd
description - Client: Blue Cross Blue Shield MA: Massachusetts Health Insurance
â¢   Used Oracle SQL to store and organize data. This includes capacity planning, installation, configuration, database
design, migration, security, troubleshooting, backup and data recovery.
â¢   Worked with client databases installed on Oracle v10, v11, v12 on a Linux platform.

â¢   Proficient communication with clients across locations facilitating data elicitation.

â¢   Handling numerous business requests and solving them diligently within the given time frame and responding quickly and effectively to production issues within SLA.

â¢   Leading a team in co ordination with business to conduct weekly checkouts of the database servers and systems

IBM Certifications
Statistics 101, Applied Data Science with R, Big Data Foundations, Data Science Foundations

Business Analytics Certification (Pune)
Worked on Retail and Banking projects, to design a predictive business model using machine learning techniques in
R programming for an efficient business and marketing strategy."
Hadoop,"Education Details 
Hadoop DeveloperHadoop Developer - INFOSYSSkill Details 
Company Details 
company - INFOSYS
description - Project Description: The banking information had stored the data in different data ware house systems for each department but it becomes difficult for the organization to manage the data and to perform some analytics on the past data, so it is combined them into a single global repository in Hadoop for analysis.

Responsibilities:
â¢       Analyze the banking rates data set.
â¢       Create specification document.
â¢       Provide effort estimation.
â¢       Develop SPARK Scala, SPARK SQL Programs using Eclipse IDE on Windows/Linux environment.
â¢       Create KPI's test scenarios, test cases, test result document.
â¢       Test the Scala programs in Linux Spark Standalone mode.
â¢       setup multi cluster on AWS, deploy the Spark Scala programs
â¢       Provided solution using Hadoop ecosystem - HDFS, MapReduce, Pig, Hive, HBase, and Zookeeper.
â¢       Provided solution using large scale server-side systems with distributed processing algorithms.
â¢       Created reports for the BI team using Sqoop to export data into HDFS and Hive.
â¢       Provided solution in supporting and assisting in troubleshooting and optimization of MapReduce jobs and
Pig Latin scripts.
â¢       Deep understanding of Hadoop design principles, cluster connectivity, security and the factors that affect
system performance.
â¢       Worked on Importing and exporting data from different databases like Oracle, Teradata into HDFS and Hive
using Sqoop, TPT and Connect Direct.
â¢       Import and export the data from RDBMS to HDFS/HBASE
â¢       Wrote script and placed it in client side so that the data moved to HDFS will be stored in temporary file and then it will start loading it in hive tables.
â¢       Developed the Sqoop scripts in order to make the interaction between Pig and MySQL Database.
â¢       Involved in developing the Hive Reports, Partitions of Hive tables.
â¢       Created and maintained technical documentation for launching HADOOP Clusters and for executing HIVE
queries and PIG scripts.
â¢       Involved in running Hadoop jobs for processing millions of records of text data

Environment: Java, Hadoop, HDFS, Map-Reduce, Pig, Hive, Sqoop, Flume, Oozie, HBase, Spark, Scala,
Linux, NoSQL, Storm, Tomcat, Putty, SVN, GitHub, IBM WebSphere v8.5.

Project #1: TELECOMMUNICATIONS
Hadoop Developer

Description To identify customers who are likely to churn and 360-degree view of the customer is created from different heterogeneous data sources. The data is brought into data lake (HDFS) from different sources and analyzed using different Hadoop tools like pig and hive.

Responsibilities:
â¢       Installed and Configured Apache Hadoop tools like Hive, Pig, HBase and Sqoop for application development and unit testing.
â¢       Wrote MapReduce jobs to discover trends in data usage by users.
â¢       Involved in database connection using SQOOP.
â¢       Involved in creating Hive tables, loading data and writing hive queries Using the HiveQL.
â¢       Involved in partitioning and joining Hive tables for Hive query optimization.
â¢       Experienced in SQL DB Migration to HDFS.
â¢       Used NoSQL(HBase) for faster performance, which maintains the data in the De-Normalized way for OLTP.
â¢       The data is collected from distributed sources into Avro models. Applied transformations and standardizations and loaded into HBase for further data processing.
â¢       Experienced in defining job flows.
â¢       Used Oozie to orchestrate the workflow.
â¢       Implemented Fair schedulers on the Job tracker to share the resources of the Cluster for the Map Reduce
jobs given by the users.
â¢       Exported the analyzed data to the relational databases using HIVE for visualization and to generate reports for the BI team.

Environment: Hadoop, Hive, Linux, MapReduce, HDFS, Hive, Python, Pig, Sqoop, Cloudera, Shell Scripting,
Java (JDK 1.6), Java 6, Oracle 10g, PL/SQL, SQL*PLUS"
Hadoop,"Skill Set: Hadoop, Map Reduce, HDFS, Hive, Sqoop, java. Duration: 2016 to 2017. Role: Hadoop Developer Rplus offers an quick, simple and powerful cloud based Solution, Demand Sense to accurately predict demand for your product in all your markets which Combines Enterprise and External Data to predict demand more accurately through Uses Social Conversation and Sentiments to derive demand and Identifies significant drivers of sale out of hordes of factors that Selects the best suited model out of multiple forecasting models for each product. Responsibilities: â¢ Involved in deploying the product for customers, gathering requirements and algorithm optimization at backend of the product. â¢ Load and transform Large Datasets of structured semi structured. â¢ Responsible to manage data coming from different sources and application â¢ Supported Map Reduce Programs those are running on the cluster â¢ Involved in creating Hive tables, loading with data and writing hive queries which will run internally in map reduce way.Education Details 
Hadoop DeveloperHadoop Developer - BraindatawireSkill Details 
APACHE HADOOP HDFS- Exprience - 49 months
APACHE HADOOP SQOOP- Exprience - 49 months
Hadoop- Exprience - 49 months
HADOOP- Exprience - 49 months
HADOOP DISTRIBUTED FILE SYSTEM- Exprience - 49 monthsCompany Details 
company - Braindatawire
description - Technical Skills:
â¢   Programming: Core Java, Map Reduce, Scala
â¢   Hadoop Tools: HDFS, Spark, Map Reduce, Sqoop, Hive, Hbase
â¢   Database: MySQL, Oracle
â¢   Scripting: Shell Scripting
â¢   IDE: Eclipse
â¢   Operating Systems: Linux (CentOS), Windows
â¢   Source Control: Git (Github)"
Hadoop,"â¢ Operating systems:-Linux- Ubuntu, Windows 2007/08 â¢ Other tools:- Tableau, SVN, Beyond Compare.Education Details 
January 2016 Bachelors of Engineering Engineering  Gujarat Technological UniversitySystems Engineer/Hadoop DeveloperSystems Engineer/Hadoop Developer - Tata Consultancy ServicesSkill Details 
Hadoop,Spark,Sqoop,Hive,Flume,Pig- Exprience - 24 monthsCompany Details 
company - Tata Consultancy Services
description - Roles and responsibility:

Working for a American pharmaceutical company (one of the world's premier
biopharmaceutical) who develops and produces medicines and vaccines for a wide range of medical
disciplines, including immunology, oncology, cardiology, endocrinology, and neurology. To handle large
amount of United Healthcare data big data analytics is used. Data from all possible data sources like records of all Patients(Old and New), records of medicines, Treatment Pathways & Patient Journey for
Health Outcomes, Patient Finder (or Rare Disease Patient Finder), etc being gathered, stored and processed at one place.

â¢     Worked on cluster with specs as:
o    Cluster Architecture: Fully
Distributed Package Used:
CDH3
o    Cluster Capacity: 20 TB
o    No. of Nodes: 10 Data Nodes + 3 Masters + NFS Backup For NN

â¢     Developed proof of concepts for enterprise adoption of Hadoop.
â¢   Used SparkAPI over Cloudera Hadoop YARN to perform analytics on the Healthcare data in Cloudera
distribution.
â¢   Responsible for cluster maintenance, adding and removing cluster nodes, cluster monitoring and trouble-shooting, manage and review data backups, and reviewing Hadoop log files.
â¢   Imported & exported large data sets of data into HDFS and vice-versa using sqoop.
â¢   Involved developing the Pig scripts and Hive Reports
â¢   Worked on Hive partition and bucketing concepts and created hive external and Internal tables with Hive
partition.Monitoring Hadoop scripts which take the input from HDFS and load the data into Hive.
â¢   Developed Spark scripts by using Scala shell commands as per the requirement and worked with both
Data frames/SQL/Data sets and RDD/MapReduce in Spark. Optimizing of existing algorithms in Hadoop
using SparkContext, Spark-SQL, Data Frames and RDD's.
â¢   Collaborated with infrastructure, network, database, application and BI to ensure data, quality and availability.
â¢   Developed reports using TABLEAU and exported data to HDFS and hive using Sqoop.
â¢   Used ORC & Parquet file formats for serialization of data, and Snappy for the compression of the data.

Achievements

â¢   Appreciation for showing articulate leadership qualities in doing work with the team.
â¢   Completed the internal certification of TCS Certified Hadoop Developer.

Ongoing Learning
â¢   Preparing and scheduled the Cloudera Certified Spark Developer CCA 175."
Hadoop,"Areas of expertise â¢ Big Data Ecosystems: Hadoop-HDFS, MapReduce, Hive, Pig, Sqoop, HBase Oozie, Spark, Pyspark, HUE and having knowledge on cassandra â¢ Programming Languages: Python, Core Java and have an idea on Scala â¢ Databases: Oracle 10g, MySQL, Sqlserver NoSQL - HBase, Cassandra â¢ Tools: Eclipse, Toad, FTP, Tectia, Putty, Autosys, Anaconda, Jupyter notebool and Devops - RTC, RLM. â¢ Scripting Languages: JSP â¢ Platforms: Windows, UnixEducation Details 
 M.Tech (IT-DBS) B.Tech (CSE)  SRM UniversitySoftware EngineerSoftware Engineer - Larsen and ToubroSkill Details 
Company Details 
company - Larsen and Toubro
description - Worked as a Software Engineer in Technosoft Corporation, Chennai from Aug 2015 to sep 2016.
company - Current Project
description - Duration: September 2016 to Till date
Vendor: Citi bank
Description:
Citibank's (Citi) Anti-Money Laundering (AML) Transaction Monitoring (TM) program is a future state solution and a rules-based system for transaction monitoring of ICG-Markets business.
Roles and Responesbilities:
â¢ Building and providing domain knowledge for Anti Money Laundering among team members.
â¢ The layered architecture has Data Warehouse and Workspace layers which are used by Business Analysts.
â¢ Actively involved in designing of star-schema model involving various Dimensions and Fact tables.
â¢ Designed SCD2 for maintaining history of the DIM data.
â¢ Developing Hive Queries for mapping data between different layers of architecture, and it's usage in Oozie Workflows.
â¢ Integration with Data Quality and Reconciliation Module.
â¢ Regression and Integration testing of solution for any issues in integration with other modules and effectively testing the data flow from layer-to-layer.
â¢ Transaction monitoring system development to generate Alerts for the suspicious and fraudulent transactions based on requirements provide by BAs.
â¢ Developing spark Jobs for various business rules.
â¢ Learning ""Machine Learning"", which will be used further in the project for developing an effective model for Fraud detection for Anti Money Laundering system.
â¢ Scheduling Jobs using Autosys tool.
â¢ Deployment and Code Management using RTC and RLM(Release Lifecycle Management)

Hadoop Developer
#  Current Project: PRTS - RAN
Environment: Hadoop 2.x, HDFS, Yarn, Hive, Sqoop, HBase, Tez, Tableau, Sqlserver, Teradata
Cluster Size: 96 Node Cluster.
Distribution: Horton works - HDP2.3
company - Alcatel lucent
description - 1X) and  Ruckus Wireless
Description:
The scope of this project is to maintain and store the operational and parameters data collected from the multiple vendors networks by the mediation team into the OMS data store and make it available for RF engineers to boost the network performance.
Responsibilities:
â¢ Working with Hadoop Distributed File System.
â¢ Involved in importing data from MySQL to HDFS using SQOOP.
â¢ Involved in creating Hive tables, loading with data and writing hive queries which will run  on top of  Tez execution Engine.
â¢ Involved in Preparing Test cases Document.
â¢ Involved in Integrating Hive and HBase to store the operational data.
â¢ Monitoring the Jobs through Oozie.
company - Current Project
description - Anti - Money laundering
Environment: Hadoop 2.x, HDFS, Yarn, Hive, Oozie, Spark, Unix, Autosys, Python, RTC, RLM, ETL Framwe work
Cluster Size: 56 Node Cluster.
Distribution: Cloudera 5.9.14"
Hadoop,"Technical Skill Set: Programming Languages Apache Hadoop, Python, shell scripting, SQL Technologies Hive, Pig, Sqoop, Flume, Oozie, Impala, hdfs Tools Dataiku, Unravel, Cloudera, Putty, HUE, Cloudera Manager, Eclipse, Resource Manager Initial Learning Program: Tata Consultancy Services: June 2015 to August 2015 Description: This is a learning program conducted by TCS for the newly joined employees, to accomplish them to learn the working standard of the organization. During this period employee are groomed with various technical as well as ethical aspects. Education Details 
 B.E. Electronics & Communication Indore, Madhya Pradesh Medi-caps Institute of Technology & ManagementHadoop developerhadoop,hive,sqoop,flume,pig,mapreduce,python,impala,spark,scala,sql,unix.Skill Details 
APACHE HADOOP SQOOP- Exprience - 31 months
Hadoop- Exprience - 31 months
HADOOP- Exprience - 31 months
Hive- Exprience - 31 months
SQOOP- Exprience - 31 months
python- Exprience - Less than 1 year months
hdfs- Exprience - Less than 1 year months
unix- Exprience - Less than 1 year months
impala- Exprience - Less than 1 year months
pig- Exprience - Less than 1 year months
unravel- Exprience - Less than 1 year months
mapreduce- Exprience - Less than 1 year months
dataiku- Exprience - Less than 1 year monthsCompany Details 
company - Tata Consultancy Services
description - Project Description
Data warehouse division has multiple products for injecting, storing, analysing and presenting data. The Data Lake program is started to provide multi-talent, secure data hub to store application's data on Hadoop platform with strong data governance, lineage, auditing and monitoring capabilities. The object of the project is to provide necessary engineering support to analytics and application teams so that they can focus on the business logic development. In this project, the major task is to set up the Hadoop cluster and govern all the activities which are required for the smooth functioning of various Hadoop ecosystems. As the day and day data increasing so to provide stability to the ecosystem and smooth working of it, Developing and automating the various requirement specific utilities.

Responsibility 1. Developed proactive Health Check utility for Data Lake. The utility proactively checks the smooth functioning of all Hadoop components on the cluster and sends the result to email in HTML format. The utility is being used for daily Health Checks as well as after upgrades.
2. Getting the data in different formats and processing the data in Hadoop ecosystem after filtering the data using the appropriate techniques.
3. Developed data pipeline utility to ingest data from RDBMS database to Hive external tables using Sqoop commands. The utility also offers the data quality check like row count validation.
4. Developed and automated various cluster health check, usage, capacity related reports using Unix shell scripting.
5. Optimization of hive queries in order to increase the performance and minimize the Hadoop resource utilizations.
6. Creating flume agents to process the data to Hadoop ecosystem side.
7. Performed benchmark testing on the Hive Queries and impala queries.
8. Involved in setting up the cluster and its components like edge node and HA implementation of the services: Hive Server2, Impala, and HDFS.
9. Filtering the required data from available data using different technologies like pig, regex Serde etc.
10. Dataiku benchmark testing on top of impala and hive in compare to Greenplum database.
11. Moving the data from Greenplum database to Hadoop side with help of Sqoop pipeline, process the data to Hadoop side and storing the data into hive tables to do the performance testing.
12. Dealing with the Hadoop ecosystem related issues in order to provide stability to WM Hadoop ecosystem.
13. Rescheduling of job from autosys job hosting to TWS job hosting for better performance.

Declaration:
I hereby declare that the above mentioned information is authentic to the best of my knowledge
company - Tata Consultancy Services
description - Clients: 1. Barclays 2. Union bank of California (UBC) 3. Morgan Stanley (MS)

KEY PROJECTS HANDLED
Project Name ABSA- Reconciliations, UBC and WMDATALAKE COE
company - Tata Consultancy Services
description - Project Description
Migration of data from RDBMS database to Hive (Hadoop ecosystem) . Hadoop platform ability with strong data governance, lineage, auditing and monitoring capabilities. The objective of this project was to speed up the data processing so that the analysis and decision making become easy. Due to RDBMS limitations to process waste amount of data at once and produce the results at the earliest, Client wanted to move the data to Hadoop ecosystem so that they can over-come from those limitations and focus on business improvement only.

Responsibility 1. Optimising the SQL queries for those data which were not required to move from RDBMS to any other platform.
2. Writing the Hive queries and logic to move the data from RDBMS to Hadoop ecosystem.
3. Writing the hive queries to analyse the required data as per the business requirements.
4. Optimization of hive queries in order to increase the performance and minimize the Hadoop resource utilizations.
5. Writing the sqoop commands and scripts to move the data from RDBMS to Hadoop side.
company - Tata Consultancy Services
description - Project Description
Create recs and migrating static setup of reconciliations from 8.1 version to 9.1 version of the environment Intellimatch.

Responsibility 1. Have worked on extracting business requirements, analyzing and implementing them in developing Recs 2. Worked on migrating static setup of reconciliations from 8.1 version to 9.1 version of the environment Intellimatch.
3. Done the back end work where most of the things were related to writing the sql queries and provide the data for the new recs.

Project Name   PSO"
Hadoop,"Technical Skills Programming Languages: C, C++, Java, .Net., J2EE, HTML5, CSS, MapReduce Scripting Languages: Javascript, Python Databases: Oracle (PL-SQL), MY-SQL, IBM DB2 Tools:IBM Rational Rose, R, Weka Operating Systems: Windows XP, Vista, UNIX, Windows 7, Red Hat 7Education Details 
January 2015 B.E  Pimpri Chinchwad, MAHARASHTRA, IN Pimpri Chinchwad College of Engineering
January 2012 Diploma MSBTE  Dnyanganaga Polytechnic
 S.S.C   New English School TakaliHadoop/Big Data DeveloperHadoop/Big Data Developer - British TelecomSkill Details 
APACHE HADOOP MAPREDUCE- Exprience - 37 months
MapReduce- Exprience - 37 months
MAPREDUCE- Exprience - 37 months
JAVA- Exprience - 32 months
.NET- Exprience - 6 monthsCompany Details 
company - British Telecom
description - Project: British Telecom project (UK)
Responsibilities:
â¢ Working on HDFS, MapReduce, Hive, Spark, Scala, Sqoop, Kerberos etc. technologies
â¢ Implemented various data mining algorithms on Spark like K-means clustering, Random forest, NaÃ¯ve bayes etc.
â¢ A knowledge of installing, configuring, maintaining and securing Hadoop.
company - DXC technology
description - HPE legacy), Bangalore
â¢ Worked on Hadoop + Java programming
â¢ Worked on Azure and AWS (EMR) services.
â¢ Worked on HDInsight Hadoop cluster..
â¢ Design, develop, document and architect Hadoop applications
â¢ Develop MapReduce coding that works seamlessly on Hadoop clusters.
â¢ Analyzing and processing the large data sets on HDFS.
â¢ An analytical bent of mind and ability to learn-unlearn-relearn surely comes in handy."
Hadoop,"Technical Skill Set Big Data Ecosystems: Hadoop, HDFS, HBase, Map Reduce, Sqoop, Hive, Pig, Spark-Core, Flume. Other Language: Scala, Core-Java, SQL, PLSQL, Sell Scripting ETL Tools: Informatica Power Center8.x/9.6, Talend 5.6 Tools: Eclipse, Intellij Idea. Platforms: Windows Family, Linux /UNIX, Cloudera. Databases: MySQL, Oracle.10/11gEducation Details 
 M.C.A  Pune, MAHARASHTRA, IN Pune UniversityHodoop DeveloperHodoop Developer - PRGX India Private Limited PuneSkill Details 
Company Details 
company - PRGX India Private Limited Pune
description - Team Size: 10+
Environment: Hive, Spark, Sqoop, Scala and Flume.

Project Description:
The bank wanted to help its customers to avail different products of the bank through analyzing their expenditure behavior. The customers spending ranges from online shopping, medical expenses in hospitals, cash transactions, and debit card usage etc. the behavior allows the bank to create an analytical report and based on which the bank used to display the product offers on the customer portal which was built using java. The portal allows the customers to login and see their transactions which they make on a day to day basis .the analytics also help the customers plan their budgets through the budget watch and my financial forecast applications embedded into the portal. The portal used hadoop framework to analyes the data as per the rules and regulations placed by the regulators from the respective countries. The offers and the interest rates also complied with the regulations and all these processing was done using the hadoop framework as big data analytics system.

Role & Responsibilities:
â Import data from legacy system to hadoop using Sqoop, flume.
â Implement the business logic to analyses  the data
â Per-process data using spark.
â Create hive script and loading data into hive.
â Sourcing various attributes to the data processing logic to retrieve the correct results.

Project 2
company - PRGX India Private Limited Pune
description - 
company - PRGX India Private Limited Pune
description - Team Size: 11+
Environment: Hadoop, HDFS, Hive, Sqoop, MySQL, Map Reduce

Project Description:-
The Purpose of this project is to store terabytes of information from the web application and extract meaningful information out of it.the solution was based on the open source s/w hadoop. The data will be stored in hadoop file system and processed using Map/Reduce jobs. Which in trun includes getting the raw html data from the micro websites, process the html to obtain product and user information, extract various reports out of the vistor tracking information and export the information for further processing

Role & Responsibilities:
â Move all crawl data flat files generated from various micro sites to HDFS for further processing.
â Sqoop implementation for interaction with database
â Write Map Reduce scripts to process the data file.
â Create hive tables to store the processed data in tabular formats.
â Reports creation from hive data.

Project 3
company - PRGX India Private Limited Pune
description - Team Size: 15+
Environment: Informatica 9.5, Oracle11g, UNIX

Project Description:
Pfizer Inc. is an American global pharmaceutical corporation headquartered in New York City. The main objective of the project is to build a Development Data Repository for Pfizer Inc. Because all the downstream application are like Etrack, TSP database, RTS, SADMS, GFS, GDO having their own sql request on the OLTP system directly due to which the performance of OLTP system goes slows down. For this we have created a Development Data Repository to replace the entire sql request directly on the OLTP system. DDR process extracts all clinical, pre-clinical, study, product, subject, sites related information from the upstream applications like EPECS, CDSS, RCM, PRC, E-CLINICAL, EDH and after applying some business logic put it into DDR core tables. From these snapshot and dimensional layer are created which are used for reporting application.

Role & Responsibilities:
â To understand & analyze the requirement documents and resolve the queries.
â To design Informatica mappings by using various basic transformations like Filter, Router, Source qualifier, Lookup etc and advance transformations like Aggregators, Joiner, Sorters and so on.
â Perform cross Unit and Integration testing for mappings developed within the team. Reporting bugs and bug fixing.
â Create workflow/batches and set the session dependencies.
â Implemented Change Data Capture using mapping parameters, SCD and SK generation.
â Developed Mapplet, reusable transformations to populate the data into data warehouse.
â Created Sessions & Worklets using workflow Manager to load the data into the Target Database.
â Involved in Unit Case Testing (UTC)
â Performing Unit Testing and UAT for SCD Type1/Type2, fact load and CDC implementation.

Personal Scan

Address: Jijayi Heights, Flat no 118, Narhe, (Police chowki) Pune- 411041"
ETL Developer,"Technical Summary â¢ Knowledge of Informatica Power Center (ver. 9.1 and 10) ETL Tool: Mapping designing, usage of multiple transformations. Integration of various data source like SQL Server tables, Flat Files, etc. into target data warehouse. â¢ SQL/PLSQL working knowledge on Microsoft SQL server 2010. â¢ Unix Work Description: shell scripting, error debugging. â¢ Job scheduling using Autosys, Incident management and Change Requests through Service Now, JIRA, Agile Central â¢ Basic knowledge of Intellimatch (Reconciliation tool) Education Details 
January 2010 to January 2014 BTech CSE Sangli, Maharashtra Walchand College of Engineering
October 2009 H.S.C  Sangli, Maharashtra Willingdon College
August 2007 S.S.C Achievements Sangli, Maharashtra Martin's English SchoolETL DeveloperIT AnalystSkill Details 
ETL- Exprience - 48 months
EXTRACT, TRANSFORM, AND LOAD- Exprience - 48 months
INFORMATICA- Exprience - 48 months
MS SQL SERVER- Exprience - 48 months
RECONCILIATION- Exprience - 48 months
Jira- Exprience - 36 monthsCompany Details 
company - Tata Consultancy Services
description - Project Details
Client/Project: Barclays UK London/HEXAD
Environment: Informatica (Power Center), SQL Server, UNIX, Autosys, Intellimatch.

Project Description:
The objective is to implement a strategic technical solution to support the governance and monitoring of break standards - including enhancements to audit capabilities. As a part of this program, the required remediation of source system data feeds involves consolidation of data into standardized feeds.

These remediated data feeds will be consumed by ETL layer. The reconciliation tool is
designed to source data from an ETL layer. The data from the Front and Back office systems,
together with static data must therefore be delivered to ETL. Here it will be pre-processed and delivered to reconciliation tool before the reconciliation process can be performed.

Role and Responsibilities:
â¢ Responsible for analyzing, designing and developing ETL strategies and processes,
writing ETL specifications
â¢ Requirement gathering
â¢ Making functional documents and low level documents
â¢ Developing and debugging the Informatica mappings to resolve bugs, and identify the causes of failures
â¢ User interaction to identify the issues with the data loaded through the application
â¢ Developed mappings using different transformations
company - Tata Consultancy Services
description - Project Details
Client/Project: Barclays UK London/HEXAD
Environment: Informatica (Power Center), SQL Server, UNIX, Autosys, Intellimatch.

Project Description:
The objective is to implement a strategic technical solution to support the governance and monitoring of break standards - including enhancements to audit capabilities. As a part of this program, the required remediation of source system data feeds involves consolidation of data into standardized feeds.

These remediated data feeds will be consumed by ETL layer. The reconciliation tool is
designed to source data from an ETL layer. The data from the Front and Back office systems,
together with static data must therefore be delivered to ETL. Here it will be pre-processed and delivered to reconciliation tool before the reconciliation process can be performed.

Role and Responsibilities:
â¢ Responsible for analyzing, designing and developing ETL strategies and processes,
writing ETL specifications
â¢ Requirement gathering
â¢ Making functional documents and low level documents
â¢ Developing and debugging the Informatica mappings to resolve bugs, and identify the causes of failures
â¢ User interaction to identify the issues with the data loaded through the application
â¢ Developed mappings using different transformations"
ETL Developer,"TechnicalProficiencies DB: Oracle 11g Domains: Investment Banking, Advertising, Insurance. Programming Skills: SQL, PLSQL BI Tools: Informatica 9.1 OS: Windows, Unix Professional Development Trainings â¢ Concepts in Data Warehousing, Business Intelligence, ETL. â¢ BI Tools -Informatica 9X Education Details 
 BCA  Nanded, Maharashtra Nanded UniversityETL DeveloperETL Developer - Sun Trust Bank NYSkill Details 
ETL- Exprience - 39 months
EXTRACT, TRANSFORM, AND LOAD- Exprience - 39 months
INFORMATICA- Exprience - 39 months
ORACLE- Exprience - 39 months
UNIX- Exprience - 39 monthsCompany Details 
company - Sun Trust Bank NY
description - Sun Trust Bank, NY JAN 2018 to present
Client: Sun Trust Bank NY
Environment: Informatica Power Center 9.1, Oracle 11g, unix.

Role: ETL Developer

Project Profile:
Sun Trust Bank is a US based multinational financial services holding company, headquarters in NY that operates the Bank in New York and other financial services investments. The company is organized as a stock corporation with four divisions: investment banking, private banking, Retail banking and a shared services group that provides
Financial services and support to the other divisions.
The objective of the first module was to create a DR system for the bank with a central point of communication and storage for Listed, Cash securities, Loans, Bonds, Notes, Equities, Rates, Commodities, and
FX asset classes.
Contribution / Highlights:

â¢ Liaising closely with Project Manager, Business Analysts, Product Architects, and Requirements Modelers (CFOC) to define Technical requirements and create project documentation.
â¢ Development using Infa 9.1, 11g/Oracle, UNIX.
â¢ Use Informatica PowerCenter for extraction, transformation and loading (ETL) of data in the Database.
â¢ Created and configured Sessions in Informatica workflow Manager for loading data into Data base tables from various heterogeneous database sources like Flat Files, Oracle etc.
â¢ Unit testing and system integration testing of the developed mappings.
â¢ Providing production Support of the deployed code.
â¢ Providing solutions to the business for the Production issues.
â¢ Had one to One interaction with the client throughout the project and in daily meetings.

Project #2
company - Marshall Multimedia
description - JUN 2016 to DEC 2017

Client: Marshall Multimedia
Environment: Informatica Power Center 9.1, Oracle 11g, unix.

Role: ETL Developer

Project Profile:
Marshall Multimedia is a US based multimedia advertisement services based organization which has
head courter in New York. EGC interface systems are advert management, Customer Management, Billing and
Provisioning Systems for Consumer& Enterprise Customers.
The main aim of the project was to create an enterprise data warehouse which would suffice the need of reports belonging to the following categories: Financial reports, management reports and
rejection reports. The professional reports were created by Cognos and ETL work was performed by
Informatica. This project is to load the advert details and magazine details coming in Relational tables into data warehouse and calculate the compensation and incentive amount monthly twice as per business
rules.

Contribution / Highlights:
â¢ Developed mappings using different sources by using Informatica transformations.
â¢ Created and configured Sessions in Informatica workflow Manager for loading data into Data Mart tables from various heterogeneous database sources like Flat Files, Oracle etc.

2
â¢ Unit testing and system integration testing of the developed mappings.
â¢ Providing solutions to the business for the Production issues.

Project #3
company - Assurant healthcare/Insurance Miami USA
description - Assurant, USA                                                                                                    NOV 2015 to MAY 2016

Project: ACT BI - State Datamart
Client: Assurant healthcare/Insurance Miami USA
Environment: Informatica Power Center 9.1, Oracle 11g, unix.

Role: ETL Developer

Project Profile:
Assurant, Inc. is a holding company with businesses that provide a diverse set of specialty, niche-market insurance
products in the property, casualty, life and health insurance sectors. The company's four operating segments are Assurant
Employee Benefits, Assurant Health, Assurant Solutions and Assurant Specialty Property.
The project aim at building State Datamart for enterprise solution. I am part of team which is responsible for ETL
Design & development along with testing.

Contribution / Highlights:
â¢   Performed small enhancement
â¢   Daily load monitoring
â¢   Attend to Informatica job failures by analyzing the root cause, resolving the failure using standard
documented process.
â¢   Experience in writing SQL statements.
â¢   Strong Problem Analysis & Resolution skills and ability to work in Multi Platform Environments
â¢   Scheduled the Informatica jobs using Informatica scheduler
â¢   Extensively used ETL methodology for developing and supporting data extraction, transformations and loading process, in a corporate-wide-ETL Solution using Informatica.
â¢   Involved in creating the Unit cases and uploaded in to Quality Center for Unit Testing and UTR
â¢   Ensure that daily support tasks are done in accordance with the defined SLA."
ETL Developer,"Education Details 
January 2015 Bachelor of Engineering EXTC Mumbai, Maharashtra Mumbai University
January 2012 Diploma Industrial Electronics Vashi, MAHARASHTRA, IN Fr. Agnel PolytechnicETL DeveloperETL DeveloperSkill Details 
informatica- Exprience - 36 monthsCompany Details 
company - Blue Shield of California
description - Duration: (Mar 2016 - Sept 2017)

Description:
Blue Shield of California (BSC) is health plan provider. The intent of this project is to process feeds coming in and going out of BSC system related to eligibility, enrollment, and claims subject areas. All these feeds comes in different formats and are processed using Informatica 9.6.1, Oracle 11g, Facets 5.0 &Tidal.

Technical environment: ETL tool (Informatica power Center 9.6.1), Oracle 11g (SQL, PL-SQL), UNIX, Facets, Tidal, JIRA, Putty.

Role: ETL Developer
Responsibilities: â¢ Responsible for analyzing the business requirement document â¢ Involved in development of Informatica mappings using different transformations like source qualifier, expression, filter, router, joiner, union, aggregator, normalizer, sorter, lookup and its corresponding sessions and workflows.
â¢ Extensively used Informatica Debugger to figure out the problems in mapping and involved in troubleshooting the existing bugs.
â¢ Writing Unix Scripts & SQL's as per the business requirement.
â¢ Impact analysis of change requests & their development.
â¢ Data fabrication using Facets screens as well as SQL statements in membership domain.
â¢ Unit testing & trouble shooting using Informatica debugger, SQL query & preparation of Unit Test Cases.
â¢ Prepare documents for design, unit testing and impact analysis.

Awards & Achievements â¢ Received Kudos Award at Syntel for contribution in error free work, commitment towards learning, client appreciation and outstanding display of Syntel values Received appreciation from Management for outstanding performance in complete tenure.
â¢ Received spot recognition for automation done in project."
ETL Developer,"SKILL SET â Talend Big Data â Informatica Power center â Microsoft SQL Server â SQL Platform 6.2.1 Management Studio Workbench â AWS Services â Talend Administration Console â Microsoft Visual â Redshift (TAC) Studio â Athena â Data Warehouse Concept - Star â SQL â S3 Schema, Facts, Dimensions â Data Modeling - â Data Integration Microsoft Access Education Details 
January 2012 to January 2016 BE  Mumbai, Maharashtra University of Mumbai
January 2012 CBSE Technology Kochi, Kerala St. FrancisTalend ETL DeveloperTalend ETL Developer - Tata Consultancy ServicesSkill Details 
DATA WAREHOUSE- Exprience - 23 months
DATABASE- Exprience - 20 months
INTEGRATION- Exprience - 20 months
INTEGRATOR- Exprience - 20 months
MS SQL SERVER- Exprience - 20 monthsCompany Details 
company - Tata Consultancy Services
description - Prepared ETL mapping Documents for every mapping and Data Migration document for smooth transfer of project from development to testing environment and then to production environment. Performed Unit testing and System testing to
validate data loads in the target. Troubleshoot long running jobs and fixed the issues.
â¢ Expertise in creating mappings in TALEND using Big Data supporting components such as tJDBCConfiguration, tJDBCInput,
tHDFSConfiguration, tS3configuration, tCacheOut, tCacheIn, tSqlRow and standard components like tFileInputDelimited,
tFileOutputDelimited, tMap, tJoin, tReplicate, tParallelize, tConvertType, tAggregate, tSortRow, tFlowMeter, tLogCatcher,
tRowGenerator, tJava, tJavarow, tAggregateRow, tFilter etc.
â¢ Used ETL methodologies and best practices to create Talend ETL jobs. Followed and enhanced programming and naming
standards. Developed jobs, components and Joblets in Talend. Used tRunJob component to run child job from a parent job and to pass parameters from parent to child job.
â¢ Created and deployed physical objects including custom tables, custom views, stored procedures, and indexes to SQL server for Staging and Data-Warehouse environment. Involved in writing SQL Queries and used Joins to access data from MySQL.
â¢ Created and managed Source to Target mapping documents for all Facts and Dimension tables. Broad design, development and testing experience with Talend Integration Suite and knowledge in Performance Tuning of mappings.
â¢ Extensively used tMap component which does lookup & Joiner Functions. Experienced in writing expressions within tmap as per the business need. Handled insert and update Strategy using tSQLRow.
â¢ Created Implicit, local and global Context variables in the job to run Talend jobs against different environments.
â¢ Worked on Talend Administration Console (TAC) for scheduling jobs and adding users. Experienced in Building a Talend job outside of a Talend studio as well as on TAC server.
â¢ Developed mappings to load Fact and Dimension tables, SCD Type 1 and SCD Type 2 dimensions and Incremental loading and unit tested the mappings.
â¢ Developed Framework Integrated Job which schedules multiple jobs at a time and updates the last successful run time,
success status, sending mail for failed jobs, maintaining the counts in SQL Database. Used tParalleize component and multi
thread execution option to run subjobs in parallel which increases the performance of a job.
â¢ Created Talend jobs to copy the files from one server to another and utilized Talend FTP components. Implemented FTP
operations using Talend Studio to transfer files in between network folders as well as to FTP server using components like tFileList, tS3Put, tFTPut, tFileExist, tFTPConnection etc.
â¢ Extracted data from flat files/ databases applied business logic to load them in the staging database as well as flat files.
â¢ Successfully Loaded Data into different targets from various source systems like SQL Database, DB2, Flatfiles, XML files etc into the Staging table and then to the target database.
company - Tata Consultancy Services
description - Experience in development and design of ETL (Extract, Transform and Loading data) methodology for supporting data
transformations and processing, in a corporate wide ETL Solution using TALEND Big Data Platform.
â¢   Excellent working experience in Agile methodologies.
â¢   Proficiency in gathering and understanding the client requirements and translate business needs into technical
requirements.
â¢   Design and develop end-to-end ETL process from various source systems to Staging area, from staging to Data Warehouse,
soliciting and documenting business, functional and data requirements, context/variable diagrams, use cases and ETL
related diagrams.
â¢   Excellent oral/written communication with ability to effectively work with onsite and remote teams.
â¢   A good team player with excellent problem solving ability and time management skills having profound insight to determine
priorities, schedule work and meet critical deadlines.
company - Tata Consultancy Services
description - Prepared ETL mapping Documents for every mapping and Data Migration document for smooth transfer of project from development to testing environment and then to production environment. Performed Unit testing and System testing to
validate data loads in the target. Troubleshoot long running jobs and fixed the issues."
ETL Developer,"Computer skills: - Yes. SQL knowledge-yes Unix knowledge-yes Data warehouse knowledge-yes Ab intio -yee MY HOBBIES: - â¢ Playing Cricket, football. â¢ Reading books â¢ Visiting new places/Travelling. DECLARATION:- I hereby declare that the above mentioned information is factual and correct up to the best of my knowledge and belief. Date: -.27.01.2019 MR. MANISH PRABHAKAR PATIL Place: -MUMBAI Education Details 
June 2014 to June 2015 Bachelor's Electronics and Telecommunication  A C Patil college of Engineering
January 2009 to January 2011  Engineering Navi Mumbai, Maharashtra Bharati vidyapeeth
January 2008 H.S.C.  Mumbai, Maharashtra Khalsa collegeETL Informatica DeveloperETL DEVELOPERSkill Details 
ETL- Exprience - Less than 1 year months
Data Warehouse- Exprience - Less than 1 year months
Datastage- Exprience - Less than 1 year monthsCompany Details 
company - Reliance Infocomm
description - I havevbeen working as ETL Developer in reliance industries in India for the past 3years.I have very good knowledge of Informatica and SQL as well as good knowledge of Unix.I am willing to work in yours company as Developer."
DotNet Developer,"TECHNICAL SKILLS â¢ Web Technologies: ASP .NET, HTML, CSS, Jquery. â¢ Languages: C, C++, C#.NET, MVC 5. â¢ Database: SQL SERVER 2005/2008/2016. â¢ Reporting Tools.: Kindo UI, Telerik functions, Crystal Report. â¢ Platforms: Visual Stadio 2010/2014. Education Details 
January 2014 B.E  Satara, Maharashtra L.N.B.C.College of Engg Satara.
January 2011 Diploma Thergaon Pune, Maharashtra M.M.PolytechnicDot net developerSkill Details 
.NET- Exprience - 24 months
ASP- Exprience - 24 months
C#- Exprience - 24 months
C++- Exprience - 6 months
CRYSTAL REPORT- Exprience - 6 months
Html- Exprience - Less than 1 year months
Css- Exprience - Less than 1 year months
Entityframewok,jquery,javascript- Exprience - Less than 1 year months
Mvc- Exprience - 6 months
Sql- Exprience - 24 monthsCompany Details 
company - Corecode technology
description - Worked on web application using asp,c#,mvc
as well as sql for database and also bootstrap,css,html for designing.created reports using kindo and telerik controls
company - Inetsoft solution
description - Created web application using asp and c# and also used sql for database."
DotNet Developer,"Participated in intra college cricket competition and various other sports events. Group dance in college cultural programme Education Details 
 Msc Computer Science Pune, Maharashtra Pune University
 Bsc Computer Science Pune, Maharashtra Pune University
 HSC Semi-English Pune, Maharashtra Maharashatra Board
 SSC Semi-English Pune, Maharashtra Maharashatra BoardDOT NET DeveloperDot Net DeveloperSkill Details 
Asp.Net,C#, HTML, CSS, SQL- Exprience - 6 months
Javascript- Exprience - Less than 1 year months
sql server 2008/2012- Exprience - Less than 1 year monthsCompany Details 
company - 
description - "
DotNet Developer,"Technical Skills â¢ Languages: C#, ASP .NET MVC, HTML, CSS, JavaScript, AngularJs â¢ Primary Skill: Entity Framework. â¢ Tools Used: SQL Server 14, Visual Studio 13. Project Details: 1.Project Name: Transport Management System Role: Dot Net Developer Platform Used: MVC, AngularJs, SQL Server Description: This project is about the Transport Management System. This project is used to keeps all the record of the Vehicle, Customer, Employee. Reduce costs with centralized planning and execution of logistics activities. Vehicle owner can add his vehicle for rent of the specific day into the application from their location. Admin can easily access the data of Vehicle, Customer & Employee. Responsibilities: Used 3-tier architecture for presentation layer, the Business and Data Access Layers and were coded using C# as per user Requirements. Make changes in the project with discussing the group for new requirement. Work on Customer and Vehicle model. 2.Project Name: CRM Role: Dot Net Developer Platform Used: MVC, SQL Server Description: It's kind of CRM Application where a training institute can easily track their student data. where we have different user store or access and utilise to manage this data with application. Any user can easily fill the information of the leads comes in that institute also who joined or Convert that leads as a student in that institute. All information can store or easily manage this application also the good leads or the student who didn't join but their records are store in this application so this application help institute to fetch all the contacts information or those leads or student who didn't join that time. We can later contact to those leads in future. Also with the application we get to know each and every leads or student who joined Responsibilities: Used 3-tier architecture for presentation layer, the Business and Data Access Layers and were coded using C# as per user Requirements. Make changes in the project with discussing the group for new requirement. Work on Enquiry model. Key Strength: â¢ Adaptability. â¢ Hard Worker. â¢ Self Motivated. â¢ Positive Attitude.Education Details 
January 2008 HSC   Maharashtra Board
January 2006 SSC   Maharashtra BoardDot Net DeveloperDot Net Developer - Glyphisoft TechnologySkill Details 
ASP- Exprience - 14 months
DOT- Exprience - 14 months
MODEL VIEW CONTROLLER- Exprience - 14 months
MODEL-VIEW-CONTROLLER- Exprience - 14 months
MVC- Exprience - 14 monthsCompany Details 
company - Glyphisoft Technology
description - Having around 1.1+ Years of experience in development in Asp.net MVC
â¢   Currently associated with Glyphisoft Technology Solution as .net Developer."
DotNet Developer,"Education Details 
January 2014 B.com Education Details Pune, Maharashtra University Of Pune
January 2010 B.Com  Pune, Maharashtra University Of PuneSoftware DeveloperDot Net Developer with 3 years experience.Skill Details 
C,c#,asp.net,jquery,ajax,web api- Exprience - Less than 1 year months
Sql- Exprience - Less than 1 year monthsCompany Details 
company - Technology - Dot Net
description - â¢ Previous Experience worked in SepSoft ERP Solutions From 1-Jan 2016 To 24th Dec 2016.

â¢ Designation - Software Developer.
â¢ Technology - Dot Net
company - Sepsoft Erp Solution
description - Worked at Sepsoft Erp Solution as Dot Net Developer."
DotNet Developer,"Technologies ASP.NET, MVC 3.0/4.0/5.0, Unit Testing, Entity Framework 6.0 Languages C#.NET JS Library JavaScript, JQuery, Angular1.5, Typescript. Tools Visual Studio 2008/2010/2012/2015, Visual Studio Code (IDE), .NET Framework 2.0, 3.5, 4.0, 4.5 Databases SQL Server 2005, 2008, 2014, 2015 Source control tools Tortoise SVN, Git,Education Details 
January 2010 B.Tech  Kakinada, Andhra Pradesh JNTUDot Net DeveloperDot Net Developer - The Tie BarSkill Details 
.NET- Exprience - 34 months
ASP- Exprience - 34 months
ASP.NET- Exprience - 34 months
C#- Exprience - 34 months
JAVASCRIPT- Exprience - 34 monthsCompany Details 
company - The Tie Bar
description - Duration: January 2017 to Till Date

Responsibility:
â¢ Design and coding in a .NET server-side environment, specifically utilising the Microsoft MVC framework and client side environment using Knockout JS.
â¢ Consuming Web API and WCF Rest full services.
â¢ Designed and coded application components in an agile environment utilizing a test driven development approach.
â¢ Created and maintained project tasks and schedules.
â¢ Provided programming estimates, identified potential problems and recommended alternative solutions.
â¢ Worked in close cooperation with project managers and other functional team members to form a team effort in development.
â¢ Utilized pair programming approach to ensure high quality code.

Projects:
Title: THE TIEBAR
Environment: Visual Studio 2015, MVC 5.0, C#, JavaScript, JSON, LINQ, HTML 5
Database: SQL Server 2015

Role: Developer
Description:
The Tie Bar is US based online shopping website; it is the one-stop destination for luxury menswear with premium dress shirts, ties, bow ties and more,

Title: Leave Management System
Environment: Visual Studio 2010, ASP.Net, C#.Net, JavaScript, JQuery
Database: SQL Server 2008

Role: Developer
Description:
A Leave Management System is a web application having two module one is front end and second is admin module. Admin user can manage the site content in this application. The main functionality of this web application is used for maintaining the employee leave policy in a company

Title: Around Pune (CMS)
Environment: Visual Studio 2010, ASP.Net, C#.Net, JavaScript, JQuery
Database: SQL Server 2008

Role: Developer
Description:
Around Pune is a web application. In that there are two modules one is admin module and another is client or front end. Admin module controls the site date or its cms (content management system) project. The purpose of this site is to show or provide the details of all the spots and tourists places around the Pune.

Title: Country Side
Environment: Visual Studio 2008, ASP.Net, C#.Net, JavaScript, JQuery
Database: SQL Server 2008
Role: Developer
Description:
This is cms (content management system) website controlling by admin user. In that, there is one admin module to maintain website content. This site is used to provide information about tourist places, and provide offers, Different packages for trips.
company - Hamsa Hitech Pvt. Ltd
description - Responsibility:
â¢ Design and coding in a .NET server-side environment, specifically utilising the Asp.Net and C#.Net.
â¢ Design and implement systems that use relational databases, specifically SQL 2008.

Project:
Title: A G JOHNSON
Environment: Visual Studio 2010, ASP.Net, C#.Net, JavaScript, JQuery,
Database: SQL Server 2008
Role: Developer
Description:
A G JOHNSON is a global employment website. The main purpose of this application is to recruit employees for their clients in different countries like Switzerland, Norway and Sweden and it provide services like Organisational Development, Management and Leadership Support and Development, Legal Support

Title: CRM for Company
Environment: Visual Studio 2008, ASP.Net 3.0 using C#.Net, JavaScript, JQuery,
Database: SQL Server 2008
Role: Developer
Description:
Customer relationship management System is a web application. Which is maintaining the relation with company?  We can view the Project List, Client List, Notification List, Appointment List, Conversation List, Client Person List and Company Person."
DotNet Developer,"Technical Skills CATEGORY SKILLS Language C, C#, OOP, Dot Net Technologies ASP.Net MVC, ADO.Net, Entity Framework, LINQ Web Technologies HTML, CSS Browser Scripting JavaScript, Jquery, Ajax, JSON Web Browser Internet Explorer 8.0, Mozilla Firefox10 Front-end-framework Bootstrap, Kendo UI Database SQL Server 2012 Development Tools Visual Studio 2013 Operating Systems MS Window 2007 Project Details Projects Worked On: 1. Project Name: Politician Website Role: Trainee Project Description: I developed Politician website. In this website there is Home Page, Quick Facts, Category, Biography, Gallery, and ContactUs Page. It is a totally dynamic website. Environment: Operating System: Windows 7 Development Tools: Visual Studio 2013 Database Server: SQL Server 2012 Technology: .NET Framework 4.5, ASP.Net MVC5 Presentation Layer: HTML, CSS, Jquery, Bootstrap Role and Responsibilities: â¢ Understanding requirements, Coding and unit testing. 2. Project Name: Coaching Management System (CMS) Project Description: This software is helps to manage institute (Student, Teacher, Courses, Batches, Fees, Attendance, and Marks) . Provide easy reports like outstanding Fees, Marks, and Attendance etc. This software is specially designed for coaching institute. Environment: Operating System: Windows 7 Database Server: SQL Server 2012 Technology: .NET Framework 4.5, ASP.Net MVC5 Presentation Layer: HTML, CSS, JavaScript, Jquery, Bootstrap Features: â¢ Project includes the feature of testing type and the activity on the project activities. â¢ Project includes the online management and test process. Role and Responsibilities: â¢ Worked in Development of Back-end Like Data Collections. â¢ Database Schema Designing, and Create Database Dictionary & Made Database Constraints. â¢ I was also worked on Front-end Designing like Screen Generations, form Designing & Applying client side Validation. â¢ SRS documentation and Development. 3. Project Name: Hospital Management System (HMS) Project Description: Hospital Management System is powerful, flexible, and easy to use and designed and developed to deliver real conceivable benefits to hospitals and clinics and more importantly it is backed by reliable and dependable. Hospital Management System is designed for multispecialty hospitals, to cover a wide range of hospital administration and management processes. It is an integrated end-to-end Hospital Management System that provides relevant information across the hospital to support effective decision making for patient care, hospital administration and critical financial accounting, in a seamless flow. Environment: Presentation Layer: HTML, CSS, JavaScript, Jquery, Kendo UI Business Layer: .NET Framework 4.5, ASP.Net MVC5 Database Layer: SQL Server 2012 Operating System: Windows 7 Features: â¢ Project includes the feature of testing type and the activity on the project activities. â¢ Project includes the online management and test process. Role and Responsibilities: â¢ Worked in Development of Back-end Like Data Collections. â¢ Database Schema Designing, and Create Database Dictionary & Made Database Constraints. â¢ I was also worked on Front-end Designing like Screen Generations, form Designing & Applying client side Validation. â¢ SRS documentation and Development. 4. Project Name: Ticket Booking Software (BESS) Project Description: Online Ticket Booking System is a Web based application. You can directly book the ticket by this application. This application is implemented for Bus, Tour and Travel Agency. Environment: Presentation Layer: HTML, CSS, JavaScript, Jquery, KendoUI Business Layer: .NET Framework 4.5, ASP.Net MVC5 Database Layer: SQL Server 2012 Operating System: Windows 7 Features: â¢ Project includes the feature of testing type and the activity on the project activities. â¢ Project includes the online management and test process. Role and Responsibilities: â¢ Worked in Development of Back-end Like Data Collections. â¢ Database Schema Designing, and Create Database Dictionary & Made Database Constraints. â¢ I was also worked on Front-end Designing like Screen Generations, form Designing & Applying client side Validation. â¢ SRS documentation and Development. 5. Project Name: School Management System (SMS) Project Description: School Management System is complete school management software designed to automate a school's diverse operations from classes, exams to school events calendar. This school software has a powerful online community to bring parents, teachers and student on a common interactive platform. It is paperless office automation solution for today's modern schools. The School Management System provides the facility to carry out all day to day activities of the school, making them fast, easy, efficient and accurate. Environment: Presentation Layer: HTML, CSS, JavaScript, Jquery Business Layer: .NET Framework 4.5, ASP.Net MVC5 Database Layer: SQL Server 2012 Operating System: Windows 7 Features: â¢ Project includes the feature of testing type and the activity on the project activities. â¢ Project includes the online management and test process. Role and Responsibilities: â¢ Worked in Development of Back-end Like Data Collections. â¢ Database Schema Designing, and Create Database Dictionary & Made Database Constraints. â¢ I was also worked on Front-end Designing like Screen Generations, form Designing & Applying client side Validation. â¢ SRS documentation and Development. 6. Project Name: CREDIT MONITORING application Using the CREDIT MONITORING application, RMs, UHs, Credit Controllers and CMUs are able to monitor and manage the following items online, update the regularization dates and add their comments as well as generate reports: - Overdrafts - Past Dues - SBLC / FG Expiry - Mortgage Expiry - Cheques Returns & Failed Direct Debit - NBF vs. CB Classification Relationship Manager The relationship manager can only update the Regularization Date and the RM Comments. All other fields will be disabled (greyed-out) for the relationship manager. On updating the Regularization Date and the RM Comments, a successful message will be displayed to the RM and then the RM will be navigated to the Dashboard screen again. Credit Controller (CCU) The credit controller can only update the CCU Comments, if and only if the Regularization Date and RM Comments are present. Otherwise, the CCU Comments field will be disabled (greyed-out) . All other fields will also be disabled (greyed-out) for the CCU. On updating the CCU Comments, a successful message will be displayed to the CCU and then the CCU will be navigated to the Dashboard screen. CMU The CMU can only update the CMU comments, if and only if the CCU Comments are present. Otherwise, the CMU Comments field should be disabled (greyed-out) . All other fields will be disabled (greyed-out) for the CMU. On updating the CMU Comments, a successful message will be displayed to the CMU and then the CMU will be navigated to the Dashboard screen. UH The UH can only update the UH comments All other fields will be disabled (greyed-out) for the UH. On updating the UH Comments, a successful message will be displayed to the UH and then the UH will be navigated to the Dashboard screen. Environment: Presentation Layer: HTML, CSS, JavaScript, Jquery Business Layer: .NET Framework 4.5, ASP.Net MVC5 Database Layer: SQL Server 2012 Operating System: Windows 7 Role and Responsibilities: â¢ Understanding requirements, Coding and unit testing. â¢ SRS documentation and Development. Education Details 
 MCM  Pune, Maharashtra Pune University
 B.Sc. Computer Science Indore, Madhya Pradesh DAVV UniversityDesignation - Dot Net DeveloperDot Net DeveloperSkill Details 
Entity Framework- Exprience - Less than 1 year months
C#- Exprience - Less than 1 year months
Asp.Net- Exprience - Less than 1 year months
Javascript- Exprience - Less than 1 year months
OOP- Exprience - Less than 1 year months
ASP.Net MVC- Exprience - Less than 1 year months
CSS- Exprience - Less than 1 year months
Ajax- Exprience - Less than 1 year months
SQL- Exprience - Less than 1 year months
Jquery- Exprience - Less than 1 year months
HTML- Exprience - Less than 1 year months
JSON- Exprience - Less than 1 year monthsCompany Details 
company - Cognizant Technology Solutions
description - I am working as a dot net developer.
company - Ni-Lux Software Ltd.
description - I was worked as a dot net developer.
company - Ni-lux Software Ltd
description - "
DotNet Developer,"TECHNICAL SKILLS â Programming Languages: C#.NET â Web Technologies: ASP.NET MVC, Web API, Angular, JQuery, HTML5, CSS3, Bootstrap. â Database: SQL Server 2014 â IDE: Visual Studio 2015, Visual Studio Code â Office Packages: MS-Office (Access, Word, Excel) Education Details 
January 2016 B.Sc.(Computer Science)  Solapur, Maharashtra Solapur UniversityDOT NET DeveloperDOT NET Developer - Navshar Global SoftwareSkill Details 
.NET- Exprience - 15 months
ASP- Exprience - 15 months
ASP.NET- Exprience - 15 months
BOOTSTRAP- Exprience - 15 months
jQuery- Exprience - 15 months
ASP.NET MVC- Exprience - Less than 1 year months
Angularjs- Exprience - Less than 1 year months
C#- Exprience - Less than 1 year months
Entity Framework- Exprience - Less than 1 year monthsCompany Details 
company - Navshar Global Software
description - 017.

PROJECTS WORKED ON
1. Navnirman Tech Festival
â Navnirman Tech Festival is a technical event management application that is used to scheduling and organizing event.
â Technologies: ASP.NET MVC, SQL Server, Entity Framework, AJAX, JQuery
â Role: Involved Everywhere

2. Like Security
â The Like Security web application provides trusted services and man power as well labour management.
â Technologies: ASP.NET MVC, SQL Server, Bootstrap, AJAX, JQuery
â Role: Involved Everywhere

3. Performance Review (In House Project)
â This is an In-House Web application which is used to fill the review for particular Employee by other employees who worked with him. This application is very useful to take decision in appraisal of employee. All the submitted review can be downloaded in PDF format as per selection.
â Technologies: ASP.NET MVC, Entity Framework, SQL Server, AJAX, JQuery
â Role: Involved Everywhere
company - None
description - None"
Blockchain,"Hobbies â¢ Playing Chess â¢ Solving Rubik's Cube â¢ Watching Series Languages â¢ English â¢ Hindi â¢ Marathi Education Details 
January 2014 to January 2017 Bachelorâs Degree Information Technology, First Class Pune, Maharashtra JSPMâs Jayawantrao Sawant College of Engineering
January 2010 to January 2014 Diploma Information Technology, First Class Nashik, Maharashtra K. K. Wagh Polytechnic
January 2010 SSC, First Class  Nashik, Maharashtra New Era English SchoolBlockchain DeveloperBlockchain Developer - Corpcloud Global Services Pvt. LtdSkill Details 
Blockchain- Exprience - 6 months
Smart Contracts- Exprience - 6 months
DApps- Exprience - 6 months
MEAN Stack- Exprience - 12 monthsCompany Details 
company - Corpcloud Global Services Pvt. Ltd.
description - â¢ Worked productively with the Team to identify requirements and proposed ideas on enhancing the product.
â¢ Developing and managing userâs Blockchain account wallets and transactions.
â¢ Regularly monitoring smooth executions of Blockchain transactions and wallet functions along with identifying and correcting possible errors.
â¢ Writing smart contracts, their APIâs and documenting them.
company - Corpcloud Global Services Pvt. Ltd.
description - â¢ Identifying complex bugs in the system and resolving them.
â¢ Implemented and updated application modules under the direction of Seniors.
â¢ Effectively coded required changes and alterations and checked in into repository using Bit Bucket.
â¢ Performed code check-ins and check-outs regularly and worked with APIâs."
Blockchain,"Skills Strong CS fundamentals and problem solving Ethereum, Smart Contracts, Solidity skills Golang, Node, Angular, React Culturally fit for startup environment MongoDB, PostGresql, MySql Enthusiastic to learn new technologies AWS, Docker, Microservices Blockchain, Protocol, ConsensusEducation Details 
January 2014 M.Tech Computer Engineering Jaipur, Rajasthan Malaviya National Institute Of Technology Jaipur
January 2011 B.E. Computer Science And Engg Kolhapur, Maharashtra Shivaji UniversityBlockchain EngineerBlockchain Engineer - XINFIN OrgnizationSkill Details 
MONGODB- Exprience - 16 months
CONTRACTS- Exprience - 12 months
MYSQL- Exprience - 9 months
AWS- Exprience - 6 months
PROBLEM SOLVING- Exprience - 6 monthsCompany Details 
company - XINFIN Orgnization
description - Xinfin is a global open source Hybrid Blockchain protocol.
Rolled out multiple blockchain based pilot projects on different use cases for various clients. Eg.
Tradefinex (Supply chain Management), Land Registry (Govt of MH), inFactor (Invoice Factoring)
Build a secure and scalable hosted wallet based on ERC 20 standards for XINFIN Network.
Working on production level blockchain use cases.
Technology: Ethereum Blockchain, Solidity, Smart Contracts, DAPPs, Nodejs
company - ORO Wealth
description - OroWealth is a zero commision online investment platform, currently focused on direct mutual funds
Build various scalable web based products (B2B and B2C) based on MEAN stack technology and integrated  with multiple finance applications/entities. eg. Integration KYC and MF Entities.
Technology: Node.js, Angular.js, MongoDB, Express
company - YallaSpree
description - Hyderabad, Telangana
Yallaspree is a largest digital shopping directory in U.A.E with over 22K stores.
Own the responsibility to develop and maintain following modules:
- Admin and Vendor interface       - Database operations
- Writing Webservices                      - Complete Notification system
- Events  and Offers Page
Technology: CakePHP (PHP Framework), JQuery, MySql
company - RailTiffin.com
description - Mumbai, Maharashtra
RailTiffin.com is an e-commerce platform to serve food to railway passengers.
Worked on multiple roles like bug fixing, DB operations, Feature customisation and writing API endpoints.
Technology: OpenCart (Ecommerce Framework), JQuery, MySql
company - Accolite Software India Private Limited
description - Bengaluru, KA
Accolite is a global IT Services company headquartered in Dallas, USA with offices in India.
Worked on Birst Analytics Tool to develop, deploy and maintain reports"
Blockchain,"KEY SKILLS: Programing languages: C, C++, Python, Apex, Visualforce, Database: Mysql, Framework: Django, Technologies: Salesforce.com (Administration and development), Force.com, Salesforce CRM, Blockchain Website Designing: HTML5, CSS3, Project: DIPLOMA: Project Name: VANET-virtual Ad - Hoc Network Technology Used: Java. About Project: A system has been made for Video-streaming. In this project, we give the Real, time video-steaming on two or more devices using VANET AP DEGREE: Project Name: Drowsiness Detection System Technology Used: python, Raspberry pi. About project: A system has been made for Driver safety when parson driving car. It detects Drowsy condition of a parson then system brings the alarm.Education Details 
January 2017 B.E Computer Engineering Pune, Maharashtra Sanghavi College of Engineering
January 2014 Diploma Computer Technology Nashik, Maharashtra Shree Mahavir Polytechnic
January 2010 S.S.C.  Nashik, Maharashtra C.D.O.Meri High schoolBlockChain DeveloperSkill Details 
AP- Exprience - 6 months
APEX- Exprience - 6 months
C++- Exprience - 6 months
CRM- Exprience - 6 months
CUSTOMER RELATIONSHIP MANAGEMENT- Exprience - 6 monthsCompany Details 
company - 
description - Job Description: Managing the mining firm,
Creating the smart contract on Waves and Ethereum both
Platform"
Blockchain,"SOFTWARE SKILLS: Languages: C, C++ & java Operating Systems: Windows XP, 7, Ubuntu RDBMS: Oracle (SQL) Database, My SQL, PostgreSQL Markup & Scripting: HTML, JavaScript & PHP, CSS, JQuery, Angular js. Framework: Struts, Hibernate, spring, MVC Web Server: Tomcat and Glassfish. Web Services: REST AND SOAP TRAINING DETAIL Duration: 4 months From: - United Telecommunication Limited Jharnet project (Place - Ranchi, Jharkhand) Networking Requirements: Elementary configuration of router and switch, IP and MAC addressing, Lease Line, OSI Layers, Routing protocols. Status: - Network Designer.Education Details 
    2 High School
 Diploma Government Women Ranchi, Jharkhand The InstitutionBlockchain EngineerBlockchain Engineer - AuxledgerSkill Details 
JAVA- Exprience - 19 months
CSS- Exprience - 12 months
HTML- Exprience - 12 months
JAVASCRIPT- Exprience - 12 months
C++- Exprience - 6 monthsCompany Details 
company - Auxledger
description - Worked with on lots of product on blockchain.

â¢ Bitcoin: Build Wallet and explorer on Bitcoin
â¢ Ethereum: Build Wallet and explorer on ethereum blockchain.
â¢ Customize product on Ethereum: Inventory system (Build smart contract in solidity,
deployed in java byte code and on ethereum as well and I have written API in java spring on that and then build front end and called all Api)
â¢ Audit Logger: I have audit logger for OTC exchange to keep all transaction record in blockchain.
â¢ DOC Safe on ethereum: I have build an ethereum application to keep Documents safe on blockchain and document in encrypted form on server.
â¢ And explore with Litecoin, Ripple & exchange (OTC P2P) Hyperledger Fabric   ..continue   ..
company - 
description - Worked with a USA team on blockchain on ethereum, I have designed product on ethereum
blockchain,
â¢ Setup private ethereum and bitcoin blockchain. Worked on loyalty program system and HER
System on ethereum network.
company - ERP System, CRM for Real Estate Company
description - â¢ At Lavisa Infrastructure Bangalore                                        Sep 2015- Oct 2016
Software developer
â¢ ERP System, CRM for Real Estate Company.
company - News Portal
description - â¢ On demand product development from client side requirement. Like
â¢ Dynamic website: Content management system where I have designed front end with backend where content of website was manageable from admin panel.
â¢ News Portal: News portal where content was in Hindi language. I have used Html, Css,
JavaScript, JDBC, MySQL data base.
â¢ Birthday Reminder: A small web application for birthday reminder, I have used HTMl, CSS,
JavaScript, JDBC, MySQL DB.
â¢ Car parking System: A web application for Management of Car Parking System, I have used
HTMl, CSS, JavaScript, JDBC, MySQL DB.
company - Company portal for employee management for Inside Company
description - â¢ At United Telecom Limited Ranchi                                         Nov 2013-Sep 2014
Web developer
â¢ Company portal for employee management for Inside Company. Onsite employee, & in different-different district. And management of all kind of government service like adhar
card, Birth certificate, pan card tracker etc.

Technology skill:

Technology: Blockchain (Bitcoin, Ethereum, Ripple, Hyperledger Fabric)
Block-chain: Private setup of blockchain, Node building.
Smart Contract: Solidity Language.
Smart Contract Api: Java Spring
Dapp Building: Node js, React js, Express js"
Blockchain,"SKILLS Bitcoin, Ethereum Solidity Hyperledger, Beginner Go, Beginner R3 Corda, Beginner Tendermint, Nodejs, C Programming, Java, Machine Learning specilaized in Brain Computer Interface, Computer Networking and Server Admin, Computer Vision, Data Analytics, Cloud Computing, Reactjs, angularEducation Details 
January 2014 to January 2018 Bachelor of Engineering Computer Science & Engineering  Thakur College of Engineering and Technology
September 2016 to March 2017  Dynamic Blood Bank System Mumbai, Maharashtra IIT
January 2014 CBSE   Senior Secondary
January 2011 CBSE Banking  VIDYASHRAM PUBLIC SCHOOLBlockchain DeveloperBlockchain Developer - Zhypility TechnologiesSkill Details 
NETWORKING- Exprience - 27 months
DATA ANALYTICS- Exprience - 11 months
COMPUTER VISION- Exprience - 6 months
JAVA- Exprience - 6 months
MACHINE LEARNING- Exprience - 6 monthsCompany Details 
company - Zhypility Technologies
description - une 2018
company - Area Business Owner Amway Enterprise Limited
description - Business Strategizing Promotion, Analytics and Networking Terms
company - Virtual
description - Developing Prototype of Smart India Hackthon to deployment level.
3.Networking And Switch Intern Bharti Airtel Private Limited (Mumbai)
company - 1.International Research Scholar- University Of Rome, Tor Vergata (Rome)
description - Nov 2017 - Nov 2017
Has done research on Reality Based Brain computer Interface and proposed paper in International Journal of Advanced Research (IJAR-20656) accepted paper by reviewer and Smart Kisan -Revolutionizing Country -IJSRD accepted for publication
company - 
description - under Reliance Jio (Mumbai) Dec 2017 - Jan 2017
company - Maharastra State Government Hackthon
description - 
company - Virtual
description - I was handling group of Interns in the marketing and sales team of nearby to promote on all social media platform the nearby products.
company - Promotion And Stock Marketing Drums Foods International
description - 
company - 8.Data Science And Web Analytics POSITRON INTERNET (Virtual)
description - 
company - 
description - I was making people aware about women equality rights and raise voice against violence through various modes of events and other sources of media to help the society.
company - IIT Bombay And IIT KGP Startup
description - 
company - IIT Bombay And IIT KGP Startup
description - "
Testing,"â¢ Good logical and analytical skills â¢ Positive attitude towards solving problems and accepting challenges â¢ A team player & leader â¢ A good organizer PRESONAL DETAILS: DOB: 20/07/1995. Marital status: Single.Education Details 
July 2015 to June 2018 BE Electrical Pune, Maharashtra Sinhagad institute of technology
January 2012 DIPLOMA MSBTE Kopargaon, MAHARASHTRA, IN K.B.P.Polytechnic
January 2010 SSC S.G.Vidyalaya  state boardTesting EngineerElectrical engineerSkill Details 
Company Details 
company - Intelux Electronics Pvt Ltd
description - 1. Power management system Testing department"
Testing,"COMPUTER PROFICIENCY â¢ Basic: MS-Office (PowerPoint, word, Outlook, Excel) â¢ Language Known: Basics of C, CPP, Java. â¢ Basics of Networking â¢ Basics command of Linux PROJECT DETAILS Minor Project Details: â¢ Title: Applocker for Android. â¢ Project Area: Android Application. â¢ Description: Applocker provides the protection of the System applications as well as the Third party applications installed in the Android devices. The password protection is provided with the help of patterns of volume keys. Hence, an extra measure of privacy is acquired. Major Project Details: â¢ Title: Online Complaint System For Cyber Crimes. â¢ Project Area: Android Application â¢ Description: Online Complaint System for Cyber Crimes is an android application which will be in use after a person lodged a complaint in Police station regarding cyber crime but no action has been taken against it within the prescribed time constraint. Such person will directly use this application which will help him/her to directly lodge the complaint to Commissioner Office and it will get store in the Commissioner's database and necessary action will be taken against it. STRENGTHS â¢ Belief in team work both as a team member and a leader. â¢ Hard and ethical worker.Education Details 
January 2013 to January 2016 B.E. Yeshwantrao Chavan Nagpur, Maharashtra Nagpur University
 Diploma Aggregate  Maharashtra State
 S.S.C. Education Nagpur, Maharashtra Maharashtra StateTesting engineerSkill Details 
ANDROID- Exprience - 6 months
CPP- Exprience - 6 months
DATABASE- Exprience - 6 months
EXCEL- Exprience - 6 months
JAVA- Exprience - 6 months
Selenium- Exprience - 12 months
Automation Testing- Exprience - 12 months
Selenium Webdriver- Exprience - 12 months
Manual Testing- Exprience - 6 months
Regression Testing- Exprience - 6 monthsCompany Details 
company - Maxgen technologies
description - I'm a software test engineer working at Maxgen technologies from past 1 year."
Testing,"Computer Skills: â¢ Proficient in MS office (Word, Basic Excel, Power point) Strength: â¢ Hard working, Loyalty & Creativity â¢ Self-motivated, Responsible & Initiative â¢ Good people management skill & positive attitude. â¢ knowledge of windows, Internet.Education Details 
 Bachelor of Electrical Engineering Electrical Engineering Nashik, Maharashtra Guru Gobind Singh College of Engineering and Research Centre
 Diploma Electrical Engineering Nashik, Maharashtra S. M. E. S. Polytechnic CollegeTesting EngineerSkill Details 
EXCEL- Exprience - 6 months
MS OFFICE- Exprience - 6 months
WORD- Exprience - 6 monthsCompany Details 
company - 
description - Department: Testing

Responsibilities: â¢ To check ACB and VCB of  Circuit Breaker.
â¢ Following test conducted of Circuit Breaker as per drawing.
1. To check breaker timing.
2. To check contact resistance using contact resistance meter (CRM) 3. To check breaker insulation resistance (IR) 4. To check breaker rack out and rack in properly or not.
5. To check closing and tripping operation work properly or not.
â¢ To check and following test conducted in MCC & PCC panel.
1. Insulation Resistance (IR) test.
2. Contact Resistance (CRM) test.
3. To check connection on mcc & pcc panel as per drawing.
â¢ To check and following test conducted in transformer.
1.  Insulation Resistance (IR) test.
2.  Transformer Ratio test.
3. Transformer Vector Group test.
4. Magnetic Balance test.
5. Magnetic Current test.
6. To check the transformer tapping remotely as well as manually 7. To check the all alarm and tripping protection command work properly
Or not as per circuit diagram.
 â¢ To check and test conducted in HV cables.
1. Hi-Pot test.
2. Insulation resistance (IR) test.
â¢ To check the LV cables using megger (IR Test) â¢ To check the relay connections as per circuit diagram.
Create the defects list which arising during the testing and try to find the solution to minimize the problem.
company - TRANS POWER SOLUTIONS
description - Lake-Site CO-Op.Soc. Adi Shankaracharya Marg,
Opp. IIT Main Gate, Powai 400076."
Testing,"â Willingness to accept the challenges. â Positive thinking. â Good learner. â Team Player. DECLARATION: I hereby declare that the above mentioned information is correct up to my knowledge and I bear the responsibility for the correctness of the above mentioned particulars. Date: / / Name: Dongare Mandakini Murlidhar Signature: Education Details 
June 2015 Electronics and Telecommunication Engineering  Kolhapur, Maharashtra Shivaji University
June 2012  Education  Secondary and Higher Secondary
 B.E. Electronics and Telecommunication  Jaywant College of Engineering and ManagementTesting EngineerElectronics Engineer - Abacus Electronics Pvt LtdSkill Details 
Language - C, C++- Exprience - Less than 1 year months
Operating Systems- Windows 7-8/NT/XP- Exprience - Less than 1 year monthsCompany Details 
company - Abacus Electronics Pvt Ltd
description - Duties:
â Perform electronic system testing for acceptance, compliance, warranty and other types.
â Develop test plan and procedure for electronic systems.
â Maintain complete and accurate documentations for system testing.
â Analyze and troubleshoot test defects in a timely fashion.
â Write system assembly instructions and resolve assembly issues accurately.
â Work with Supervisors to plan and coordinate test activities.
â Evaluate system performance and suggest improvements.
â Understand and interpret drawings, schematics, technical manuals and instructions.
â Also performed Hardware testing, debugging of hardware PCBs.
â Follow company policies and safely regulations.
â Work with cross-functional teams to complete assigned job duties within deadlines.
â Recommend process improvements to enhance testing efficiency.
company - Minilec India Pvt Ltd , Pirangoot.
description - ï¶	Taking responsibility for the quality of a companyâs product.
ï¶	Working with the departmental manager, production staff and suppliers to ensure quality, they aim to minimize the cost of reworking or waste and maximize customer satisfaction with the product.
ï¶	To establish, implement and maintain quality management system to measure and control quality in the production process.
ï¶	Work with the aim that to eliminate the causes of quality issues and reduce the risk of failure."
Testing,"PERSONAL SKILLS â¢ Quick learner, â¢ Eagerness to learn new things, â¢ Competitive attitude, â¢ Good leadership qualities, â¢ Ability to deal with people diplomatically. PERSONAL DOSSIER Fathers Name: Dhanraj WaghEducation Details 
January 2012 to January 2016 Bachelors of Engineering Engineering Pune, Maharashtra Pune University
January 2012 Higher Secondary Certificate  Nashik, Maharashtra SND College of Engineering and Research Center
January 2010 Secondary School Certificate  Yeola, Maharashtra Swami Muktanand Jr. College
 HSC   Maharashtra State Board
 SSC   Maharashtra State Bard
 BE  Rajapur, Uttar Pradesh Madhyamik Vidya Mandir RajapurTesting and Quality Control EngineerTesting and Quality Control Engineer - M/S Rakesh Transformer Industries Pvt. LtdSkill Details 
Company Details 
company - M/S Rakesh Transformer Industries Pvt. Ltd
description - Responsibilities:
â¢     To conduct Routine test, Type Test (Temperature Rise Test), Special Test on Transformers as per IS & IEC up

to - 10 MVA / 33 KV Class & preparing of its test reports.

â¢     Routine tests, Type tests and Special tests as per IS 2026, IS 1180, IS 11171/IEC-60076- test

Manual/inspection plans of Power Transformers, Distribution Transformers, Inverter Duty, Converter Duty,

Motor Duty Transformer, Furnace Transformer, Auto Transformers, Dry Type (VPI & CRT) LT Transformers,

Special Transformers
â¢     Testing Remote Tap Changer Cubicle (RTCC) Panel with OLTC and its operation ( CTR, Esun MR.)

â¢     Functional Testing of Transformer Accessories ( BR, PRV, MOG, WTI, OTI and its Setting)

â¢     In case any failure in testing analyses the root cause and submit the CAPA

â¢     Assist the customer or their representative at the time of inspection.

â¢     In process testing of winding, core assembly and core coil assembly.

â¢     Handling 3rd party and Government Body's Inspection.

â¢     Preparation of Daily Internal Testing Reports and send to concern.

â¢     Support Certification Audits (ISO & BIS)

â¢     Calibration/validation of tools, instruments, Measuring equipment's

â¢     Suggest the client about proper maintenance of transformer
â¢     Doing all documentation and Maintain Records and Analyze the Test Results and Maintain Testing Ledger

â¢     In process Quality Inspection of Winding, Core assembly, Core Coil assembly as per Standard and as per

organizational Norms.

â¢     Pre Dispatch Quality Inspection of Complete Product.

â¢     Implementation of, WHY-WHY analysis.
company - M/S Silverline Electricals Pvt. Ltd
description - Routine tests, Type tests (Temperature Rise Test) and Special tests as per IS 2026, IS 1180/ test

Manual/inspection plans of Distribution Transformer, Power Transformer

â¢     Successfully handled Responsibilities of ERDA's testing.

â¢     On site commissioning of Transformer.

â¢     Assist the customer or their representative at the time of inspection.

â¢     In process testing of winding, core assembly and core coil assembly.

â¢     Successfully handling 3rd party and Government Body's Inspection

â¢     Preparation of Daily Internal Testing Reports and send to concern.

â¢     Suggest the client about proper maintenance of transformer"
Testing,"COMPUTER SKILLS & SOFTWARE KNOWLEDGE MS-Power Point, MS - Office, C, Protius (PCB Design), Multisim, Micro wind, Matlab, Keil, Latex, Basic I nternet Fundamentals, Software and Hardware Knowledge PROJECT DETAILS Diploma Project: Speed Control of DC Motor Using Heart Beats. Mini Project: Water Gardening System Using Solar Panel. Final Year BE Project: Iris Recognition system.Education Details 
January 2016 BE EDUCATION Pune, Maharashtra PUNE University
January 2010 SSC   Maharashtra BoardQuality EngineerQuality Engineer - Matrix TechnologiesSkill Details 
MATLAB- Exprience - 6 months
PCB- Exprience - 6 months
PCB DESIGN- Exprience - 6 monthsCompany Details 
company - Matrix Technologies
description - 
company - RB Electronics
description - "
Testing,"Skill Set OS Windows XP/7/8/8.1/10 Database MYSQL, sql server 2005, 2008 & 2012 Languages Core Java Web Technology HTML, CSS Testing Manual Testing, Database Testing Other Bug tracking and reporting, End user handling.Education Details 
January 2016 MCS  Pune, Maharashtra P.V.P College Pravaranagar
January 2011 HSC   A.K.Junior College
January 2009 SSC   A.K.MahavidyalyaJR TESTING ENGINEERJR TESTING ENGINEER - M-Tech Innovations LtdSkill Details 
TESTING- Exprience - 24 months
WINDOWS XP- Exprience - 24 months
CSS- Exprience - 6 months
DATABASE- Exprience - 6 months
DATABASE TESTING- Exprience - 6 monthsCompany Details 
company - M-Tech Innovations Ltd
description - Responsibilities â¢     Analyzing the Testing Requirements â¢     Preparing Test Plans, Test Scenarios â¢     Preparing Test Cases â¢     Executing the test cases â¢     Tracking bugs â¢     Coordinating developers in order to fix it â¢     Preparing test summary reports â¢     Reporting quality manager â¢     Attending meeting

Projects
Project Name   1.Web Based Time Attendance Application
Environment    Windows-7, Vista, Windows XP, Windows 8, Windows 10
Clients        MCCIA, Sapphire Park, Bramha Suncity, Wanless hospital
Role           Software Testing
Duration       6 months

Project Name   Android 1.Time Attendance Application   2.Vehicle Tracking System   3.Vistor Management System
Environment    Android Mobile, Android Tablet.
Clients        Vankan Security
Role           Application Testing
Duration       6 months

Project Name   1.Web Based Factory Automation Process   2.Web Based Annual Maintenance Charges
Environment    Windows-7, Vista, Windows XP, Windows 8, Windows 10
Role           Software Testing
Duration       6 months

Project Name   Web Based Library Management System
Environment    Windows-7, Vista, Windows XP, Windows 8, Windows 10
Role           Software Testing
Duration       6 months"
