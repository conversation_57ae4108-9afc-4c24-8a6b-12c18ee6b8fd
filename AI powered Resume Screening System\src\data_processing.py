# -*- coding: utf-8 -*-
"""Data Processing.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/1Oee_JhD_31qO1bWNHKK-K9NnbTIPC8-T
"""

#Importing the neessary module. Other modules will be imported as per demand for the work.
import numpy as np
import pandas as pd

"""**bold text**"""

df = pd.read_csv('/content/*/Resume.csv')

df.head()

print(df.isnull().sum())

print(f"Dataset contains {df.shape[0]} resumes and {df.shape[1]} coloumns")

df = df.drop(columns=['Resume_html'])

df.head()

"""## **REGex module doubts  go here:-https://www.w3schools.com/python/python_regex.asp**"""

import re
# rEGEX OR RE IS A MODULE IN PYTHON WHICH DEALS, SEARCH AND REPLACE FUCNTION LIKE IN MCROSFT OFFICE WORD. iT CAN SEARCH USING words , that start with, to find all words, to split a word and sub or replace a word.

def clean_text(text): # a user defined fucntion to clean text which can be resued and helps to recall data in an easy way
  text = text.lower() # 1st tep converting all text to lower case.re. sun fnction works liek this . re.sub(pattern(regex pattern to find), replacement(what to replace with ), (text whats the string to process))
  text = re.sub(r'\n+',' ',text) #\n refers to next line to be deleted which has more occurence , where extra lime can be deleted.
  text = re.sub(r'[^a-zA-Z0-9\s]','',text) # ^ means not or negation [] means whats inside.since inside a-z &A-Z &0-9 those will be conserved . while subtituting everything else with ''. r before makes sure \s or \n the '\' in thse commands is considered as special character.
  text = re.sub(r'\s+',' ',text) # removes extra spaces. \s returns extra spaces. replaces extrspaces with one space(' ') using sub function. + sign refers to if the extra spaces is doubted to occur more than oen time.

import pandas as pd

df['Clean_Resume'] = df['Resume_str'].apply(clean_text) # clean resume has resuem _Str whcih has been cleaned by clean_text fucntion.
df[['Resume_str','Clean_Resume']].head()

"""Module is a .py file with collection functions and global variables.**e,g, demo_module, random ,re, html.**
Packages is a collection of modules with a intepreter file. **numpy , pandas are all packages.**
Library is a chunk of code or collection of packages that can be used to perform fucntions without repeating. eg. **matplotlib,pytorch etc..**
https://learnpython.com/blog/python-modules-packages-libraries-frameworks/

Module = Your fingers

Package= Your hands

Library = Building your home

Framework = Buying a home **e.g DJANGO , FLASK **

difference between frameworka nd library is that when I build a library i control when the package flow and application, when and where ot acess and control the library. in a framework its flo wis managed by itself.
"""

import spacy # a nlp processing LIBRARY helps in tokenisation (helps hide senstive info ith non sensitive characters.)
import nltk # like spacy ntlk is also a a library for natural language processing . only difference is ntlk is desiggned for research and spacy for perfomance.
from nltk.corpus import stopwords

#Work starts.....
nlp = spacy.load("en_core_web_sm") #Loads nlp models.This model is used for tokenization, part-of-speech tagging, dependency parsing, and lemmatization.
nltk.download("stopwords") #Stopwords are words that are filtered out (e.g., “a,” “the,” “and”) because they don’t contribute much meaning.
stop_words = set(stopwords.words("english")) # storing them as a set for fats lookup .

"""In the abbove step we split the resuem into words which can be processed. so the pre rocessing fucntion is as follows."""

#lets try a sample for pratice
sample = df["Resume_str"].iloc[0]
sample_doc = nlp(sample.lower())
print("Toekans are :",[token.text for token in sample_doc])
print(f"Lemmas are :",[token.lemma_ for token in sample_doc] )

#lets to do it with function
sample_run = preprocessing(sample)

def preprocessing(text): # creation of pre process fucntion
  if not isinstance(text,str): # isinstance is a fucntion in python which return for type of object. in this case isinstance(text(object),str(type of object))
    print(f"Skipping a non-string value : {text}")
    return ""
  doc = nlp(text.lower()) # lower all text cases.
  tokens = [token.lemma_ for token in doc if token.is_alpha and token.text not in stop_words] #token.lemma converts words to base form ."for token in doc" calls each word in doc(which is the lowercase text), then token.is_alpha Returns True if the token consists only of alphabetic characters (A-Z or a-z). and token.text in stopwords removes common words such as than , the etc..

  processed_text = " ".join(tokens) # joins the tokens
  print(f"Processed text: {processed_text[:100]}") # print fitst 100 words.
  return processed_text

"""Here the fucntion works liek this , it checks the list for all text , if the text is not a string it ll skip and and it will print skipping the non instrang value which is "non -string value". then it eill turn the text into lower case. then token . lemma converts words to bae form such running ot run , managed to manage . etcc. rest of the line of code removes the commond words such as the, than etcc then token.is_alpha converts the words to token .
then joins these token and ends the funtion and processed_text has all the joined token

"""

# Applying the fucntion
df["Clean_Resume"]= df["Resume_str"].apply(preprocessing)

print(df[["Resume_str","Clean_Resume"]].head())

df = df.drop(columns=["Clean_Resume"])

df.head()

